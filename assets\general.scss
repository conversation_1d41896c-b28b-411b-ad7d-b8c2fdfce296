@import "~/assets/variables.scss";
.theme--dark {
    @import "dark";
}
.theme--light {
    @import "light";
}

body {
    background-color: var(--v-background-base);
}
.muted-1 {
    opacity: 0.9;
}
.muted-2 {
    opacity: 0.8;
}
.muted-3 {
    opacity: 0.7;
}
.muted-4 {
    opacity: 0.6;
}
@for $i from 1 through 3 {
    .line-clamp-#{$i} {
        display: -webkit-box !important;
        -webkit-line-clamp: #{$i};
        -webkit-box-orient: vertical;
        overflow: hidden;
        padding-bottom: 0 !important;

        /* These are technically the same, but use both */
        overflow-wrap: break-word;
        word-wrap: break-word;
        -ms-word-break: break-all;

        /* This is the dangerous one in WebKit, as it breaks things wherever */
        word-break: break-all;

        /* Instead use this non-standard one: */
        word-break: break-word;

        /* Adds a hyphen where the word breaks, if supported (No Blink) */
        -ms-hyphens: auto;
        -moz-hyphens: auto;
        -webkit-hyphens: auto;
        hyphens: auto;
    }
}
// .overlay {
//     background-color: rgba(46, 52, 56, 0.5);
//     width: 100%;
//     height: 100%;
//     position: absolute;
//     padding: 1.25rem;
//     top: 0;
//     bottom: 0;
// }
.colored-bg {
    position: relative;
    border-color: currentColor !important;
    &:before {
        background-color: currentColor !important;
        bottom: 0;
        content: "";
        left: 0;
        pointer-events: none;
        position: absolute;
        right: 0;
        top: 0;
        transition: 0.3s;
        opacity: 0.04 !important;
        border-radius: inherit !important;
    }
    &.clickable:hover:before,
    &.clicked:before {
        opacity: 0.12 !important;
    }
}
.theme--dark .colored-bg {
    &:before {
        opacity: 0.2 !important;
    }
    &.clickable:hover:before,
    &.clicked:before {
        opacity: 0.28 !important;
    }
}

.inherit-border {
    &:before {
        border-radius: inherit !important;
    }
}
.inherit {
    background-color: inherit !important;
}
.inherit--text {
    color: inherit !important;
}
.curser-pointer {
    cursor: pointer !important;
}
.curser-help {
    cursor: help;
}
.fill-width {
    width: 100% !important;
}
.relative {
    position: relative;
}
.absolute {
    position: absolute;
}
.fixed {
    position: fixed;
}
.sticky {
    position: sticky;
}
.top {
    top: 0;
}
.bottom {
    bottom: 0;
}
.left {
    left: 0;
}
.right {
    right: 0;
}
.v-application--is-ltr {
    .start {
        left: 0;
    }
    .end {
        right: 0;
    }
}
.v-application--is-rtl {
    .start {
        right: 0;
    }
    .end {
        left: 0;
    }
}
.pulse {
    border-radius: 50%;
    // margin: 10px;
    // height: 20px;
    // width: 20px;
}
.pulse.success {
    background: rgb(76 175 80);
    box-shadow: 0 0 0 0 rgb(76 175 80);
    animation: pulse-success 2s infinite;
}

@keyframes pulse-success {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(106, 214, 110, 0.7);
    }

    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgb(76 175 80/0%);
    }

    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgb(76 175 80/0%);
    }
}
.pulse.error {
    background: rgb(255, 82, 82);
    box-shadow: 0 0 0 0 rgb(255 82 82);
    box-shadow: 0 0 0 0 rgb(255 82 82);
    animation: pulse-error 2s infinite;
}

@keyframes pulse-error {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(255, 82, 82, 0.7);
    }

    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgb(255 82 82/0%);
    }

    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgb(255 82 82/0%);
    }
}
.list {
    position: relative;
}
.list-move {
    transition: transform 0.5s;
}
.list-leave-active {
    position: absolute;
}
.list-item {
    transition: all 1s;
    display: inline-block;
    margin-right: 10px;
}
.list-enter, .list-leave-to
  /* .list-complete-leave-active below version 2.1.8 */ {
    opacity: 0;
    transform: translateY(30px);
}
.v-btn--active.no-active:not(:hover)::before,
.v-list-item--active.no-active:not(:hover)::before {
    opacity: 0 !important;
}
// .v-overlay.v-overlay--active {
//     backdrop-filter: blur(2px);
//     filter: blur(2px);
// }
.fixed-numeric {
    font-variant-numeric: tabular-nums;
}
.all-transition {
    transition: all 0.3s cubic-bezier(0.075, 0.82, 0.165, 1);
}
.v-tooltip__content {
    backdrop-filter: blur(10px);
}
.v-btn__content {
    line-height: 1 !important;
}
.theme--dark .v-overlay.v-overlay--active .v-overlay__scrim {
    opacity: 0.9 !important;
}

.text-shadow {
    text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.8);
}
.v-application {
    input:-webkit-autofill,
    input:-webkit-autofill:hover,
    input:-webkit-autofill:focus,
    input:-webkit-autofill:active {
        transition: background-color 9999s ease-in-out 0s;
    }
}
.no-wrap {
    white-space: nowrap !important;
    &.row {
        flex-wrap: nowrap !important;
    }
}

label:not(.v-label) {
    font-weight: 500;
    font-size: 14px;
    display: inline-block;
    margin-bottom: 4px;
}

section {
    padding-bottom: 18px;
}
section ~ section {
    padding-top: 18px;
}
button.v-btn:not(.v-btn--icon) ~ button.v-btn:not(.v-btn--icon) {
    @include dir("margin", "0 0 0 8px", "0 8px 0 0");
}
.no-padding {
    .v-toolbar__content {
        padding: 0 !important;
    }
}
// for loop for z-index from 1 to 10
@for $i from 1 through 10 {
    .z-#{$i} {
        z-index: $i !important;
    }
}