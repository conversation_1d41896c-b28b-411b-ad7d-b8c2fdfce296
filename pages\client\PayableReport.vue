<template>
	<page title="Payable Report" desc="">
		<div>
			<data-table v-if="checkPermission('reports.payables')" ref="dataTable" :columns="columns" api="/v1/portal/reports/payables">
				<!-- <template #filter="{ models }">
					<vc-autocomplete
						key="client_id"
						v-model="models.client_id"
						api="/v1/lookups/clients"
						label="Client"
						name="client_id"
						item-text="text"
						item-value="value"
					>
					</vc-autocomplete>

					<vc-autocomplete
						key="country_id"
						v-model="models.country_id"
						api="/v1/lookups/countries"
						label="Country"
						name="countries"
						item-text="text"
						item-value="value"
					>
					</vc-autocomplete>

					<vc-autocomplete
						key="product_category_id"
						v-model="models.product_category_id"
						api="/v1/lookups/product-categories"
						label="Product Category"
						name="product_category_id"
						item-text="text"
						item-value="value"
					>
					</vc-autocomplete>
				</template> -->
				<template #item.invoice_date="{ item }">
					{{ item.invoice_date | dateTime }}
				</template>
				<template #item.estimated_arrival_date="{ item }">
					{{ item.estimated_arrival_date | dateTime }}
				</template>
				<template #item.actual_shipment_date="{ item }">
					{{ item.actual_shipment_date | dateTime }}
				</template>
			</data-table>
			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "Invoice Id",
					align: "start",
					sortable: false,
					value: "invoice_id",
				},
				{
					text: "Invoice Number",
					sortable: true,
					value: "invoice_number",
				},
				{
					text: "Invoice Amount",
					sortable: true,
					value: "invoice_amount",
				},
				{
					text: "Invoice Date",
					sortable: true,
					value: "invoice_date",
				},
				{
					text: "Is Acknowledged",
					sortable: true,
					value: "is_acknowledged",
				},
				{
					text: "Estimated arrival date",
					sortable: true,
					value: "estimated_arrival_date",
				},
				{
					text: "Actual shipment date",
					sortable: true,
					value: "actual_shipment_date",
				},
				{
					text: "Payment Terms",
					sortable: true,
					value: "payment_terms",
				},
				{
					text: "Factory Name",
					sortable: true,
					value: "factory_name",
				},
			],
		};
	},
	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

