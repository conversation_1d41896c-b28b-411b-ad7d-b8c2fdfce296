<template>
	<div>
		<v-menu offset-y bottom left>
			<template #activator="{ on, attrs }">
				<v-btn color="gray" icon class="me-2" v-bind="attrs" v-on="on">
					<v-badge :value="unreadCount ? true : false" :content="unreadCount" color="#f60002" overlap>
						<v-icon size="30"> mdi-bell </v-icon>
					</v-badge>
				</v-btn>
			</template>

			<v-list two-line max-width="350" class="notifications">
				<v-list-item-group>
					<template v-for="(item, index) in notifications">
						<v-list-item :key="item.id" class="unread" @click="notificationLinkHandler(item)">
							<v-list-item-content @click="makeRead(item.id)">
								<v-list-item-title>
									{{ item.data?.title }}
								</v-list-item-title>

								<v-list-item-subtitle class="text--primary">
									{{ item.data?.body }}
								</v-list-item-subtitle>
							</v-list-item-content>

							<v-list-item-action>
								<v-list-item-action-text> {{ item.created_at | date }}</v-list-item-action-text>
							</v-list-item-action>
						</v-list-item>

						<v-divider v-if="index < notifications.length - 1" :key="index"></v-divider>
					</template>

					<v-list-item
						v-if="countOfAllNotifications && unreadCount"
						:to="localePath({ name: 'my-notifications' })"
						dense
						two-line
						class="d-flex justify-end"
					>
						<span class="body-2">All Notifications </span>
					</v-list-item>
					<v-list-item
						v-else-if="countOfAllNotifications && !unreadCount"
						:to="localePath({ name: 'my-notifications' })"
						dense
						two-line
						class="d-flex justify-end"
					>
						<div>
							<div class="body-2">You have no unread notifications!</div>
							<div class="body-2 mt-2" style="color: #b71f23">View old Notifications!</div>
						</div>
					</v-list-item>
					<v-list-item v-else-if="!countOfAllNotifications" disabled>You have no notifications.</v-list-item>
				</v-list-item-group>
			</v-list>
		</v-menu>
	</div>
</template>

<script lang="js">

export default {
  name: "DefaultLayout",
  data() {
    return {
      notifications: [],
      unreadCount: null,
      intervalId: null,
      countOfAllNotifications: null
    }
  },

  mounted() {
   this.getNotifications();
   // Set interval to fetch notifications every 5 minutes
   this.intervalId = setInterval(this.getNotifications, 100000); // 300000ms = 5 minutes
  },

  beforeDestroy() {
    // Clear the interval when the component is destroyed
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  },


  methods: {
    notificationLinkHandler(item) {
        if (!item || !item.data || !item.data.url) {
            return '';
        }else{
          // return this.localePath({
          //   path: item.data?.url,
          // });
          setTimeout(() => {
            this.$router.replace(this.localePath({ path: item.data?.url }));
          } , 50);
          this.$router.replace('/');
        }
    },

    getNotifications() {
      this.$axios.$get(`v1/my/notifications?per_page=5&unread=true`).then((resp) => {
        this.notifications = resp.items
      })

      this.$axios.$get(`v1/my/notifications/count?unread=true`).then((resp) => {
         this.unreadCount = resp.count
     })

      this.$axios.$get(`v1/my/notifications/count?unread=false`).then((resp) => {
         this.countOfAllNotifications = resp.count
     })
    },

    makeRead(id) {
      this.$axios.$put(`/v1/my/notifications/${id}/mark-as-read`).then(() => {
        this.getNotifications()
      });
    }
}

}
</script>

<style>
.notifications .unread {
	background: #ff32320f;
}
</style>
