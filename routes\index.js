import Vue from "vue";
import Router from "vue-router";
import clientDashboard from "~/pages/client/index.vue";
Vue.use(Router);


export function createRouter(ssrContext, createDefaultRouter, routerOptions, $config, store) {
  const routes =
    [
      {
        path: "/",
        name: "index",
        component: clientDashboard,
      },
      {
        path: "/users-management",
        name: "users-management",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/UsersManagement").then((m) => m.default || m),
      },
      {
        path: "/clients",
        name: "clients",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/Clients").then((m) => m.default || m),
      },
      {
        path: "/client-profile/:id",
        name: "client-profile",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/ClientProfile").then((m) => m.default || m),
      },
      {
        path: "/countries",
        name: "countries",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/Countries").then((m) => m.default || m),
      },
      {
        path: "/cities",
        name: "cities",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/Cities").then((m) => m.default || m),
      },
      {
        path: "/currencies",
        name: "currencies",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/Currencies").then((m) => m.default || m),
      },
      {
        path: "/bank-accounts",
        name: "bank-accounts",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/BankAccounts").then((m) => m.default || m),
      },
      {
        path: "/delivery-methods",
        name: "delivery-methods",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/DeliveryMethods").then((m) => m.default || m),
      },
      {
        path: "/factories",
        name: "factories",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/Factories").then((m) => m.default || m),
      },
      {
        path: "/product-categories",
        name: "product-categories",
        component: () =>
          import(/* webpackChunkName: "accounts-type" */ "~/pages/client/Products/ProductCategories").then((m) => m.default || m),
      },
      {
        path: "/product-sub-categories/:category_id",
        name: "product-sub-categories",
        component: () =>
          import(/* webpackChunkName: "accounts-type" */ "~/pages/client/Products/SubCategories").then((m) => m.default || m),
      },
      {
        path: "/products/:sub_category_id",
        name: "products",
        component: () => import(/* webpackChunkName: "accounts-type" */ "~/pages/client/Products/ProductsList").then((m) => m.default || m),
      },
      {
        path: "/product-specifications",
        name: "product-specifications",
        component: () =>
          import(/* webpackChunkName: "users-management" */ "~/pages/client/Products/ProductSpecifications").then((m) => m.default || m),
      },
      {
        path: "/part-number",
        name: "part-number",
        component: () =>
          import(/* webpackChunkName: "users-management" */ "~/pages/client/Products/PartNumberComponents").then((m) => m.default || m),
      },
      {
        path: "/shipment-suppliers",
        name: "shipment-suppliers",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/ShipmentSuppliers").then((m) => m.default || m),
      },
      {
        path: "/freight-forwarders",
        name: "freight-forwarders",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/FreightForwarders").then((m) => m.default || m),
      },
      {
        path: "/payment-methods",
        name: "payment-methods",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/PaymentMethods").then((m) => m.default || m),
      },
      {
        path: "/data-migration",
        name: "data-migration",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/DataMigration").then((m) => m.default || m),
      },
      {
        path: "/branches",
        name: "branches",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/Branches").then((m) => m.default || m),
      },
      {
        path: "/quotes",
        name: "quotes",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/QuotesWithFilters").then((m) => m.default || m),
      },
      {
        path: "/assignments",
        name: "assignments",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/PendingTransaction").then((m) => m.default || m),
      },
      {
        path: "/quotes/:quote_id?",
        name: "new-quotes",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/NewQuote").then((m) => m.default || m),
      },
      {
        path: "/orders",
        name: "orders-list",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/Orders/List").then((m) => m.default || m),
      },
      {
        path: "/order/:quote_id?/:order_id?",
        name: "order-form",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/Orders/OrderForm").then((m) => m.default || m),
      },
      {
        path: "/show-order/:order_id",
        component: () => import(/* webpackChunkName: "Data Management" */ "~/pages/client/Orders/Index").then((m) => m.default || m),
        children: [
          {
            path: "/show-order-po/:order_id",
            name: "show-order-po",
            component: () =>
              import(/* webpackChunkName: "Data Management" */ "~/pages/client/Orders/ShowOrderPo").then((m) => m.default || m),
          },
          {
            path: "/show-order-pt/:order_id",
            name: "show-order-pt",
            component: () =>
              import(/* webpackChunkName: "Data Management" */ "~/pages/client/Orders/ShowOrderPt").then((m) => m.default || m),
          },
          {
            path: "/show-order-invoice/:order_id",
            name: "show-order-invoice",
            component: () =>
              import(/* webpackChunkName: "Data Management" */ "~/pages/client/Orders/ShowOrderInvoice").then((m) => m.default || m),
          },
          {
            path: "/show-order-proforma-invoice/:order_id",
            name: "show-order-proforma-invoice",
            component: () =>
              import(/* webpackChunkName: "Data Management" */ "~/pages/client/Orders/ShowOrderPInvoice").then((m) => m.default || m),
          },
          {
            path: "/show-order-acknowledgement/:order_id",
            name: "show-order-acknowledgement",
            component: () =>
              import(/* webpackChunkName: "Data Management" */ "~/pages/client/Orders/ShowOrderAcknowledgement").then(
                (m) => m.default || m
              ),
          },
        ],
      },
      {
        path: "/my/settings",
        name: "settings",
        component: () => import(/* webpackChunkName: "profile" */ "~/pages/common/Settings/index").then((m) => m.default || m),
        children: [
          {
            path: "/my/profile",
            name: "profile",
            component: () => import(/* webpackChunkName: "profile" */ "~/pages/common/Settings/Profile").then((m) => m.default || m),
          },
          {
            path: "/my/account-preferences",
            name: "account-preferences",
            component: () =>
              import(/* webpackChunkName: "profile" */ "~/pages/common/Settings/AccountPreferences").then((m) => m.default || m),
          },
        ],
      },
      {
        path: "/roles-and-permissions",
        name: "roles-and-permissions",
        component: () =>
          import(/* webpackChunkName: "users-management" */ "~/pages/client/RolesAndPermissions").then((m) => m.default || m),
      },
      {
        path: "/legalization-companies",
        name: "legalization-companies",
        component: () =>
          import(/* webpackChunkName: "users-management" */ "~/pages/client/LegalizationCompanies").then((m) => m.default || m),
      },

      // I need to add this for my laptop
      {
        path: "/insurance-companies",
        name: "insurance-companies",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/InsuranceCompanies").then((m) => m.default || m),
      },
      {
        path: "/activity-logs",
        name: "activity-logs",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/ActivityLogs").then((m) => m.default || m),
      },
      {
        path: "/general-sitting",
        name: "general-sitting",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/GeneralSitting").then((m) => m.default || m),
      },
      {
        path: "/my-notifications",
        name: "my-notifications",
        component: () => import(/* webpackChunkName: "users-management" */ "~/pages/client/MyNotifications").then((m) => m.default || m),
      },
      // Reports Routes
      {
        path: "/reports/sales-report",
        name: "sales-report",
        component: () => import(/* webpackChunkName: "reports" */ "~/pages/client/SalesReport").then((m) => m.default || m),
      },
      {
        path: "/reports/price-history",
        name: "price-history",
        component: () => import(/* webpackChunkName: "reports" */ "~/pages/client/PriceHistory").then((m) => m.default || m),
      },
      {
        path: "/reports/freight-report",
        name: "freight-report",
        component: () => import(/* webpackChunkName: "reports" */ "~/pages/client/FreightReport").then((m) => m.default || m),
      },
      {
        path: "/reports/receivables-report",
        name: "receivables-report",
        component: () => import(/* webpackChunkName: "reports" */ "~/pages/client/ReceivablesReport").then((m) => m.default || m),
      },
      {
        path: "/reports/payable-report",
        name: "payable-report",
        component: () => import(/* webpackChunkName: "reports" */ "~/pages/client/PayableReport").then((m) => m.default || m),
      },
      {
        path: "/reports/orders-report",
        name: "orders-report",
        component: () => import(/* webpackChunkName: "reports" */ "~/pages/client/OrdersReport").then((m) => m.default || m),
      },
      {
        path: "/reports/employee-performance-report",
        name: "employee-performance-report",
        component: () => import(/* webpackChunkName: "reports" */ "~/pages/client/employeePerformanceReport").then((m) => m.default || m),
      },
      {
        path: "/login",
        name: "login",
        component: () => import(/* webpackChunkName: "auth" */ "~/pages/common/auth/login").then((m) => m.default || m),
      },
      {
        path: "/logout",
        name: "logout",
        component: () => import(/* webpackChunkName: "auth" */ "~/pages/common/auth/logout").then((m) => m.default || m),
      },
    ]


	return new Router({
		mode: "history",
    routes
	});
}
