{
	// Place your areel-admin workspace snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and 
	// used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
	// $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
	// Placeholders with the same ids are connected.
	// Example:
	// "Print to console": {
	// 	"scope": "javascript,typescript",
	// 	"prefix": "log",
	// 	"body": [
	// 		"console.log('$1');",
	// 		"$2"
	// 	],
	// 	"description": "Log output to console"
	// }
	"new Portal Page": {
		"scope": "vue",
		"prefix": "Portal page",
		"body": [
			"<template>\r\n    <page :title=\"\\$t('.title')\" :desc=\"\\$t('.desc')\">\r\n        <template #actions>\r\n            <v-btn color=\"primary\" @click=\"\\$refs.crud.new()\"> <v-icon left>mdi-plus<\/v-icon>New Key<\/v-btn>\r\n        <\/template>\r\n        <div>\r\n            <data-table ref=\"dataTable\" :columns=\"columns\" :group-by=\"groupByModel || undefined\" api=\"\/v1\/portal\/\">\r\n                <!-- <template #prepend-action=\"{ models, filter }\">\r\n            \r\n                <\/template> -->\r\n                <template #filter=\"{ models, options }\">\r\n                    <v-select\r\n                        v-model=\"models.is_published\"\r\n                        label=\"Publish Status\"\r\n                        :items=\"options.is_published\"\r\n                        clearable\r\n                        @clear=\"models.is_published = undefined\"\r\n                    ><\/v-select>\r\n                <\/template>\r\n\r\n                <template #item.created_at=\"{ item }\">\r\n                    {{ item.created_at | dateTime }}\r\n                <\/template>\r\n                \r\n                <template #item.updated_at=\"{ item }\">\r\n                    {{ item.updated_at | dateTime }}\r\n                <\/template>\r\n        \r\n                <template #item.actions=\"{ item }\">\r\n                    <div class=\"d-flex\">\r\n                        <v-btn v-tooltip=\"\\$t('common.delete')\" small icon @click=\"\\$refs.crud.delete(item.id)\">\r\n                            <v-icon small>mdi-delete<\/v-icon>\r\n                        <\/v-btn>\r\n                        <v-btn v-tooltip=\"\\$t('common.edit')\" small icon @click=\"\\$refs.crud.edit(item.id)\">\r\n                            <v-icon small>mdi-pencil<\/v-icon>\r\n                        <\/v-btn>\r\n                    <\/div>\r\n                <\/template>\r\n            <\/data-table>\r\n            <crud\r\n                ref=\"crud\"\r\n                v-slot=\"{ item, isEdit }\"\r\n                width=\"1000\"\r\n                :default=\"defaultItem\"\r\n                item-name=\"ITEM\"\r\n                api=\"\/v1\/portal\/\"\r\n                @updated=\"refresh\"\r\n                @created=\"refresh\"\r\n                @deleted=\"refresh\"\r\n\r\n            >\r\n                {{ item }}\r\n                {{ isEdit }}\r\n            <\/crud>\r\n        <\/div>\r\n    <\/page>\r\n<\/template>\r\n\r\n<script>\r\nexport default {\r\n    data() {\r\n        return {\r\n            value: {},\r\n            columns: [\r\n                {\r\n                    text: \"#\",\r\n                    sortable: false,\r\n                    value: \"id\",\r\n                },\r\n            ],\r\n            defaultItem: {},\r\n        };\r\n    },\r\n\r\n    computed: {},\r\n    watch: {},\r\n\r\n    methods: {\r\n        refresh() {\r\n            this.\\$refs.dataTable.refresh();\r\n        },\r\n    },\r\n};\r\n<\/script>\r\n"
		]
	},
	"new Portal Page With Tabs": {
		"scope": "vue",
		"prefix": "Portal page with tabs",
		"body": [
			"<template>\r\n\t<page :tabs=\"tabs\" :title=\"\\$t('$1.title')\">\r\n\t\t<template #actions>\r\n\t\t\t<v-btn v-can:create color=\"primary\" depressed @click=\"newItem()\">\r\n\t\t\t\t<v-icon left>mdi-plus<\/v-icon>\r\n\t\t\t\t{{ \\$t(\"common.new\") }}<\/v-btn\r\n\t\t\t>\r\n\t\t<\/template>\r\n\t\t<nuxt-child ref=\"child\" \/>\r\n\t<\/page>\r\n<\/template>\r\n\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\ttabs: [\r\n\t\t\t\t{\r\n\t\t\t\t\ttext: this.\\$t(\"$2\"),\r\n\t\t\t\t\ticon: \"mdi-account-multiple\",\r\n\t\t\t\t\tto: this.localePath({ name: \"$3\", params: { type: \"$4\" } }),\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\ttext: this.\\$t(\"$5\"),\r\n\t\t\t\t\ticon: \"$6\",\r\n\t\t\t\t\tto: this.localePath({ name: \"$7\", params: { type: \"$8\" } }),\r\n\t\t\t\t},\r\n\t\t\t],\r\n\t\t};\r\n\t},\r\n\tmethods: {\r\n\t\tnewItem() {\r\n\t\t\tthis.\\$refs.child.\\$refs.crud.new();\r\n\t\t},\r\n\t},\r\n};\r\n<\/script>\r\n"
		]
	}
}
