<template>
	<v-container>
		<v-radio-group v-model="settings.theme" :label="$t('settings.theme')">
			<v-radio value="light" :label="$t('settings.light')"> </v-radio>
			<v-radio value="dark" :label="$t('settings.dark')"> </v-radio>
		</v-radio-group>
	</v-container>
</template>

<script>
export default {
	data() {
		return {
			settings: {
				theme: this.$vuetify.theme.dark ? "dark" : "light",
				displayLanguage: this.$i18n.locale,
			},
		};
	},
};
</script>
