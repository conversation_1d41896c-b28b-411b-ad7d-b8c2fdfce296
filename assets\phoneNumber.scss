.phone-number-container {
    direction: ltr;
    margin-bottom: 8px;
    position: relative;
    &.is-focused {
        label {
            color: var(--v-primary-base);
        }
    }
    .v-select__selections {
        flex-wrap: nowrap;
        direction: ltr;
    }
    .v-autocomplete.v-select input {
        min-width: 0;
    }
    &:hover > .v-input__control > .v-input__slot .v-input:not(.v-input--is-focused) .v-input__slot {
        fieldset {
            color: rgba(0, 0, 0, 0.86) !important;
        }
    }
    .code-field .v-input__slot {
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
        background-color: #fff !important;
        fieldset {
            // background-color: #fafafa;
        }
    }
    .number-field {
        direction: rtl;
        position: unset !important;
        .v-input__slot {
            border-top-left-radius: 0 !important;
            border-bottom-left-radius: 0 !important;
            background-color: #fff !important;
            padding: 0 !important;
            fieldset {
                border-left-width: 0 !important;

                legend {
                    margin-right: 24px; //icon width
                    margin-left: auto; //for firefox
                }
            }
            input {
                // text-align: left;
                direction: ltr;
            }
            label {
                right: -4px !important;
            }
        }
    }
}
.v-application--is-ltr .phone-number-container {
    .code-field .v-input__slot {
        fieldset {
            // background-color: #fafafa;
            border-right-width: 0 !important;
        }
    }
    .number-field {
        direction: ltr;
        .v-input__slot {
            border-top-left-radius: 0 !important;
            border-bottom-left-radius: 0 !important;
            background-color: #fff !important;
            fieldset {
                border-left-width: 0 !important;

                legend {
                    margin-left: 24px; //icon width
                    margin-right: auto; //for firefox
                }
            }
        }

        label {
            left: 0 !important;
        }
    }
}
.v-application--is-rtl .phone-number-container {
    .code-field .v-input__slot {
        fieldset {
            // background-color: #fafafa;
            border-right-width: 0 !important;
        }
    }
    .number-field {
        .v-input__slot {
            fieldset {
                legend {
                }
            }
        }

        label {
        }
    }
}
