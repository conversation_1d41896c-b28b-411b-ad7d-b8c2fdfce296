<template>
	<page title="Shipment Suppliers" desc="">
		<template #actions>
			<v-btn color="success" @click="$refs.crud.new()"> <v-icon left>mdi-plus</v-icon>Add Suppliers</v-btn>
		</template>
		<div>
			<data-table ref="dataTable"  :columns="columns" api="/v1/portal/shipment-suppliers">
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="'Delete'" small icon @click="$refs.crud.delete(item.id)">
							<v-icon small>mdi-delete</v-icon>
						</v-btn>
						<v-btn v-tooltip="'Edit'" small icon @click="$refs.crud.edit(item.id)">
							<v-icon small>mdi-pencil</v-icon>
						</v-btn>
					</div>
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>
			</data-table>

			<crud
				ref="crud"
				v-slot="{ item }"
				width="400"
				:default="defaultItem"
				item-name="Country"
				api="/v1/portal/shipment-suppliers"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
			<field-text-field
				v-model="item.name"
				name="name"
				label="Supplier Name"
				:rules="[$rules.required('Supplier name')]"
			></field-text-field>

			<field-text-field
				v-model="item.email"
				name="email"
				label="Supplier Email"
				:rules="[$rules.required('Supplier email')]"
			></field-text-field>

			<field-text-field
				v-model="item.phone"
				name="phone"
				label="Supplier phone"
				:rules="[$rules.required('Supplier phone')]"
			></field-text-field>

			<field-text-field
				v-model="item.address"
				name="address"
				label="Supplier Address"
				:rules="[$rules.required('Supplier address')]"
			></field-text-field>

			
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "Supplier Name",
					sortable: true,
					value: "name",
				},
				{
					text: "Supplier Email",
					sortable: true,
					value: "email",
				},
				{
					text: "Supplier Phone",
					sortable: true,
					value: "phone",
				},
				{
					text: "Supplier Address",
					sortable: true,
					value: "address",
				},
			    {
			    	text: "Created at",
			    	sortable: true,
			    	value: "created_at",
			    },
			   
				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],
			defaultItem: {
				id: null,
           		name: null,
			},
		};
	},
	computed: {},
	watch: {},
	
	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

