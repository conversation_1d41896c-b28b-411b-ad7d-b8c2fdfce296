<template>
	<page title="My Notifications" desc="">
		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/my/notifications">
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="'Show a Notification'" small icon @click="notificationLinkHandler(item)">
							<v-icon small>mdi-eye </v-icon>
						</v-btn>
					</div>
				</template>
				<template #item.title="{ item }">
					{{ item.data?.title }}
				</template>

				<template #item.message="{ item }">
					{{ item.data?.body }}
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>
			</data-table>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "Title",
					sortable: false,
					value: "title",
				},
				{
					text: "Content",
					sortable: false,
					value: "message",
				},
				{
					text: "Created At",
					sortable: false,
					value: "created_at",
				},
				{
					text: "",
					sortable: false,
					value: "actions",
				},
			],
		};
	},
	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},

		makeRead(id) {
			this.$axios.$put(`/v1/my/notifications/${id}/mark-as-read`);
		},

		notificationLinkHandler(item) {
			if (!item.read_at) {
				this.makeRead(item.id);
			}

			if (item.data?.url) {
				this.$router.push(this.localePath({ path: item.data?.url }));
			}
		},
	},
};
</script>

