<template>
	<page title="Users Management" desc="">
		<template #actions>
			<v-btn v-if="checkPermission('users.create')" color="success" @click="$refs.crud.new()">
				<v-icon left>mdi-plus</v-icon>Add User
			</v-btn>
		</template>
		<div>
			<data-table v-if="checkPermission('users.view')" ref="dataTable" :columns="columns" api="/v1/portal/users">
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-if="checkPermission('users.delete')" v-tooltip="'Delete'" small icon @click="$refs.crud.delete(item.id)">
							<v-icon small>mdi-delete</v-icon>
						</v-btn>
						<v-btn v-if="checkPermission('users.update')" v-tooltip="'Edit'" small icon @click="$refs.crud.edit(item.id)">
							<v-icon small>mdi-pencil</v-icon>
						</v-btn>
						<v-btn
							v-if="checkPermission('users.update')"
							v-tooltip="'Reset Password'"
							small
							icon
							@click="$refs.crudResetPasswoard.edit(item.id)"
						>
							<v-icon small>mdi-lock-reset</v-icon>
						</v-btn>
					</div>
				</template>

				<template #item.role="{ item }">
					{{ item.role?.name }}
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>
			</data-table>

			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>

			<crud
				ref="crud"
				v-slot="{ item, isEdit }"
				width="400"
				:default="defaultItem"
				item-name="User"
				api="/v1/portal/users"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<field-text-field v-model="item.name" name="name" label="Name" :rules="[$rules.required('name')]"></field-text-field>

				<field-text-field v-model="item.email" name="email" label="Email" :rules="[$rules.required('email')]"></field-text-field>

				<vc-select
					v-model="item.role_id"
					name="role_id"
					api="/v1/lookups/roles"
					item-text="text"
					item-value="value"
					label="Role"
					:rules="[$rules.required('role_id')]"
				></vc-select>

				<div v-if="!isEdit">
					<vc-password v-model="item.password" name="password" label="password"> </vc-password>
				</div>
			</crud>

			<crud
				ref="crudResetPasswoard"
				v-slot="{ item }"
				width="400"
				:default="defaultItemResetPasswoard"
				item-name="User"
				:put-api="putApiURLResetPasswoard"
				:get-api="false"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<vc-password v-model="item.password" name="password" label="New Password"> </vc-password>

				<vc-password v-model="item.password_confirmation" name="password_confirmation" label="Confirm Password"> </vc-password>
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			value: {},

			role: {
				name: null,
			},

			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "User Name",
					sortable: true,
					value: "name",
				},
				{
					text: "Email",
					sortable: true,
					value: "email",
				},
				{
					text: "Role",
					sortable: true,
					value: "role",
				},
				{
					text: "Created at",
					sortable: true,
					value: "created_at",
				},
				{
					text: "Updated at",
					sortable: true,
					value: "updated_at",
				},
				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],
			defaultItem: {
				id: null,
				name: null,
				email: null,
				role: null,
				notify_by_email: false,
				password: null,
			},
			defaultItemResetPasswoard: {
				password: null,
				password_confirmation: null,
			},
		};
	},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
		putApiURLResetPasswoard({ keyId }) {
			return `v1/portal/users/${keyId}/change-password`;
		},
	},
};
</script>

