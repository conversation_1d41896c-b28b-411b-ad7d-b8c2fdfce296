<template>
	<page title="Roles And Permissions" desc="">
		<template #actions>
			<v-btn v-if="checkPermission('permissions.create')" color="success" @click="permissionDialog(null, 'add')">
				<v-icon left>mdi-plus</v-icon>Add Roles
			</v-btn>
		</template>
		<div>
			<data-table v-if="checkPermission('permissions.view')" ref="dataTable" :columns="columns" api="/v1/portal/roles">
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-if="checkPermission('permissions.delete')" v-tooltip="'Delete'" small icon @click="deleteRole(item.id)">
							<v-icon small>mdi-delete</v-icon>
						</v-btn>

						<v-btn
							v-if="checkPermission('permissions.update')"
							v-tooltip="'Edit'"
							small
							icon
							@click="permissionDialog(item.id, 'edit')"
						>
							<v-icon small>mdi-eye</v-icon>
						</v-btn>
					</div>
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>
			</data-table>
			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>

			<v-dialog v-model="dialogPermissions" absolute width="700px" style="z-index: 9999; min-height: 300px">
				<v-card>
					<v-card-title>
						Permissions
						<v-spacer></v-spacer>
						<v-btn icon @click="dialogPermissions = false">
							<v-icon>mdi-close</v-icon>
						</v-btn>
					</v-card-title>
					<v-card-text class="pt-6">
						<v-text-field
							v-model="roleData.name"
							outlined
							name="name"
							label="Role Name"
							:rules="[$rules.required('name')]"
						></v-text-field>

						<v-expansion-panels>
							<v-expansion-panel v-for="(group, i) in allPermissions" :key="i" class="mb-1">
								<v-expansion-panel-header>
									<div class="d-flex align-center">
										<v-checkbox
											v-model="group.selected"
											hide-details
											class="mb-0 pb-0 mt-0 me-4"
											dense
											:label="group.group"
											@click.stop="toggleAll(group)"
										></v-checkbox>
									</div>
								</v-expansion-panel-header>
								<v-expansion-panel-content>
									<div v-for="(permission, k) in group.permissions" :key="k + 'items'">
										<v-checkbox
											v-model="permission.selected"
											dense
											hide-details
											:label="permission.description"
											@click.stop="updateSelection(permission)"
										></v-checkbox>
									</div>
								</v-expansion-panel-content>
							</v-expansion-panel>
						</v-expansion-panels>
					</v-card-text>
					<v-card-actions class="d-flex">
						<v-spacer></v-spacer>
						<v-btn color="success" @click="save()"> Save </v-btn>
					</v-card-actions>
				</v-card>
			</v-dialog>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			dialogPermissions: false,
			permissions: [],
			checked: [],

			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "Name",
					sortable: true,
					value: "name",
				},
				{
					text: "Users Count",
					sortable: true,
					value: "users_count",
				},

				{
					text: "Created at",
					sortable: true,
					value: "created_at",
				},
				{
					text: "Updated at",
					sortable: true,
					value: "updated_at",
				},
				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],

			actionType: null,
			allPermissions: [],
			itemId: null,
			roleData: {
				name: "",
				permissions: [],
			},
		};
	},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},

		permissionDialog(id, type) {
			if (type === "edit") {
				this.actionType = "edit";
				this.itemId = id;
			} else if (type === "add") {
				this.actionType = "add";
			}

			this.getAllPermissions();
		},

		getAllPermissions() {
			this.allPermissions = [];

			this.$axios
				.$get("/v1/lookups/permissions-list")
				.then((resp) => {
					this.allPermissions = resp;
					if (this.actionType === "edit") {
						this.getRoleById(this.itemId);
					}
					this.dialogPermissions = true;
				})
				.catch((error) => {
					console.error("Error fetching permissions:", error);
				});
		},

		getRoleById(id) {
			this.roleData = {};
			this.$axios.$get(`/v1/portal/roles/${id}?ids=permissions`).then((resp) => {
				this.roleData = resp;
				this.initializeSelectedPermissions();
			});
		},

		initializeSelectedPermissions() {
			this.allPermissions.forEach((group) => {
				group.permissions.forEach((permission) => {
					if (this.roleData.permissions.includes(permission.id)) {
						permission.selected = true; // Mark as selected
					}
				});
				// Check if any permissions in the group are selected
				group.selected = group.permissions.some((p) => p.selected);
			});
		},
		toggleAll(group) {
			const isSelected = group.selected;
			console.log(`Toggling group: ${group.group}, selected: ${isSelected}`);

			group.permissions.forEach((permission) => {
				console.log(`Setting permission ${permission.description} to ${isSelected}`);
				permission.selected = isSelected;
				this.updateSelection(permission);
			});

			// Force an update if UI doesn't reflect changes
			this.$forceUpdate();
		},

		updateSelection(permission) {
			if (permission.selected) {
				if (!this.roleData.permissions.includes(permission.id)) {
					this.roleData.permissions.push(permission.id);
				}
			} else {
				const index = this.roleData.permissions.indexOf(permission.id);
				if (index !== -1) {
					this.roleData.permissions.splice(index, 1);
				}
			}
			this.updateParentSelection(permission);
		},
		updateParentSelection(permission) {
			const group = this.allPermissions.find((g) => g.permissions.includes(permission));
			if (group) {
				group.selected = group.permissions.some((p) => p.selected);
			}
			this.$forceUpdate();
		},

		save() {
			if (this.actionType === "edit") {
				this.$axios.$put(`/v1/portal/roles/${this.itemId}`, this.roleData).then(() => {
					this.$toast.success("updated successfully");
					this.dialogPermissions = false;
					this.refresh();
				});
			} else {
				this.$axios.$post("/v1/portal/roles", this.roleData).then(() => {
					this.$toast.success("created successfully");
					this.dialogPermissions = false;
					this.refresh();
				});
			}
		},

		deleteRole(id) {
			this.$confirm(null, { title: "Are you sure to delete the item?" }).then((isAccepted) => {
				if (isAccepted) {
					this.$axios.$delete(`/v1/portal/roles/${id}`).then(() => {
						this.$toast.success("deleted successfully");
						this.refresh();
					});
				}
			});
		},
	},
};
</script>

