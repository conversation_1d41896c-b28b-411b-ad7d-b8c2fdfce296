<template>
	<v-card outlined class="mt-4">
		<v-card-text>
			<div id="formal-quote-pdf" style="background: #fff">
				<v-container fluid style="padding: 30px">
					<v-row>
						<v-col md="4">
							<v-img src="/logo.png" width="300px"></v-img>
						</v-col>
						<v-col md="4">
							<div class="headline text-center mt-4" style="align-items: center; text-align: center; margin-top: 10px">
								<span class="d-block" style="display: block">Quote</span>
								<span class="font-weight-bold" style="font-weight: bold">
									{{ quoteData.reference_number }}
								</span>
							</div>
						</v-col>
						<v-col md="4">
							<div class="text-end">
								<span class="d-block caption font-weight-bold">JAFZA ONE, Tower B</span>
								<span class="d-block caption font-weight-bold">13th Floor, Suite BB1302</span>
								<span class="d-block caption font-weight-bold">P.O Box 17046, JEBEL ALI</span>
								<span class="d-block caption font-weight-bold"> Dubai, United Arab of Emirates</span>
								<span class="d-block caption font-weight-bold">Tel: +971 (04) 268 4666</span>
							</div>
						</v-col>
					</v-row>

					<v-divider style="margin-top: 15px; margin-bottom: 15px" />

					<v-row class="relative">
						<v-col md="6" cols="12">
							<div>
								<div style="display: flex; align-items: center; margin-bottom: 4px">
									<span class="d-inline-block me-2" style="margin-right: 5px">Quote Date:</span>
									<h4 class="d-inline-block">{{ quoteData.created_at | date }}</h4>
								</div>

								<div style="display: flex; align-items: center; margin-bottom: 20px">
									<span class="d-inline-block me-2" style="margin-right: 5px">Valid Till:</span>
									<h4 class="d-inline-block">{{ quoteData.valid_until_date | date }}</h4>
								</div>

								<div style="margin-bottom: 6px" class="d-flex">
									<span class="me-2">Client Name:</span>
									<h4>{{ quoteData.client?.name }}</h4>
								</div>

								<div style="display: flex; align-items: center; margin-bottom: 6px">
									<span class="d-inline-block me-2">Client Country:</span>
									<h4>
										{{ quoteData.client?.country?.name }}
									</h4>
								</div>

								<div v-if="quoteData.client?.email" style="display: flex; align-items: center; margin-bottom: 6px">
									<span class="d-inline-block me-2">Email:</span>
									<h4 class="d-inline-block">
										{{ quoteData.client?.email }}
									</h4>
								</div>
							</div>
						</v-col>

						<v-col md="6" cols="12">
							<div style="display: flex; align-items: center; margin-bottom: 5px">
								<span class="d-inline-block me-2">Project:</span>

								<h4>{{ quoteData?.project_name }}</h4>
								<span class="mx-2">/</span>
								<h4>{{ quoteData?.product_category?.name }}</h4>
							</div>

							<div class="d-flex">
								<div v-for="contact in quoteData.contact_people_data" :key="contact.id">
									<div class="me-4">
										<div v-if="contact.name" style="display: flex; align-items: center; margin-bottom: 4px">
											<span class="d-inline-block" style="margin-right: 5px">Attention:</span>
											<h4 class="d-inline-block">{{ contact.name }}</h4>
										</div>

										<div>
											<span class="d-inline-block me-2" style="margin-right: 5px">Account:</span>
											<h4 class="d-inline-block">
												{{ contact.number }}
											</h4>
										</div>

										<div v-if="contact.email_one" style="display: flex; align-items: center; margin-bottom: 4px">
											<span class="d-inline-block me-2" style="margin-right: 5px">Email:</span>
											<h4 class="d-inline-block">
												{{ contact.email_one }}
											</h4>
										</div>

										<div v-if="contact.phone" style="display: flex; align-items: center; margin-bottom: 4px">
											<span class="d-inline-block me-2" style="margin-right: 5px">Phone:</span>
											<h4 class="d-inline-block">{{ contact.phone }}</h4>
										</div>

										<div v-if="contact.mobile" style="display: flex; align-items: center; margin-bottom: 4px">
											<span class="d-inline-block me-2" style="margin-right: 5px">Mobile:</span>
											<h4 class="d-inline-block">{{ contact.mobile }}</h4>
										</div>

										<div v-if="contact.fax" style="display: flex; align-items: center; margin-bottom: 4px">
											<span class="d-inline-block me-2" style="margin-right: 5px">Fax:</span>
											<h4 class="d-inline-block">{{ contact.fax }}</h4>
										</div>

										<div style="display: flex; align-items: center; margin-bottom: 6px">
											<span class="d-inline-block me-2">Address:</span>
											<h4 class="d-inline-block">
												{{ contact.address_one }}
											</h4>
										</div>

										<!-- <div style="display: flex; align-items: center; margin-bottom: 6px">
											<span class="d-inline-block me-2">Address 2:</span>
											<h4 class="d-inline-block">{{ contact.address_two }}</h4>
										</div> -->

										<div v-if="contact.position" style="display: flex; align-items: center; margin-bottom: 4px">
											<span class="d-inline-block me-2" style="margin-right: 5px">Position:</span>
											<h4 class="d-inline-block">
												{{ contact.position }}
											</h4>
										</div>
									</div>
								</div>
							</div>

							<div class="mt-6">
								<span class="d-inline-block me-2">Terms of Delivery:</span>
								<h4 class="d-inline-block mb-0 pb-0" v-html="quoteData.delivery_method_terms"></h4>
							</div>
							<div class="mt-0 pt-0">
								<span class="d-inline-block me-2">Method of Delivery:</span>
								<h4 class="d-inline-block">{{ quoteData.from_deliver_port?.delivery_method?.name }}</h4>
							</div>
						</v-col>

						<v-col cols="12" style="margin-top: 12px">
							<p class="body-2" style="margin-bottom: 10px">
								We are pleased to provide the following quote , Below is the pricing in response to your inquiry.
							</p>

							<v-card outlined>
								<v-card-text>
									<v-simple-table class="border">
										<thead style="background: #232323">
											<tr>
												<th class="text-left" style="color: #fff">#</th>
												<th class="text-left" style="color: #fff">Part # / Description</th>
												<th class="text-left" style="color: #fff">UOM</th>
												<th class="text-left" style="color: #fff">Quantity</th>
												<th class="text-left" style="color: #fff">Unit Price</th>
												<th class="text-left" style="color: #fff">Total</th>
											</tr>
										</thead>
										<tbody>
											<tr
												v-for="(item, i) in quoteData.products"
												:key="item.id"
												:style="item.is_optional ? 'background: #f2f0c7' : ''"
											>
												<td>{{ ++i }}</td>
												<td>
													<div>
														{{ item.part_number }}
													</div>
													<div>
														{{ item.description }}
													</div>
												</td>
												<td>{{ item.uom }}</td>
												<td>{{ item.quantity }}</td>
												<td>
													<span
														>{{ quoteData.client_currency?.symbol ? quoteData.client_currency?.symbol : "USD" }}
													</span>
													{{ item.unit_price }}
												</td>
												<td>
													<span>
														{{ quoteData.client_currency?.symbol ? quoteData.client_currency?.symbol : "USD" }}
													</span>
													{{ item.total }}
												</td>
											</tr>
											<tr v-if="quoteData.packaging_details">
												<td>
													<span v-if="quoteData.products">{{ quoteData.products?.length + 1 }}</span>
												</td>
												<td colspan="4">
													<div>
														<span class="me-2">{{ quoteData.delivery_port?.name }}</span>
													</div>
													<div>
														<div v-html="quoteData.delivery_method_terms"></div>
														<div>{{ quoteData.delivery_port?.delivery_method?.name }}</div>
													</div>
												</td>
												<td>
													<span>
														{{ quoteData.client_currency?.symbol ? quoteData.client_currency?.symbol : "USD" }}
													</span>
													{{ quoteData.actual_delivery_price }}
												</td>
											</tr>
										</tbody>
									</v-simple-table>

									<v-divider class="mb-3" />

									<v-row>
										<v-col md="8"></v-col>
										<v-col md="4" cols="12">
											<v-card outlined rounded="0">
												<v-card-text>
													<v-list-item dense>
														<v-list-item-content class="mb-0 pb-0">
															<v-list-item-title>Subtotal</v-list-item-title>
														</v-list-item-content>
														<v-list-item-content class="mb-0 pb-0 text-end pe-4">
															<v-list-item-title class="font-weight-bold">
																<span>{{
																	quoteData.client_currency?.symbol
																		? quoteData.client_currency?.symbol
																		: "USD"
																}}</span>
																<!-- {{ quoteData.to_currency }} -->
																{{ quoteData.subtotal }}
															</v-list-item-title>
														</v-list-item-content>
													</v-list-item>
													<v-divider />
													<v-list-item v-if="quoteData.tax_label && quoteData.tax_amount" dense>
														<v-list-item-content class="mb-0 pb-0">
															<v-list-item-title class="text-capitalize">{{
																quoteData.tax_label
															}}</v-list-item-title>
														</v-list-item-content>
														<v-list-item-content class="mb-0 pb-0 text-end pe-4">
															<v-list-item-title class="font-weight-bold">
																{{ quoteData.tax_amount }}
															</v-list-item-title>
														</v-list-item-content>
													</v-list-item>
													<v-divider v-if="quoteData.tax_label && quoteData.tax" />
													<v-list-item v-if="quoteData.discount_amount" dense>
														<v-list-item-content class="mb-0 pb-0">
															<v-list-item-title> Special Discount </v-list-item-title>
														</v-list-item-content>
														<v-list-item-content class="mb-0 pb-0 text-end pe-4">
															<v-list-item-title class="font-weight-bold">
																<span>{{
																	quoteData.client_currency?.symbol
																		? quoteData.client_currency?.symbol
																		: "USD"
																}}</span>
																{{ quoteData.discount_amount }}
															</v-list-item-title>
														</v-list-item-content>
													</v-list-item>

													<v-list-item v-if="quoteData.delivery_price" dense>
														<v-list-item-content class="mb-0 pb-0">
															<v-list-item-title> Delivery Price </v-list-item-title>
														</v-list-item-content>
														<v-list-item-content class="mb-0 pb-0 text-end pe-4">
															<v-list-item-title class="font-weight-bold">
																<!-- {{ quoteData.to_currency }} -->
																<span>{{
																	quoteData.client_currency?.symbol
																		? quoteData.client_currency?.symbol
																		: "USD"
																}}</span>
																{{ quoteData.delivery_price }}
															</v-list-item-title>
														</v-list-item-content>
													</v-list-item>
													<v-list-item v-else-if="quoteData.estimated_delivery_price" dense>
														<v-list-item-content class="mb-0 pb-0">
															<v-list-item-title>Estimated Delivery Price </v-list-item-title>
														</v-list-item-content>
														<v-list-item-content class="mb-0 pb-0 text-end pe-4">
															<v-list-item-title class="font-weight-bold">
																<span>{{
																	quoteData.client_currency?.symbol
																		? quoteData.client_currency?.symbol
																		: "USD"
																}}</span>
																{{ quoteData.estimated_delivery_price }}
															</v-list-item-title>
														</v-list-item-content>
													</v-list-item>
													<v-divider v-if="quoteData.delivery_price || quoteData.estimated_delivery_price" />
													<v-list-item dense>
														<v-list-item-content class="mb-0 pb-0">
															<v-list-item-title>Total</v-list-item-title>
														</v-list-item-content>
														<v-list-item-content class="mb-0 pb-0 text-end pe-4">
															<v-list-item-title class="font-weight-bold">
																<span>{{
																	quoteData.client_currency?.symbol
																		? quoteData.client_currency?.symbol
																		: "USD"
																}}</span>
																{{ quoteData.to_currency }}
																{{ quoteData.total }}
															</v-list-item-title>
														</v-list-item-content>
													</v-list-item>
												</v-card-text>
											</v-card>
											<p class="mt-3 font-weight-bold">{{ quoteData.total_spell }}</p>
										</v-col>
									</v-row>
								</v-card-text>
							</v-card>
						</v-col>
					</v-row>

					<v-row class="fill-height mt-0">
						<v-col cols="12">
							<div style="margin-top: 10px">
								<h3 class="font-weight-bold mb-3">Notes</h3>
								<p class="caption" v-html="quoteData.notes"></p>
							</div>
						</v-col>
					</v-row>

					<v-row class="fill-height mt-0">
						<v-col cols="12">
							<p style="margin-bottom: 10px">
								If you would like to discuss this quotation, please contact your representative directly at the numbers
								indicated.
							</p>

							<ul style="margin-bottom: 10px">
								<li><b>Contact:</b> Mohamed El Hamed</li>
								<li><b>Phone:</b> +**************** ,+971 521 721 569</li>
								<li><b>e-Mail:</b><EMAIL></li>
							</ul>

							<b>
								Quote valid for
								<span class="px-2" style="background: yellow; color: #000 !important">
									{{ quoteData.valid_until }} days.
								</span>
								Prices subject to change based on date of release for fabrication and shipment.
							</b>
							<div class="mt-1" v-html="quoteData.payment_method_terms"></div>
						</v-col>
					</v-row>
					<v-divider style="margin-top: 20px; margin-bottom: 20px" />

					<v-row class="text-center">
						<v-col cols="12">
							<p style="text-align: center">
								JAFZA ONE, Tower B, 13th Floor, Suite BB1302, P.O Box 17046, JEBEL ALI, Dubai United Arab of Emirates, Tel:
								+971 (04) 268 4666
							</p>
							<div v-if="disableFactoryLogo" class="text-center mx-auto mt-8" style="max-width: 250px">
								<!-- <v-img :src="quoteData.factory?.logo" /> -->
								<v-img v-if="quoteData.factory?.name === 'Nystrom'" class="mx-auto" src="/images/factories/nystrom.png" />
								<v-img v-else-if="quoteData.factory?.name === 'BACH'" class="mx-auto" src="/images/factories/bach.png" />
								<v-img v-else-if="quoteData.factory?.name === 'EZS'" class="mx-auto" src="/images/factories/ezs.png" />
								<v-img v-else-if="quoteData.factory?.name === 'ARDEX'" class="mx-auto" src="/images/factories/ardex.png" />
								<v-img v-else-if="quoteData.factory?.name === 'ej'" class="mx-auto" src="/images/factories/ej.png" />
								<v-img
									v-else-if="quoteData.factory?.name === 'Meishuo'"
									class="mx-auto"
									src="/images/factories/meishuo.png"
								/>
								<v-img v-else-if="quoteData.factory?.name === 'Form'" class="mx-auto" src="/images/factories/form.png" />
								<v-img
									v-else-if="quoteData.factory?.name === 'Rollsis'"
									class="mx-auto"
									src="/images/factories/rollsis.png"
								/>
								<v-img
									v-else-if="quoteData.factory?.name === 'Genotek'"
									class="mx-auto"
									src="/images/factories/genotek.png"
								/>
							</div>
						</v-col>
					</v-row>
				</v-container>
			</div>
		</v-card-text>
	</v-card>
</template>


<script>
export default {
	props: {
		quoteData: {
			type: Object,
			default: () => ({}),
		},
		disableFactoryLogo: {
			type: Boolean,
			default: false,
		},
	},
};
</script>

<style lang="scss">
.theme--dark #formal-quote-pdf {
	background: #121212 !important;
}
</style>

