<template>
	<div>
		<!-- 		<v-tabs>
				<v-tab @click="orderType = 'op'">Purchase Order</v-tab>
				<v-tab @click="orderType = 'pt'">Pick Ticket</v-tab>
				<v-tab @click="orderType = 'pi'">Proforma Invoice</v-tab>
				<v-tab @click="orderType = 'invoice'">Invoice</v-tab>
			</v-tabs>
		</div>

		<v-card outlined class="mt-4">
			<v-card-text>
				<div v-if="orderType === 'op'" id="element-to-convert" style="background: #fff">
					<div id="element-to-convert" style="background: #fff">
						<v-container class="pa-8" style="padding: 20px">
							<v-row>
								<v-col md="6">
									<v-img src="/logo.png" width="350px" style="width: 250px"></v-img>
								</v-col>
								<v-col md="6">
									<div class="text-end" style="text-align: right">
										<div style="font-weight: bold; font-size: 1.6rem">PURCHASE ORDER</div>
										<div style="font-weight: bold">{{ quoteData.reference_number }}</div>
										<div style="font-weight: bold">{{ quoteData.created_at | date }}</div>
									</div>
								</v-col>
							</v-row>

							<v-divider class="my-3" />

							<v-row class="relative">
								<div v-if="pdfHiddenElements" class="absolute end">
									<v-btn icon small text class="text-end mt-1" @click="exportToPDF"><v-icon>mdi-download</v-icon></v-btn>
								</div>

								<v-col md="6" cols="12">
									<div>
										<div style="margin-bottom: 25px; margin-top: 5px">
											<h4 class="font-weight-bold" style="margin-bottom: 6px; padding-left: 3px; background: #ebebeb">
												BILL TO
											</h4>
											<span class="d-inline-block coffee-3--text body-2">AREEL SPECIALTIES INTERNATIONAL FZCO</span>
											<span class="d-inline-block coffee-3--text body-2"
												>JAFZA ONE, Tower B , 13th Floor, Suite BB1302</span
											>
											<span class="d-inline-block coffee-3--text body-2" style="margin-bottom: 8px"
												>P.O Box 17046, JEBEL ALI, Duba</span
											>

											<div style="display: flex; align-items: center; margin-bottom: 2px">
												<h5 class="d-inline-block" style="margin-right: 5px">Attention of:</h5>
												<span class="d-inline-block coffee-3--text body-2">{{
													quoteData.client_contact_person?.name
												}}</span>
											</div>
											<div style="display: flex; align-items: center; margin-bottom: 2px">
												<h5 class="d-inline-block" style="margin-right: 5px">Phone:</h5>
												<span class="d-inline-block coffee-3--text body-2">
													{{ quoteData.client_contact_person?.phone }}
												</span>
											</div>
											<div style="display: flex; align-items: center; margin-bottom: 2px">
												<h5 class="d-inline-block" style="margin-right: 5px">Mobile:</h5>
												<span class="d-inline-block coffee-3--text body-2">{{
													quoteData.client_contact_person?.mobile
												}}</span>
											</div>
											<div style="display: flex; align-items: center; margin-bottom: 2px">
												<h5 class="d-inline-block" style="margin-right: 5px">Email:</h5>
												<span class="d-inline-block coffee-3--text body-2">{{
													quoteData.account_manager?.email
												}}</span>
											</div>
										</div>
									</div>
								</v-col>

								<v-col md="6" cols="12">
									<div>
										<div style="margin-bottom: 25px; margin-top: 5px">
											<div style="margin-bottom: 8px">
												<h4
													class="font-weight-bold"
													style="background: #ebebeb; padding-left: 3px; margin-right: 5px"
												>
													PRODUCT
												</h4>
												<span class="d-inline-block coffee-3--text body-2">{{
													quoteData.product_category?.name
												}}</span>
											</div>

											<div style="display: flex; align-items: center; margin-bottom: 2px">
												<h5 class="d-inline-block" style="margin-right: 5px">Quote Ref. #:</h5>
												<span class="d-inline-block coffee-3--text body-2">{{ quoteData.reference_number }}</span>
											</div>
											<div style="display: flex; align-items: center; margin-bottom: 2px">
												<h5 class="d-inline-block" style="margin-right: 5px">Quote Date:</h5>
												<span class="d-inline-block coffee-3--text body-2">{{
													quoteData.valid_until_date | date
												}}</span>
											</div>
										</div>
										<div style="margin-bottom: 25px; margin-top: 5px">
											<h4 class="font-weight-bold" style="margin-bottom: 6px; background: #ebebeb; padding-left: 3px">
												SHIPPING ADDRESS
											</h4>

											<div style="display: flex; align-items: center; margin-bottom: 2px">
												<h5 class="d-inline-block"></h5>
												<span class="d-inline-block coffee-3--text body-2" v-html="quoteData.shipping_instructions">
												</span>
											</div>
											<div style="display: flex; align-items: center; margin-bottom: 2px">
												<h5 class="d-inline-block">-</h5>
												<span class="d-inline-block coffee-3--text body-2"></span>
											</div>
										</div>
									</div>
								</v-col>

								<v-col cols="12">
									<v-card outlined>
										<v-card-text>
											<v-simple-table class="border">
												<thead style="background: #232323">
													<tr>
														<th class="text-left" style="color: #fff">Quantity</th>
														<th class="text-left" style="color: #fff">UOM</th>
														<th class="text-left" style="color: #fff">Part # / Description</th>
														<th class="text-left" style="color: #fff">Unit Price</th>
														<th class="text-left" style="color: #fff">Total</th>
													</tr>
												</thead>
												<tbody>
													<tr v-for="item in quoteData.products" :key="item">
														<template v-if="!item.is_optional">
															<td>{{ item.quantity }}</td>
															<td>{{ item.uom }}</td>

															<td>
																<div>
																	{{ item.part_number }}
																</div>
																<div>
																	{{ item.description }}
																</div>
															</td>
															<td>{{ item.unit_price }}</td>
															<td>{{ item.total }}</td>
														</template>
													</tr>
												</tbody>
											</v-simple-table>
										</v-card-text>
									</v-card>
								</v-col>
							</v-row>

							<v-row class="fill-height mt-0">
								<v-col cols="8">
									<div class="mt-6">
										<h4 class="font-weight-bold mb-3" style="margin-right: 5px">Terms & Conditions :</h4>
										<span class="d-inline-block coffee-3--text body-2" v-html="quoteData.delivery_method_terms"></span>
									</div>
									<div class="mt-4 d-flex">
										<h4 class="font-weight-bold mb-3" style="margin-right: 5px">Payment :</h4>
										<span class="d-inline-block coffee-3--text body-2">{{ quoteData.payment_method }}</span>
									</div>
									<div class="mt-4 d-flex">
										<h4 class="font-weight-bold mb-3" style="margin-right: 5px">Packing :</h4>
										<div class="d-inline-block coffee-3--text body-2" v-html="quoteData.delivery_notes"></div>
									</div>
									<div class="mt-4">
										<h4 class="font-weight-bold mb-3">Notes</h4>
										<p class="coffee-3--text body-2">-</p>
									</div>
									<div class="mt-6">
										<h4 class="font-weight-bold mb-3">Warranty</h4>
										<p class="coffee-3--text body-2">
											To be provided with 5 years against manufacturing defects starting from date of delivery
										</p>
									</div>
								</v-col>

								<v-spacer />

								<v-col md="3" cols="12" class="mt-0 pt-0">
									<v-card outlined color="#ecebeb" rounded="0">
										<v-card-content>
											<v-list-item dense>
												<v-list-item-content class="mb-0 pb-0">
													<v-list-item-title>Subtotal</v-list-item-title>
												</v-list-item-content>
												<v-list-item-content class="mb-0 pb-0 text-end pe-4">
													<v-list-item-title class="font-weight-bold"
														>{{ quoteData.to_currency }} {{ quoteData.subtotal }}</v-list-item-title
													>
												</v-list-item-content>
											</v-list-item>
											<v-divider />

											<v-list-item v-if="quoteData.tax_label && quoteData.tax" dense>
												<v-list-item-content class="mb-0 pb-0">
													<v-list-item-title class="text-capitalize">{{ quoteData.tax_label }}</v-list-item-title>
												</v-list-item-content>
												<v-list-item-content class="mb-0 pb-0 text-end pe-4">
													<v-list-item-title class="font-weight-bold"> %{{ quoteData.tax }} </v-list-item-title>
												</v-list-item-content>
											</v-list-item>
											<v-divider v-if="quoteData.tax_label && quoteData.tax" />

											<v-list-item v-if="quoteData.discount" dense>
												<v-list-item-content class="mb-0 pb-0">
													<v-list-item-title>Special Discount</v-list-item-title>
												</v-list-item-content>
												<v-list-item-content class="mb-0 pb-0 text-end pe-4">
													<v-list-item-title class="font-weight-bold"
														>%{{ quoteData.discount }}</v-list-item-title
													>
												</v-list-item-content>
											</v-list-item>
											<v-divider v-if="quoteData.discount" />
											<v-list-item dense>
												<v-list-item-content class="mb-0 pb-0">
													<v-list-item-title>Total</v-list-item-title>
												</v-list-item-content>
												<v-list-item-content class="mb-0 pb-0 text-end pe-4">
													<v-list-item-title class="font-weight-bold"
														>{{ quoteData.to_currency }} {{ quoteData.total }}</v-list-item-title
													>
												</v-list-item-content>
											</v-list-item>
										</v-card-content>
									</v-card>
								</v-col>
							</v-row>
							<v-divider style="margin-top: 50px" />

							<v-row>
								<v-col class="text-center" style="font-size: 14px; text-align: center">
									<div style="margin-top: 15px">
										United Arab Emirates, JAFZA ONE, Tower B, 13th Floor, Suite BB1302, JEBEL ALI, P.O Box 17046, Dubai
									</div>
									<div>JEBEL ALI, P.O Box 17046, Dubai, UAE, <EMAIL>, Tel: +971 (04) 268 4666</div>
									<div>Jordan, Amman, King Abdullah II Street, Complex No. 150, office 313 + 314</div>
									<div><EMAIL>, Tel.:+962 6 5371119, Fax :+962 6 5378889, Mob.:+962 79 6022771/2</div>
								</v-col>
							</v-row>
						</v-container>
					</div>
				</div>

				<div v-if="orderType === 'pt'" id="element-to-convert" style="background: #fff">
					<v-container class="pa-8" style="padding: 20px">
						<v-row>
							<v-col md="6">
								<v-img src="/logo.png" width="350px" style="width: 250px"></v-img>
								<div style="width: 250px; text-align: center">
									<div style="font-size: 12px">JAFZA ONE, Tower B , 13th Floor, Suite BB1302</div>
									<div style="font-size: 12px">P.O Box 17046, JEBEL ALI, Dubai, UAE</div>
									<div style="font-size: 12px">Tel: +971 (04) 268 4666</div>
								</div>
							</v-col>
							<v-col md="6">
								<v-simple-table style="border: 1px solid #ebebeb">
									<thead>
										<tr style="background: #696969">
											<th style="font-size: 2rem; color: #fff; text-align: center" colspan="4">PICK TICKET</th>
										</tr>
										<tr>
											<th class="text-left" style="background: #ebebeb">Sales Order #</th>
											<th class="text-left" style="background: #ebebeb">Order Date</th>
											<th class="text-left" style="background: #ebebeb">Customer #</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>{{ quoteData.pick_ticket?.reference_number }}</td>
											<td>{{ quoteData.pick_ticket?.created_at | date }}</td>
											<td>{{ quoteData.client_contact_person?.client?.name }}</td>
										</tr>
										<tr style="background: #ebebeb">
											<th colspan="2">Account Manager</th>
											<th class="text-left" style="background: #ebebeb">Purchase Order</th>
										</tr>
										<tr>
											<td colspan="2">{{ quoteData.account_manager?.name }}</td>
											<td>{{ quoteData.client_contact_person?.client?.name }}</td>
										</tr>
									</tbody>
								</v-simple-table>
							</v-col>
						</v-row>

						<v-divider class="my-3" />

						<v-row class="relative">
							<div v-if="pdfHiddenElements" class="absolute end">
								<v-btn icon small text class="text-end mt-1" @click="exportToPDF"><v-icon>mdi-download</v-icon></v-btn>
							</div>

							<v-col md="6" cols="12">
								<div>
									<div style="margin-bottom: 25px; margin-top: 5px">
										<h3 class="font-weight-bold" style="margin-bottom: 6px">BILL TO</h3>
										<span class="d-inline-block coffee-3--text body-2">AREEL SPECIALTIES INTERNATIONAL FZCO</span>
										<span class="d-inline-block coffee-3--text body-2"
											>JAFZA ONE, Tower B , 13th Floor, Suite BB1302</span
										>
										<span class="d-inline-block coffee-3--text body-2">P.O Box 17046, JEBEL ALI, Duba</span>
										<span class="d-inline-block coffee-3--text body-2" style="margin-bottom: 8px"
											>United Arab of Emirates</span
										>

										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block">Attention of:</h5>
											<span class="d-inline-block coffee-3--text body-2">{{ quoteData.account_manager?.name }}</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block">Phone:</h5>
											<span class="d-inline-block coffee-3--text body-2">+971 (04) 268 4666</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block">Mobile:</h5>
											<span class="d-inline-block coffee-3--text body-2">+971 521 721 569</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block">Email:</h5>
											<span class="d-inline-block coffee-3--text body-2">{{ quoteData.account_manager?.email }}</span>
										</div>
									</div>
								</div>
							</v-col>

							<v-col md="6" cols="12">
								<div>
									<div style="margin-bottom: 25px; margin-top: 5px">
										<div style="margin-bottom: 8px">
											<h3 class="font-weight-bold">SHIP TO</h3>
										</div>

										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-1">Client Name:</h5>
											<span class="d-inline-block coffee-3--text body-2">
												{{ quoteData.client_contact_person?.client?.name }}
											</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-1">Client Email:</h5>
											<span class="d-inline-block coffee-3--text body-2">
												{{ quoteData.client_contact_person?.client?.email }}
											</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-1">Client Address:</h5>
											<span class="d-inline-block coffee-3--text body-2">
												{{ quoteData.client_contact_person?.address_one }}
											</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-1">Address Tow:</h5>
											<span class="d-inline-block coffee-3--text body-2">
												{{ quoteData.client_contact_person?.address_two }}
											</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-1">Phone:</h5>
											<span class="d-inline-block coffee-3--text body-2">
												{{ quoteData.client_contact_person?.phone }}
											</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-1">Fax:</h5>
											<span class="d-inline-block coffee-3--text body-2">
												{{ quoteData.client_contact_person?.fax }}
											</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-1">Email:</h5>
											<span class="d-inline-block coffee-3--text body-2">
												{{ quoteData.client_contact_person?.client?.email }}
											</span>
										</div>
									</div>
								</div>
							</v-col>

							<v-col cols="12">
								<v-card outlined>
									<v-card-text>
										<v-simple-table class="border">
											<thead style="background: #232323">
												<tr>
													<th class="text-left" style="color: #fff">Quantity</th>
													<th class="text-left" style="color: #fff">UOM</th>
													<th class="text-left" style="color: #fff">Part # / Description</th>
													<th class="text-left" style="color: #fff">Unit Price</th>
													<th class="text-left" style="color: #fff">Total</th>
												</tr>
											</thead>
											<tbody>
												<tr v-for="item in quoteData.products" :key="item">
													<template v-if="!item.is_optional">
														<td>{{ item.quantity }}</td>
														<td>{{ item.uom }}</td>

														<td>
															<div>
																{{ item.part_number }}
															</div>
															<div>
																{{ item.description }}
															</div>
														</td>
														<td>{{ item.unit_price }}</td>
														<td>{{ item.total }}</td>
													</template>
												</tr>
											</tbody>
										</v-simple-table>
									</v-card-text>
								</v-card>
							</v-col>
						</v-row>

						<v-row class="fill-height mt-0">
							<v-col cols="8"> </v-col>

							<v-spacer />

							<v-col md="3" cols="12" class="mt-0 pt-0">
								<v-card outlined color="#ecebeb" rounded="0">
									<v-card-content>
										<v-list-item dense>
											<v-list-item-content class="mb-0 pb-0">
												<v-list-item-title>Subtotal</v-list-item-title>
											</v-list-item-content>
											<v-list-item-content class="mb-0 pb-0 text-end pe-4">
												<v-list-item-title class="font-weight-bold"
													>{{ quoteData.to_currency }} {{ quoteData.subtotal }}</v-list-item-title
												>
											</v-list-item-content>
										</v-list-item>
										<v-divider />

										<v-list-item v-if="quoteData.tax_label && quoteData.tax" dense>
											<v-list-item-content class="mb-0 pb-0">
												<v-list-item-title class="text-capitalize">{{ quoteData.tax_label }}</v-list-item-title>
											</v-list-item-content>
											<v-list-item-content class="mb-0 pb-0 text-end pe-4">
												<v-list-item-title class="font-weight-bold"> %{{ quoteData.tax }} </v-list-item-title>
											</v-list-item-content>
										</v-list-item>
										<v-divider v-if="quoteData.tax_label && quoteData.tax" />

										<v-list-item v-if="quoteData.discount" dense>
											<v-list-item-content class="mb-0 pb-0">
												<v-list-item-title>Special Discount</v-list-item-title>
											</v-list-item-content>
											<v-list-item-content class="mb-0 pb-0 text-end pe-4">
												<v-list-item-title class="font-weight-bold">%{{ quoteData.discount }}</v-list-item-title>
											</v-list-item-content>
										</v-list-item>
										<v-divider v-if="quoteData.discount" />
										<v-list-item dense>
											<v-list-item-content class="mb-0 pb-0">
												<v-list-item-title>Total</v-list-item-title>
											</v-list-item-content>
											<v-list-item-content class="mb-0 pb-0 text-end pe-4">
												<v-list-item-title class="font-weight-bold"
													>{{ quoteData.to_currency }} {{ quoteData.total }}</v-list-item-title
												>
											</v-list-item-content>
										</v-list-item>
									</v-card-content>
								</v-card>
							</v-col>
						</v-row>
					</v-container>
				</div>

				<div v-if="orderType === 'pi'" id="element-to-convert" style="background: #fff">
					<v-container class="pa-8" style="padding: 20px">
						<v-row>
							<v-col md="6">
								<v-img src="/logo.png" width="350px" style="width: 250px"></v-img>
							</v-col>
							<v-col md="6">
								<div class="text-end" style="text-align: right">
									<div style="font-weight: bold; font-size: 1.6rem">Proforma Invoice</div>
									<div style="font-weight: bold">
										{{ quoteData.proforma_invoice?.reference_number }}
									</div>
									<div style="font-weight: bold">Date: {{ quoteData.proforma_invoice?.created_at | date }}</div>
									<div style="font-weight: bold">TRN: {{ quoteData.proforma_invoice?.trn_number }}</div>
								</div>
							</v-col>
						</v-row>

						<v-divider class="my-3" />

						<v-row class="relative">
							<div v-if="pdfHiddenElements" class="absolute end">
								<v-btn icon small text class="text-end mt-1" @click="exportToPDF"><v-icon>mdi-download</v-icon></v-btn>
							</div>

							<v-col md="6" cols="12">
								<div>
									<div style="margin-bottom: 8px">
										<h4 class="font-weight-bold" style="background: #ebebeb; padding-left: 3px; margin-right: 5px">
											PROJECT
										</h4>
										<span class="d-inline-block coffee-3--text body-2">Venetian Macao</span>
									</div>
									<div style="margin-bottom: 5px; margin-top: 5px">
										<h5 class="d-inline-block">P.O Ref. #:</h5>
										<span class="d-inline-block coffee-3--text body-2">{{ quoteData.reference_number }} </span>
									</div>
									<div style="display: flex; align-items: center; margin-bottom: 2px">
										<h5 class="d-inline-block">P.O Date:</h5>
										<span class="d-inline-block coffee-3--text body-2">{{ quoteData.created_at | date }} </span>
									</div>
								</div>
							</v-col>
							<v-col md="6" cols="12">
								<div>
									<div style="margin-bottom: 25px">
										<div style="margin-bottom: 8px">
											<h4 class="font-weight-bold" style="background: #ebebeb; padding-left: 3px; margin-right: 5px">
												PRODUCT
											</h4>
											<span class="d-inline-block coffee-3--text body-2">{{ quoteData.product_category?.name }}</span>
										</div>

										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block">Quote Ref. #:</h5>
											<span class="d-inline-block coffee-3--text body-2">{{ quoteData.reference_number }}</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block">Quote Date:</h5>
											<span class="d-inline-block coffee-3--text body-2">{{
												quoteData.valid_until_date | date
											}}</span>
										</div>
									</div>
								</div>
							</v-col>
							<v-col md="6" cols="12">
								<div>
									<div style="margin-bottom: 25px; margin-top: 5px">
										<h4 class="font-weight-bold" style="background: #ebebeb; padding-left: 3px; margin-right: 5px">
											BILL TO
										</h4>
										<span class="d-inline-block coffee-3--text body-2">AREEL SPECIALTIES INTERNATIONAL FZCO</span>
										<span class="d-inline-block coffee-3--text body-2"
											>JAFZA ONE, Tower B , 13th Floor, Suite BB1302</span
										>
										<span class="d-inline-block coffee-3--text body-2">P.O Box 17046, JEBEL ALI, Duba</span>
										<span class="d-inline-block coffee-3--text body-2" style="margin-bottom: 8px"
											>United Arab of Emirates</span
										>

										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block">Attention of:</h5>
											<span class="d-inline-block coffee-3--text body-2">{{ quoteData.account_manager?.name }}</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block">Phone:</h5>
											<span class="d-inline-block coffee-3--text body-2">+971 (04) 268 4666</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block">Mobile:</h5>
											<span class="d-inline-block coffee-3--text body-2">+971 521 721 569</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block">Email:</h5>
											<span class="d-inline-block coffee-3--text body-2">{{ quoteData.account_manager?.email }}</span>
										</div>
									</div>
								</div>
							</v-col>

							<v-col md="6" cols="12">
								<div>
									<div style="margin-bottom: 25px; margin-top: 5px">
										<h4 class="font-weight-bold" style="background: #ebebeb; padding-left: 3px; margin-right: 5px">
											SHIP TO
										</h4>

										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block">-</h5>
											<span class="d-inline-block coffee-3--text body-2"></span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block">-</h5>
											<span class="d-inline-block coffee-3--text body-2"></span>
										</div>
									</div>
								</div>
							</v-col>

							<v-col cols="12">
								<v-card outlined>
									<v-card-text>
										<v-simple-table class="border">
											<thead style="background: #232323">
												<tr>
													<th class="text-left" style="color: #fff">Quantity</th>
													<th class="text-left" style="color: #fff">UOM</th>
													<th class="text-left" style="color: #fff">Part # / Description</th>
													<th class="text-left" style="color: #fff">Unit Price</th>
													<th class="text-left" style="color: #fff">Total</th>
												</tr>
											</thead>
											<tbody>
												<tr v-for="item in quoteData.products" :key="item">
													<template v-if="!item.is_optional">
														<td>{{ item.quantity }}</td>
														<td>{{ item.uom }}</td>

														<td>
															<div>
																{{ item.part_number }}
															</div>
															<div>
																{{ item.description }}
															</div>
														</td>
														<td>{{ item.unit_price }}</td>
														<td>{{ item.total }}</td>
													</template>
												</tr>
											</tbody>
										</v-simple-table>
									</v-card-text>
								</v-card>
							</v-col>
						</v-row>

						<v-row class="fill-height mt-0">
							<v-col cols="8">
								<div class="mt-6">
									<h3 class="font-weight-bold mb-3">Payment Terms and Conditions:</h3>
									<div class="coffee-3--text body-2" v-html="quoteData.payment_method_terms"></div>
								</div>
								<div class="mt-6">
									<h3 class="font-weight-bold mb-3">Accounts Details</h3>

									<v-row>
										<v-col v-for="(account, i) in quoteData.proforma_invoice?.bank_accounts" :key="i" md="6">
											<h5>{{ account.bank_name }}</h5>
											<div class="body-2"><b>IBAN: </b>{{ account.iban }}</div>
											<div class="body-2"><b>Swift Code: </b>{{ account.swift_code }}</div>
											<div class="body-2"><b>Account Number: </b>{{ account.account_number }}</div>
											<div class="body-2"><b>Branch Name: </b>{{ account.branch_name }}</div>
											<div class="body-2"><b>Currency: </b>{{ account.currency }}</div>
										</v-col>
									</v-row>
								</div>
							</v-col>

							<v-spacer />

							<v-col md="3" cols="12" class="mt-0 pt-0">
								<v-card outlined color="#ecebeb" rounded="0">
									<v-card-content>
										<v-list-item dense>
											<v-list-item-content class="mb-0 pb-0">
												<v-list-item-title>Subtotal</v-list-item-title>
											</v-list-item-content>
											<v-list-item-content class="mb-0 pb-0 text-end pe-4">
												<v-list-item-title class="font-weight-bold"
													>{{ quoteData.to_currency }} {{ quoteData.subtotal }}</v-list-item-title
												>
											</v-list-item-content>
										</v-list-item>
										<v-divider />

										<v-list-item v-if="quoteData.tax_label && quoteData.tax" dense>
											<v-list-item-content class="mb-0 pb-0">
												<v-list-item-title class="text-capitalize">{{ quoteData.tax_label }}</v-list-item-title>
											</v-list-item-content>
											<v-list-item-content class="mb-0 pb-0 text-end pe-4">
												<v-list-item-title class="font-weight-bold"> %{{ quoteData.tax }} </v-list-item-title>
											</v-list-item-content>
										</v-list-item>
										<v-divider v-if="quoteData.tax_label && quoteData.tax" />

										<v-list-item v-if="quoteData.discount" dense>
											<v-list-item-content class="mb-0 pb-0">
												<v-list-item-title>Special Discount</v-list-item-title>
											</v-list-item-content>
											<v-list-item-content class="mb-0 pb-0 text-end pe-4">
												<v-list-item-title class="font-weight-bold">%{{ quoteData.discount }}</v-list-item-title>
											</v-list-item-content>
										</v-list-item>
										<v-divider v-if="quoteData.discount" />
										<v-list-item dense>
											<v-list-item-content class="mb-0 pb-0">
												<v-list-item-title>Total</v-list-item-title>
											</v-list-item-content>
											<v-list-item-content class="mb-0 pb-0 text-end pe-4">
												<v-list-item-title class="font-weight-bold"
													>{{ quoteData.to_currency }} {{ quoteData.total }}</v-list-item-title
												>
											</v-list-item-content>
										</v-list-item>
									</v-card-content>
								</v-card>
							</v-col>
						</v-row>
					</v-container>
				</div>

				<div v-if="orderType === 'invoice'" id="element-to-convert" style="background: #fff">
					<v-container class="pa-8" style="padding: 20px">
						<v-row>
							<v-col md="6">
								<v-img src="/logo.png" width="350px" style="width: 250px"></v-img>
							</v-col>
							<v-col md="6">
								<div class="text-end" style="text-align: right">
									<div style="font-weight: bold; font-size: 1.6rem">INVOICE</div>
									<div style="font-weight: bold">
										{{ quoteData.pick_ticket?.reference_number }}
									</div>
									<div style="font-weight: bold">Date: {{ quoteData.pick_ticket?.created_at | date }}</div>
									<div style="font-weight: bold">Purchase Order: PB303_23</div>
								</div>
							</v-col>
						</v-row>

						<v-divider class="my-3" />

						<v-row class="relative">
							<div v-if="pdfHiddenElements" class="absolute end">
								<v-btn icon small text class="text-end mt-1" @click="exportToPDF"><v-icon>mdi-download</v-icon></v-btn>
							</div>

							<v-col md="6" cols="12">
								<div>
									<div style="margin-bottom: 25px; margin-top: 5px">
										<h3 class="font-weight-bold" style="margin-bottom: 6px">BILL TO</h3>
										<span class="d-inline-block coffee-3--text body-2">AREEL SPECIALTIES INTERNATIONAL FZCO</span>
										<span class="d-inline-block coffee-3--text body-2"
											>JAFZA ONE, Tower B , 13th Floor, Suite BB1302</span
										>
										<span class="d-inline-block coffee-3--text body-2">P.O Box 17046, JEBEL ALI, Duba</span>
										<span class="d-inline-block coffee-3--text body-2" style="margin-bottom: 8px"
											>United Arab of Emirates</span
										>

										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block">Attention of:</h5>
											<span class="d-inline-block coffee-3--text body-2">{{ quoteData.account_manager?.name }}</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block">Phone:</h5>
											<span class="d-inline-block coffee-3--text body-2">+971 (04) 268 4666</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block">Mobile:</h5>
											<span class="d-inline-block coffee-3--text body-2"> +971 521 721 569</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block">Email:</h5>
											<span class="d-inline-block coffee-3--text body-2">{{ quoteData.account_manager?.email }}</span>
										</div>
									</div>
								</div>
							</v-col>

							<v-col md="6" cols="12">
								<div>
									<div style="margin-bottom: 25px; margin-top: 5px">
										<div style="margin-bottom: 8px">
											<h3 class="font-weight-bold">SHIP TO</h3>
										</div>

										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-1">Client Name:</h5>
											<span class="d-inline-block coffee-3--text body-2">
												{{ quoteData.client_contact_person?.client?.name }}
											</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-1">Client Email:</h5>
											<span class="d-inline-block coffee-3--text body-2">
												{{ quoteData.client_contact_person?.client?.email }}
											</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-1">Client Address:</h5>
											<span class="d-inline-block coffee-3--text body-2">
												{{ quoteData.client_contact_person?.address_one }}
											</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-1">Address Tow:</h5>
											<span class="d-inline-block coffee-3--text body-2">
												{{ quoteData.client_contact_person?.address_two }}
											</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-1">Phone:</h5>
											<span class="d-inline-block coffee-3--text body-2">
												{{ quoteData.client_contact_person?.phone }}
											</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-1">Fax:</h5>
											<span class="d-inline-block coffee-3--text body-2">
												{{ quoteData.client_contact_person?.fax }}
											</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-1">Email:</h5>
											<span class="d-inline-block coffee-3--text body-2">
												{{ quoteData.client_contact_person?.client?.email }}
											</span>
										</div>
									</div>
								</div>
							</v-col>
							<v-col cols="12">
								<v-simple-table>
									<thead>
										<tr>
											<th class="text-left">Account Manager</th>
											<th class="text-left">Collect/Prepaid</th>
											<th class="text-left">Collect/Prepaid</th>
											<th class="text-left">Location</th>
											<th class="text-left">Carrier</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>{{ quoteData.account_manager?.name }}</td>
											<td>-</td>
											<td>-</td>
											<td>-</td>
											<td>-</td>
										</tr>
									</tbody>
								</v-simple-table>
							</v-col>

							<v-col cols="12">
								<v-card outlined>
									<v-card-text>
										<v-simple-table class="border">
											<thead style="background: #232323">
												<tr>
													<th class="text-left" style="color: #fff">Product Name</th>
													<th class="text-left" style="color: #fff">Product sub category</th>
													<th class="text-left" style="color: #fff">Part Number</th>
													<th class="text-left" style="color: #fff">Description</th>
													<th class="text-left" style="color: #fff">UOM</th>
													<th class="text-left" style="color: #fff">Quantity</th>
													<th class="text-left" style="color: #fff">Unit Price</th>
													<th class="text-left" style="color: #fff">Total</th>
												</tr>
											</thead>
											<tbody>
												<tr
													v-for="item in quoteData.products"
													:key="item"
													:style="item.is_optional ? 'background: #f2f0c7' : ''"
												>
													<td>
														{{ item.name }}
													</td>

													<td>{{ item.part_number }}</td>

													<td>{{ item.description }}</td>

													<td>{{ item.uom }}</td>

													<td>{{ item.quantity }}</td>

													<td>{{ item.unit_price }}</td>

													<td>{{ item.total }}</td>
												</tr>
											</tbody>
										</v-simple-table>
									</v-card-text>
								</v-card>
							</v-col>
						</v-row>

						<v-row class="fill-height mt-0">
							<v-col cols="8"> </v-col>

							<v-spacer />

							<v-col md="3" cols="12" class="mt-0 pt-0">
								<v-card outlined color="#ecebeb" rounded="0">
									<v-card-content>
										<v-list-item dense>
											<v-list-item-content class="mb-0 pb-0">
												<v-list-item-title>Subtotal</v-list-item-title>
											</v-list-item-content>
											<v-list-item-content class="mb-0 pb-0 text-end pe-4">
												<v-list-item-title class="font-weight-bold"
													>{{ quoteData.to_currency }} {{ quoteData.subtotal }}</v-list-item-title
												>
											</v-list-item-content>
										</v-list-item>
										<v-divider />

										<v-divider v-if="quoteData.tax_label && quoteData.tax" />
										<v-list-item v-if="quoteData.tax_label && quoteData.tax" dense>
											<v-list-item-content class="mb-0 pb-0">
												<v-list-item-title class="text-capitalize">{{ quoteData.tax_label }}</v-list-item-title>
											</v-list-item-content>
											<v-list-item-content class="mb-0 pb-0 text-end pe-4">
												<v-list-item-title class="font-weight-bold"> %{{ quoteData.tax }} </v-list-item-title>
											</v-list-item-content>
										</v-list-item>

										<v-divider v-if="quoteData.discount" />
										<v-list-item dense>
											<v-list-item-content class="mb-0 pb-0">
												<v-list-item-title>Total</v-list-item-title>
											</v-list-item-content>
											<v-list-item-content class="mb-0 pb-0 text-end pe-4">
												<v-list-item-title class="font-weight-bold"
													>{{ quoteData.to_currency }} {{ quoteData.total }}</v-list-item-title
												>
											</v-list-item-content>
										</v-list-item>
									</v-card-content>
								</v-card>
							</v-col>
						</v-row>
					</v-container>
				</div>
			</v-card-text>
		</v-card>-->
	</div>
</template>

<script lang="ts" setup>
</script>

