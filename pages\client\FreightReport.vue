<template>
	<page title="Freight Report" desc="">
		<div>
			<data-table v-if="checkPermission('reports.freight')" ref="dataTable" :columns="columns" api="/v1/portal/reports/freight">
				<template #filter="{ models }">
					<vc-autocomplete
						key="client_id"
						v-model="models.client_id"
						api="/v1/lookups/clients"
						label="Client"
						name="client_id"
						item-text="text"
						item-value="value"
					>
					</vc-autocomplete>

					<vc-autocomplete
						key="country_id"
						v-model="models.country_id"
						api="/v1/lookups/countries"
						label="Country"
						name="countries"
						item-text="text"
						item-value="value"
					>
					</vc-autocomplete>

					<vc-autocomplete
						key="product_category_id"
						v-model="models.product_category_id"
						api="/v1/lookups/product-categories"
						label="Product Category"
						name="product_category_id"
						item-text="text"
						item-value="value"
					>
					</vc-autocomplete>
				</template>

				<template #item.pi_date="{ item }">
					{{ item.pi_date | dateTime }}
				</template>
			</data-table>
			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "Client Name",
					sortable: true,
					value: "client_name",
				},
				{
					text: "PO Number",
					sortable: true,
					value: "po_number",
				},
				{
					text: "Quoted Freight",
					sortable: true,
					value: "quoted_freight",
				},
				{
					text: "Total Freight Cost",
					sortable: true,
					value: "total_freight_cost",
				},
				{
					text: "Freight Profit",
					sortable: true,
					value: "freight_profit",
				},
				{
					text: "Is Acknowledged",
					sortable: true,
					value: "is_acknowledged",
				},
				{
					text: "PI Date",
					sortable: true,
					value: "pi_date",
				},
			],
		};
	},
	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

