function findElByName(elements, name) {
  return (
    elements.find((el) => el.$attrs.name === name) ||
    elements.reduce((acc, el) => {
      return acc || findElByName(el.$children, name);
    }, null)
  );
}

export default {
  data() {
    return {};
  },
  methods: {
    genericErrorHandler(e, ref) {
      console.log(e);
      
      // Clear any existing toasts before showing new ones
      this.$toast.clear();

      if (e.response) {
        const { message } = e.response.data;

        if (e.response.status === 422) {
          const newErrors = Object.values(e.response.data.errors)
            .flat() // Flatten the array to get all error messages
            .join("<br><br>"); // Join multiple error messages with <br>

          this.$toast.error(newErrors.replace(/<br\s*\/?>/gi, "<br>"), {
            title: message,
            // timeout: 7000,
            showClose: true,
            html: true, // Enable HTML rendering if supported by your toast library
          });

          const errors = e.response.data.errors;
          if (ref) {
            for (const key in e.response.data.errors) {
              const el = findElByName(ref.$children, key);
              if (el) {
                el.error = true;
                el.errorMessages = errors[key];

                el.onInput = (e) => {
                  el.error = !!el.errorMessages.length;
                  el.errorMessages = [];

                  el.internalValue = e.target.value;
                  el.badInput = e.target.validity && e.target.validity.badInput;
                };
              }
            }
          }
        } else if (e.response.status === 404) {
          // Handle 404
          // this.$router.push("/404");
        } else if (e.response.status === 500) {
          this.$toast.error(message, { title: "500 Server Error Occurred", timeout: 0, showClose: true });
        } else {
          this.$toast.error(message, { title: `${e.response.status} Error Occurred`, timeout: 0, showClose: true });
        }
      }
    },

    pageErrorHandler(e) {
      this.$store.commit("setStatusCode", e?.response?.status);
      throw e;
    },
  },
};
