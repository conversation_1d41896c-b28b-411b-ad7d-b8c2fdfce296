{"name": "areel-admin", "version": "1.0.0", "private": true, "scripts": {"dev": "nuxt", "build": "nuxt build", "start": "nuxt start", "generate": "nuxt generate", "lint:js": "eslint --ext \".js,.vue\" --ignore-path .gitignore .", "lint:style": "stylelint \"**/*.{css,scss,sass,html,vue}\" --ignore-path .gitignore", "lint:prettier": "prettier --check .", "lint": "npm run lint:js && npm run lint:style && npm run lint:prettier", "lintfix": "prettier --write --list-different . && npm run lint:js -- --fix && npm run lint:style -- --fix", "delete:cache": "rd /s  \"node_modules/.cache\""}, "dependencies": {"@amcharts/amcharts5": "^5.2.21", "@amcharts/amcharts5-geodata": "^5.0.3", "@nuxtjs/auth-next": "5.0.0-1648802546.c9880dc", "@nuxtjs/axios": "^5.13.6", "@nuxtjs/dayjs": "^1.4.1", "@nuxtjs/i18n": "^7.2.2", "@nuxtjs/localtunnel": "^1.1.3", "@nuxtjs/proxy": "^2.1.0", "@tiptap/extension-color": "^2.0.0-beta.207", "@tiptap/extension-text-style": "^2.0.0-beta.207", "apexcharts": "^3.35.4", "body-parser": "^1.20.0", "clone-deep": "^4.0.1", "cookie-universal-nuxt": "^2.2.1", "core-js": "^3.19.3", "css-file-icons": "^0.1.0", "dot-object": "^2.1.4", "dotenv": "^16.0.3", "driver.js": "^0.9.8", "express": "^4.18.1", "fs": "^0.0.1-security", "html2canvas": "^1.0.0-alpha.12", "html2pdf.js": "^0.10.1", "i18n-iso-countries": "^7.5.0", "ibantools": "^4.2.2", "ipx": "^0.9.11", "js-file-download": "^0.4.12", "json-conditions": "^1.3.4", "libphonenumber-js": "^1.10.12", "lodash": "^4.17.21", "mime": "^3.0.0", "mime-db": "^1.52.0", "mime-type": "^4.0.0", "nuxt": "^2.16.2", "nuxt-purgecss": "^1.0.0", "nuxt-route-meta": "^2.3.4", "pdfmake": "^0.2.9", "sortablejs": "^1.15.0", "tiptap-vuetify": "^2.24.0", "v-snackbars": "^3.2.8", "vue": "^2.7.14", "vue-apexcharts": "^1.6.2", "vue-gates": "^2.1.2", "vue-geo-heat-maps": "^0.2.13", "vue-json-pretty": "^1.9.3", "vue-marquee-text-component": "^1.2.0", "vue-orgchart": "^1.1.7", "vue-position-sticky": "^0.2.1", "vue-server-renderer": "^2.6.14", "vue-template-compiler": "^2.6.14", "vue-tooltip-directive": "^1.0.6-1", "vue2-teleport": "^1.0.1", "vuetify": "^2.6.9", "vuetify-customized-fields": "^1.0.11", "vuetify-datetime-picker": "^2.1.1", "vuetify-toast-snackbar-ng": "^0.7.5", "webpack": "^4.46.0"}, "devDependencies": {"@babel/eslint-parser": "^7.16.3", "@faker-js/faker": "^7.4.0", "@nuxt/image": "^0.7.1", "@nuxtjs/eslint-config": "^8.0.0", "@nuxtjs/eslint-module": "^3.0.2", "@nuxtjs/router": "^1.7.0", "@nuxtjs/router-extras": "^1.1.1", "@nuxtjs/stylelint-module": "^4.1.0", "@nuxtjs/vuetify": "^1.12.3", "eslint": "^8.4.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-nuxt": "^3.1.0", "eslint-plugin-vue": "^8.2.0", "postcss-html": "^1.3.0", "prettier": "^2.5.1", "stylelint": "^14.1.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recommended-vue": "^1.1.0", "stylelint-config-standard": "^24.0.0"}}