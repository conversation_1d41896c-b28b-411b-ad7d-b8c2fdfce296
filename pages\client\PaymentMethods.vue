<template>
	<page title="Payment Methods" desc="">
		<template #actions>
			<v-btn v-if="checkPermission('payment_methods.create')" color="success" @click="$refs.crud.new()">
				<v-icon left>mdi-plus</v-icon>Add New</v-btn
			>
		</template>
		<div>
			<data-table v-if="checkPermission('payment_methods.view')" ref="dataTable" :columns="columns" api="/v1/portal/payment-methods">
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn
							v-if="checkPermission('payment_methods.update')"
							v-tooltip="'Edit'"
							small
							icon
							@click="$refs.crud.edit(item.id)"
						>
							<v-icon small>mdi-pencil</v-icon>
						</v-btn>
						<v-btn
							v-if="checkPermission('payment_methods.delete')"
							v-tooltip="'Delete'"
							small
							icon
							@click="$refs.crud.delete(item.id)"
						>
							<v-icon small>mdi-delete</v-icon>
						</v-btn>
					</div>
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>

				<template #item.terms="{ item }">
					<div v-html="item.terms"></div>
				</template>

				<template #item.is_active="{ item }">
					<span v-if="item.is_active === true" small outlined>
						<v-icon color="green">mdi-check</v-icon>
					</span>
					<span v-else small outlined color="red">
						<v-icon color="red">mdi-close</v-icon>
					</span>
				</template>
			</data-table>
			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>

			<crud
				ref="crud"
				v-slot="{ item }"
				width="600"
				:default="defaultItem"
				item-name="Payment Methods"
				api="/v1/portal/payment-methods"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<field-text-field v-model="item.name" name="name" label="Name" :rules="[$rules.required('name')]"></field-text-field>

				<field-wysiwyg v-model="item.terms" name="notes" label="Payment Terms"></field-wysiwyg>

				<v-switch v-model="item.is_active" label="Is Active"></v-switch>
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "Name",
					sortable: true,
					value: "name",
				},
				{
					text: "Terms",
					sortable: true,
					value: "terms",
				},
				{
					text: "Is Active",
					sortable: true,
					value: "is_active",
				},
				{
					text: "Created at",
					sortable: true,
					value: "created_at",
				},

				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],
			defaultItem: {
				id: null,
				name: null,
			},
		};
	},
	computed: {},
	watch: {},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

<style lang="scss">
.ProseMirror {
	min-height: 100px !important;
}
</style>
