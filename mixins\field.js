export default {
	props: {
		value: {
			type: [String, Number, Boolean, Array, Object],
			default: null,
		},
		items: {
			type: [Array],
			default: null,
		},
		api: {
			type: [String],
			default: null,
		},
		rules: {
			type: [Array],
			default: () => [],
		},
		label: {
			type: [String],
			default: null,
		},
	},
	data() {
		return {
			apiItems: [],
		};
	},
	async fetch() {
		if (this.api) {
			this.apiItems = await this.$axios.$getOnce(this.api);
		}
	},
	computed: {
		localValue: {
			get() {
				return this.value;
			},
			set(value) {
				this.$emit("input", value);
			},
		},
		localItems() {
			return this.apiItems?.length > 0 ? this.apiItems : this.items || [];
		},
	},
};
