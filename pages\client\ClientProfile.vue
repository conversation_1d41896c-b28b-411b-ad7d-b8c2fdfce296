<template>
	<v-container>
		<v-row>
			<v-col md="3">
				<v-card>
					<v-card-title>
						<span class="title">Client Details </span>
					</v-card-title>
					<v-card-text>
						<div class="mt-3">
							<span class="body-2 font-weight-black">Name:</span>
							<span class="subtitle-2"> {{ clientDetails.name }}</span>
						</div>
						<div class="mt-3">
							<span class="body-2 font-weight-black">Email:</span>
							<span class="subtitle-2"> {{ clientDetails.email }}</span>
						</div>
						<div class="mt-3">
							<span class="body-2 font-weight-black">Country:</span>
							<span class="subtitle-2"> {{ clientDetails.country?.name }}</span>
						</div>
					</v-card-text>
				</v-card>

				<v-card v-for="(item, i) in contactPeople" :key="i" class="mt-3">
					<v-card-title>
						<span class="title">Contact Name </span> : <span class="title ms-2 text-capitalize">{{ item.name }}</span>
					</v-card-title>
					<v-card-text>
						<div class="mt-3">
							<span class="body-2 font-weight-black">Email One:</span>
							<span class="subtitle-2"> {{ item.email_one }}</span>
						</div>
						<div class="mt-3">
							<span class="body-2 font-weight-black">Email Tow:</span>
							<span class="subtitle-2"> {{ item.email_two }}</span>
						</div>
						<div class="mt-3">
							<span class="body-2 font-weight-black">Phone:</span>
							<span class="subtitle-2"> {{ item.phone }}</span>
						</div>
						<div class="mt-3">
							<span class="body-2 font-weight-black">Mobile:</span>
							<span class="subtitle-2"> {{ item.mobile }}</span>
						</div>
						<div class="mt-3">
							<span class="body-2 font-weight-black">Position:</span>
							<span class="subtitle-2"> {{ item.position }}</span>
						</div>
						<div class="mt-3">
							<span class="body-2 font-weight-black">Address One:</span>
							<span class="subtitle-2"> {{ item.address_one }}</span>
						</div>
						<div class="mt-3">
							<span class="body-2 font-weight-black">Address Tow:</span>
							<span class="subtitle-2"> {{ item.address_two }}</span>
						</div>
					</v-card-text>
				</v-card>
			</v-col>
			<v-col md="9">
				<v-card>
					<v-card-title> Markup / Products List </v-card-title>
					<v-card-text>
						<v-btn small class="float-end mb-3" color="success" @click="ProductDialog = true">
							<v-icon left>mdi-plus</v-icon>Add Product
						</v-btn>

						<v-simple-table>
							<thead>
								<tr>
									<th class="text-left">Name</th>
									<th class="text-left">Code</th>
									<!-- <th class="text-left">Description</th> -->
									<th class="text-left">Markup</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="item in productsList" :key="item.name">
									<td>{{ item.name }}</td>
									<td>{{ item.code }}</td>
									<!-- <td>{{ item.description }}</td> -->
									<td>
										{{ item.client_details.markup }}
										<span v-if="item.client_details.markup_type === 'percentage'" class="caption">%</span>
									</td>
									<td class="d-flex">
										<v-btn v-tooltip="'Edit'" small icon @click="ShowProductDialogMethod(item)">
											<v-icon small>mdi-pencil</v-icon>
										</v-btn>
										<v-btn v-tooltip="'Delete'" small icon @click="DeleteProductDialog(item.id)">
											<v-icon small>mdi-trash-can</v-icon>
										</v-btn>
									</td>
								</tr>
							</tbody>
						</v-simple-table>
					</v-card-text>
				</v-card>
			</v-col>
		</v-row>

		<v-dialog ref="addProductDialog" v-model="ProductDialog" absolute width="600px">
			<v-card>
				<v-card-title>
					<div class="mb-3">Add Product</div>
					<v-spacer></v-spacer>
					<v-btn icon @click="ProductDialog = false">
						<v-icon>mdi-close</v-icon>
					</v-btn>
				</v-card-title>
				<v-card-text>
					<v-form>
						<v-container>
							<vc-select
								v-model="newProductFormData.product_id"
								name="product"
								api="/v1/lookups/products"
								label="product"
								:rules="[$rules.required('product')]"
							></vc-select>

							<v-row>
								<v-col md="4" cols="12">
									<vc-select
										v-model="newProductFormData.markup_type"
										name="markup_type"
										label="Markup Type"
										:items="markupType"
									></vc-select>
								</v-col>
								<v-col md="8" cols="12">
									<v-text-field
										v-model="newProductFormData.markup"
										hide-details
										name="markup"
										label="Markup"
									></v-text-field>
								</v-col>
							</v-row>
						</v-container>
					</v-form>
				</v-card-text>
				<v-card-actions>
					<v-spacer></v-spacer>
					<v-btn text @click="ProductDialog = false">Cancel</v-btn>
					<v-btn color="primary" text @click="addProduct()">Save</v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>
		<v-dialog v-model="ShowProductDialog" absolute width="600px">
			<v-card>
				<v-card-title>
					<div class="mb-3">Add Product</div>
					<v-spacer></v-spacer>
					<v-btn icon @click="ShowProductDialog = false">
						<v-icon>mdi-close</v-icon>
					</v-btn>
				</v-card-title>
				<v-card-text>
					<v-form>
						<v-container>
							<vc-select
								v-model="newProductFormData.product_id"
								name="product"
								api="/v1/lookups/products"
								label="product"
								:rules="[$rules.required('product')]"
							></vc-select>

							<v-row>
								<v-col md="4" cols="12">
									<vc-select
										v-model="newProductFormData.markup_type"
										name="markup_type"
										label="Markup Type"
										:items="markupType"
									></vc-select>
								</v-col>
								<v-col md="8" cols="12">
									<field-text-field v-model="newProductFormData.markup" name="markup" label="Markup"></field-text-field>
								</v-col>
							</v-row>
						</v-container>
					</v-form>
				</v-card-text>
				<v-card-actions>
					<v-spacer></v-spacer>
					<v-btn text @click="ShowProductDialog = false">Cancel</v-btn>
					<v-btn color="primary" text @click="UpdateProductDialogMethod(newProductFormData.product_id)">Save</v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>
	</v-container>
</template>

<script>
export default {
	data() {
		return {
			productsList: [],
			ProductDialog: false,
			ShowProductDialog: false,
			newProductFormData: {},

			productHeaders: [
				{ text: "id", value: "id" },
				{ text: "Name", value: "name" },
				{ text: "Code", value: "code" },
				{ text: "Description", value: "description" },
				{ text: "Markup", value: "markup" },
			],

			markupType: ["amount", "percentage"],

			contactPeople: [
				{
					name: null,
					email_one: null,
					email_two: null,
					phone: null,
					mobile: null,
					fax: null,
					position: null,
					address_one: null,
					address_two: null,
				},
			],

			clientDetails: {
				country: {},
			},
		};
	},

	async fetch() {
		await this.$axios.$get(`/v1/portal/clients/${this.$route.params.id}`).then((resp) => {
			this.clientDetails = resp;
			this.contactPeople = resp.contact_people;
		});

		await this.$axios.$get(`/v1/portal/clients/${this.$route.params.id}/products`).then((resp) => {
			this.productsList = resp;
		});
	},

	methods: {
		addProduct() {
			this.$axios
				.$post(`/v1/portal/clients/${this.$route.params.id}/products`, this.newProductFormData)
				.then((resp) => {
					this.ProductDialog = false;
					this.$toast.success("has been successfully");
					this.$fetch();
				})
				.catch((err) => {
					this.genericErrorHandler(err, this.$refs.addProductDialog);
				});
			//	.catch(this.genericErrorHandler);
		},

		ShowProductDialogMethod(item) {
			this.ShowProductDialog = true;
			this.newProductFormData.markup_type = item.client_details.markup_type;
			this.newProductFormData.markup = item.client_details.markup;
			this.newProductFormData.product_id = item.id;
			console.log("ShowProductDialogMethod", item);
		},

		UpdateProductDialogMethod(productId) {
			this.$axios
				.$put(`/v1/portal/clients/${this.$route.params.id}/products/${productId}`, this.newProductFormData)
				.then((resp) => {
					this.ProductDialog = false;
					this.$toast.success("has been successfully");
					this.$fetch();
				})
				.catch(this.genericErrorHandler);
		},

		DeleteProductDialog(itemId) {
			this.$confirm(null, { title: "Are you sure to delete the item?" }).then((isAccepted) => {
				if (isAccepted) {
					this.$axios
						.$delete(`/v1/portal/clients/${this.$route.params.id}/products/${itemId}`)
						.then((resp) => {
							this.$fetch();
							this.$toast.success("has been successfully");
						})
						.catch(this.genericErrorHandler);
				}
			});
		},
	},
};
</script>
