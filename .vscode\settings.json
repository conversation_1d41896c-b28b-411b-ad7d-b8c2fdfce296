{
  "nuxt.isNuxtApp": true,
  "better-comments.tags": [
    {
      "tag": "!",
      "color": "#FF2D00",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "?",
      "color": "#3498DB",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "//",
      "color": "#474747",
      "strikethrough": true,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "todo",
      "color": "#FF8C00",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "*",
      "color": "#98C379",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "@",
      "color": "#ffffff",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "#2196F3",
      "bold": true,
      "italic": true
    }
  ],
  "i18n-ally.localesPaths": [
    "locales"
  ],
  "i18n-ally.enabledFrameworks": [
    "vue"
  ],
  "i18n-ally.pathMatcher": "{locale}.json",
  "i18n-ally.keystyle": "nested",
  "i18n-ally.extract.autoDetect": true,
  "i18n-ally.displayLanguage": "en",
  "i18n-ally.sourceLanguage": "en",
  "cSpell.words": [
    "Analitics",
    "Idwise",
    "lazyimg",
    "tiktok",
    "walletable"
  ],
  "vetur.format.defaultFormatterOptions": {
    "js-beautify-html": {
      "wrap_attributes": "force-expand-multiline"
    },
    "prettyhtml": {
      "printWidth": 100,
      "singleQuote": false,
      "wrapAttributes": false,
      "sortAttributes": false
    }
  },
  "vetur.format.defaultFormatter.less": "none",
  "vetur.format.defaultFormatter.css": "prettier",
  "vetur.format.defaultFormatter.html": "prettier",
  "vetur.format.defaultFormatter.postcss": "prettier",
  "vetur.format.defaultFormatter.js": "prettier",
  "vetur.format.defaultFormatter.scss": "prettier",
  "vetur.format.defaultFormatter.stylus": "stylus-supremacy",
  "vetur.format.defaultFormatter.ts": "prettier",
  "vetur.format.options.tabSize": 2,
  "vetur.format.options.useTabs": false,
  "vetur.format.scriptInitialIndent": false,
  "vetur.format.styleInitialIndent": false,
  "vetur.underline.refValue": false,
  "vetur.validation.interpolation": false,
  "vetur.validation.script": false,
  "vetur.validation.style": false,
  "vetur.validation.template": false,
  "vetur.validation.templateProps": false,
  "workbench.colorCustomizations": {
    "commandCenter.border": "#e7e7e799",
    // "sash.hoverBorder": "#B71F23",
    // "titleBar.activeBackground": "#B71F23",
    "titleBar.activeForeground": "#e7e7e7",
    // "titleBar.inactiveBackground": "#B71F23",
    "titleBar.inactiveForeground": "#e7e7e799"
  },
  "nuxt.portNumber": 3002,
  "path-intellisense.showHiddenFiles": true,
  "diffEditor.ignoreTrimWhitespace": true,
}