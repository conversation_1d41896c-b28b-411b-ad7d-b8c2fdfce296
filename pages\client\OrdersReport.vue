<template>
	<page title="Orders Report" desc="">
		<div>
			<data-table v-if="checkPermission('reports.orders')" ref="dataTable" :columns="columns" api="/v1/portal/reports/orders">
				<!-- <template #filter="{ models }">
					<vc-autocomplete
						key="client_id"
						v-model="models.client_id"
						api="/v1/lookups/clients"
						label="Client"
						name="client_id"
						item-text="text"
						item-value="value"
					>
					</vc-autocomplete>

					<vc-autocomplete
						key="country_id"
						v-model="models.country_id"
						api="/v1/lookups/countries"
						label="Country"
						name="countries"
						item-text="text"
						item-value="value"
					>
					</vc-autocomplete>

					<vc-autocomplete
						key="product_category_id"
						v-model="models.product_category_id"
						api="/v1/lookups/product-categories"
						label="Product Category"
						name="product_category_id"
						item-text="text"
						item-value="value"
					>
					</vc-autocomplete>
				</template> -->

				<template #item.year="{ item }">
					{{ item.year | dateTime }}
				</template>

				<template #item.client_po_date="{ item }">
					{{ item.client_po_date | dateTime }}
				</template>
			</data-table>
			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "Order Id",
					align: "start",
					value: "order_id",
				},
				{
					text: "Year",
					sortable: true,
					value: "year",
				},
				{
					text: "Reference Number",
					sortable: true,
					value: "reference_number",
				},
				{
					text: "Factory Quote Number",
					sortable: true,
					value: "factory_quote_number",
				},
				{
					text: "Type Of Shipment",
					sortable: true,
					value: "type_of_shipment",
				},
				{
					text: "country",
					sortable: true,
					value: "country",
				},
				{
					text: "Client Name",
					sortable: true,
					value: "client_name",
				},
				{
					text: "Client Po Number",
					sortable: true,
					value: "client_po_number",
				},
				{
					text: "Client Po Date",
					sortable: true,
					value: "client_po_date",
				},
				{
					text: "Product Category",
					sortable: true,
					value: "product_category",
				},
			],
		};
	},
	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

