export default function ({ $axios, redirect, $config, store, req, i18n }) {
	$axios.$getOnce = function (url, config) {
		const fullPath = $axios.getUri({ url, params: config?.params });

		if (store.getters["cache/isPromiseExist"](fullPath)) {
			console.log("cache hit", store.getters["cache/getPromise"](fullPath));
			return store.getters["cache/getPromise"](fullPath);
		}
		if (store.getters["cache/isKeyExist"](fullPath)) {
			console.log("cache hit", Promise.resolve(store.getters["cache/getKey"](fullPath)));
			return new Promise((resolve) => {
				resolve(store.getters["cache/getKey"](fullPath).value);
			});
		} else {
			const promise = this.$get(url, { ...config, headers: { ...config?.headers } }).then((response) => {
				store.dispatch("cache/insertKey", { key: fullPath, value: response });
				return response;
			});

			store.dispatch("cache/insertPromise", { key: fullPath, value: promise });
			return promise;
		}
	};

	$axios.onRequest((apiReq) => {

	
		// apiReq.headers.common["Content-type"] = "application/json; charset=utf-8";
	});

	$axios.onRequestError((e) => {
		// check if error type ExpiredAuthSessionError

		console.log("onRequestError ", JSON.stringify(e));
	});

	$axios.onError((e) => {
		console.log("onError ", e?.response?.data); //   // TODO: this shouldn't be here or at least wrap with process.server
	});

	$axios.onResponse((resp) => {});
	// $axios.onResponseError((error) => {

	// });
}
