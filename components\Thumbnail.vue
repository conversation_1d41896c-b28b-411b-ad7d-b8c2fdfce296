<template>
	<div class="thumbnail py-2">
		<!-- This component is just dummy to activate the image loading -->
		<v-img width="0" height="0" :src="src" @load="imgLoadedHandler"></v-img>
		<!-- End -->
		<v-tooltip open-delay="250" :right="!isRTL" :left="isRTL">
			<template #activator="{ on }">
				<v-skeleton-loader
					:width="width + 2"
					:height="height + 2"
					:loading="loading"
					type="image"
					v-on="on"
					@click="showModel = true"
				>
					<v-card outlined rounded :width="width">
						<v-img class="rounded" eager :width="width" :src="src" :aspect-ratio="aspectRatio" @load="imgLoadedHandler"></v-img>
					</v-card>
				</v-skeleton-loader>
			</template>
			<v-img class="rounded mt-2" :lazy-src="src" :src="src" :width="previewWidth" :aspect-ratio="aspectRatio" v-on="on"></v-img>
			<div class="text-center text-subtitle-1 py-2" :style="{ width: previewWidth + 'px' }">{{ previewTitle }}</div>
		</v-tooltip>
		<v-dialog v-model="showModel">
			<dialog-close v-model="showModel" class="thumbnail-close-modal" />
			<slot v-bind="{ src }">
				<v-img contain class="rounded" :src="src"></v-img>
			</slot>
		</v-dialog>
	</div>
</template>

<script>
export default {
	props: {
		src: {
			type: String,
			required: true,
		},
		width: {
			type: Number,
			default: 100,
		},
		aspectRatio: {
			type: Number,
			default: 16 / 9,
		},
		previewTitle: {
			type: String,
			default: "",
		},
	},
	data() {
		return {
			showModel: false,
			loading: true,
			previewWidth: 400,
			on: null,
		};
	},
	computed: {
		height() {
			return this.width / this.aspectRatio;
		},
	},
	methods: {
		imgLoadedHandler() {
			this.loading = false;
		},
	},
};
</script>

<style lang="scss">
.thumbnail {
	cursor: pointer;
}
.thumbnail-close-modal {
	position: fixed !important;
	top: 42px;
	@include dir("right", "42px", "unset");
	@include dir("left", "unset", "42px");
	z-index: 1;
}
</style>
