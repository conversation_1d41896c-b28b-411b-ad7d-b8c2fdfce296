export default {
	props: {
		lang: {
			type: Object,
			default: () => ({}),
		},
		value: {
			type: String,
			default: "",
		},
		disabled: {
			type: Boolean,
			default: true,
		},
	},
	data() {
		return {
			currentIndex: null,
		};
	},
	computed: {
		localValue: {
			get() {
				return this.value;
			},
			set(value) {
				this.$emit("input", value);
			},
		},
	},
	methods: {
		setValue(index, value) {
			this.localValue = value;
		},
		inputIndex(index) {
			this.currentIndex = index;
		},
		insertPlaceHolder(value) {
			const inputComponent = this.$refs.containedRef;
      this.addLabel(inputComponent, value);
		},
		addLabel(inputComponent, value) {
      if(!inputComponent) { return }

			const sentence = inputComponent.value ?? "";
			let pos = inputComponent.selectionStart;
			if (pos === undefined) {
				pos = 0;
			}

			const len = sentence.length;
			const after = sentence.substr(pos, len);
			const before = sentence.substr(0, pos);

			/* eslint-disable-next-line */
			const finalValue = `${before} {${value}} ${after}`;
			// inputComponent.$emit("input", before + value + after);
			this.setValue(this.currentIndex, finalValue);
		},
	},
};
