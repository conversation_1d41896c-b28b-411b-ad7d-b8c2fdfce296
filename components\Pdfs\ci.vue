<template>
	<div id="pi" style="background: #fff">
		<v-container class="pa-8" style="padding: 20px">
			<v-row>
				<v-col md="6">
					<v-img src="/logo.png" width="350px" style="width: 250px"></v-img>
				</v-col>
				<v-col md="6">
					<div class="text-end" style="text-align: right">
						<div style="font-weight: bold; font-size: 1.6rem">Invoice</div>
						<div style="font-weight: bold">NO:{{ extraData.client_po_number }}</div>
						<div style="font-weight: bold">
							Date:
							{{ extraData.client_po_date | date }}
						</div>
						<!-- <div style="font-weight: bold">
							TRN: XXX
						</div> -->
					</div>
				</v-col>
			</v-row>

			<v-divider class="my-3" />

			<v-row class="relative">
				<v-col md="6" cols="12">
					<div>
						<div style="margin-bottom: 8px">
							<h4 class="font-weight-bold" style="background: #ebebeb; padding-left: 3px; margin-right: 5px">PROJECT</h4>
							<span class="d-inline-block coffee-3--text body-2">{{ extraData.quote?.project_name }}</span>
						</div>
						<div style="margin-bottom: 5px; margin-top: 5px">
							<h5 class="d-inline-block me-2">P.O Ref. #:</h5>
							<span class="d-inline-block coffee-3--text body-2">{{ extraData.client_po_number }} </span>
						</div>
						<div style="display: flex; align-items: center; margin-bottom: 2px">
							<h5 class="d-inline-block me-2">P.O Date:</h5>
							<span class="d-inline-block coffee-3--text body-2">{{ extraData.client_po_date | date }} </span>
						</div>
					</div>
				</v-col>
				<v-col md="6" cols="12">
					<div>
						<div style="margin-bottom: 25px">
							<div style="margin-bottom: 8px">
								<h4 class="font-weight-bold" style="background: #ebebeb; padding-left: 3px; margin-right: 5px">PRODUCT</h4>
								<span class="d-inline-block coffee-3--text body-2">{{ extraData.quote?.product_category?.name }}</span>
							</div>

							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Quote Ref. #:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ extraData.quote?.reference_number }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Quote Date:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ extraData.quote?.valid_until_date | date }}</span>
							</div>
						</div>
					</div>
				</v-col>
				<v-col md="6" cols="12">
					<div>
						<div style="margin-bottom: 25px; margin-top: 5px">
							<h4 class="font-weight-bold" style="background: #ebebeb; padding-left: 3px; margin-right: 5px">BILL TO</h4>
							<div class="d-inline-block coffee-3--text body-2">{{ extraData.quote?.client?.name }}</div>
							<div class="d-inline-block coffee-3--text body-2">{{ extraData.quote?.client?.email }}</div>

							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Attention of:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ extraData.bill_to_data?.name }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Phone:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ extraData.bill_to_data?.phone }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Mobile:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ extraData.bill_to_data?.mobile }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Fax:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ extraData.bill_to_data?.fax }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Email:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ extraData.bill_to_data?.email }}</span>
							</div>
						</div>
					</div>
				</v-col>

				<v-col md="6" cols="12">
					<div>
						<div style="margin-bottom: 25px; margin-top: 5px">
							<h4 class="font-weight-bold" style="background: #ebebeb; padding-left: 3px; margin-right: 5px">SHIP TO</h4>

							<div class="d-inline-block coffee-3--text body-2">{{ extraData.quote?.client?.name }}</div>
							<div class="d-inline-block coffee-3--text body-2">{{ extraData.quote?.client?.email }}</div>

							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Attention of:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ extraData.ship_to_data?.name }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Phone:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ extraData.ship_to_data?.phone }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Mobile:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ extraData.ship_to_data?.mobile }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Fax:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ extraData.ship_to_data?.fax }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Email:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ extraData.ship_to_data?.email }}</span>
							</div>
						</div>
					</div>
				</v-col>

				<v-col cols="12">
					<v-card outlined>
						<v-card-text>
							<v-simple-table class="border">
								<thead style="background: #232323">
									<tr>
										<!-- <th class="text-left" style="color: #fff">All Quantity</th> -->
										<th class="text-left" style="color: #fff">Qty Ordered</th>
										<th class="text-left" style="color: #fff">Readiness Date</th>
										<th class="text-left" style="color: #fff">Item Description</th>
										<th class="text-left" style="color: #fff">Item Price</th>
										<th class="text-left" style="color: #fff">UOM</th>
										<th class="text-left" style="color: #fff">Extended Price</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="item in pdfData.order?.quote?.products" :key="item.id">
										<template v-if="!item.is_optional && item.acknowledge_quantity">
											<!-- <td>{{ item.quantity }}</td> -->
											<td>{{ item.acknowledge_quantity }}</td>
											<td>{{ pdfData.delivery_to_client_at | date }}</td>
											<td>{{ item.description }}</td>
											<td>
												{{
													extraData.quote?.client_currency?.symbol
														? extraData.quote?.client_currency?.symbol
														: "USD"
												}}
												{{ item.unit_price }}
											</td>
											<td>{{ item.uom }}</td>
											<td>
												{{
													extraData.quote?.client_currency?.symbol
														? extraData.quote?.client_currency?.symbol
														: "USD"
												}}
												{{ item.unit_price * item.acknowledge_quantity }}
											</td>
										</template>
									</tr>
								</tbody>
							</v-simple-table>
						</v-card-text>
					</v-card>
				</v-col>
			</v-row>

			<v-row class="fill-height mt-0">
				<v-col cols="8">
					<h4 class="font-weight-bold mt-4">Accounts Details:</h4>
					<div class="d-flex">
						<div v-for="(account, i) in bankAccounts" :key="i" class="mt-2 me-10">
							<div>
								<h5 class="font-weight-bold mb-3 me-2 d-inline">Accounts Name:</h5>
								<span>{{ account.meta?.account_name }}</span>
							</div>
							<div>
								<h5 class="font-weight-bold d-inline me-2 mb-3">Bank</h5>
								<span>{{ account.meta?.bank_name }}</span>
							</div>

							<div>
								<h5>{{ account.meta?.account_name }}</h5>
								<div class="body-2"><b>IBAN: </b>{{ account.meta?.iban }}</div>
								<div class="body-2"><b>Swift Code: </b>{{ account.meta?.swift_code }}</div>
								<div class="body-2"><b>Account Number: </b>{{ account.text }}</div>
								<div class="body-2"><b>Branch Name: </b>{{ account.meta?.branch_name }}</div>
								<div class="body-2"><b>Currency: </b>{{ account.meta?.currency_symbol }}</div>
							</div>
						</div>
					</div>
				</v-col>

				<v-spacer />

				<v-col md="3" cols="12" class="mt-0 pt-0">
					<v-card outlined color="#ecebeb" rounded="0">
						<v-card-text>
							<v-list-item dense>
								<v-list-item-content class="mb-0 pb-0">
									<v-list-item-title>Subtotal</v-list-item-title>
								</v-list-item-content>
								<v-list-item-content class="mb-0 pb-0 text-end pe-4">
									<v-list-item-title class="font-weight-bold">
										{{ extraData.quote?.client_currency?.symbol ? extraData.quote?.client_currency?.symbol : "USD" }}
										{{ totalPrice }}
									</v-list-item-title>
								</v-list-item-content>
							</v-list-item>
							<v-divider />

							<v-divider v-if="pdfData.discount" />
							<v-list-item dense>
								<v-list-item-content class="mb-0 pb-0">
									<v-list-item-title>Total</v-list-item-title>
								</v-list-item-content>
								<v-list-item-content class="mb-0 pb-0 text-end pe-4">
									<v-list-item-title class="font-weight-bold">
										{{ extraData.quote?.client_currency?.symbol ? extraData.quote?.client_currency?.symbol : "USD" }}
										{{ totalPrice }}
									</v-list-item-title>
								</v-list-item-content>
							</v-list-item>
						</v-card-text>
					</v-card>
          <!-- <p class="mt-3 font-weight-bold">{{ extraData.quote?.total_spell }}</p> -->
				</v-col>
			</v-row>
		</v-container>
	</div>
</template>

<script>
export default {
	props: {
		pdfData: {
			type: Object,
			default: () => ({}),
		},
		extraData: {
			type: Object,
			default: () => ({}),
		},
	},

	data() {
		return {
			bankAccounts: [],
		};
	},

	computed: {
		totalPrice() {
			let total = 0;
			if (this.pdfData.order?.quote?.products) {
				for (const item of this.pdfData.order.quote.products) {
					total += item.unit_price * item.acknowledge_quantity;
				}
			}
			return total;
		},
	},

	mounted() {
		this.$axios.get(`/v1/lookups/bank-accounts?country_id=${this.extraData.quote?.country_id}&branch_id=${this.extraData.quote?.branch_id}`).then((resp) => {
			this.bankAccounts = [];
			this.bankAccounts = resp.data;
		});
	},
};
</script>




