<template>
	<page :title="`Products for ` + subCategoryName" desc="">
		<template #actions>
			<v-btn v-if="checkPermission('products.create')" color="success" @click="$refs.crud.new()">
				<v-icon left>mdi-plus</v-icon>Add Product
			</v-btn>
		</template>
		<div>
			<data-table
				v-if="checkPermission('products.view')"
				ref="dataTable"
				:columns="columns"
				:api="`/v1/portal/products?sub_category_id=${$route.params.sub_category_id}`"
			>
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn
							v-if="checkPermission('products.delete')"
							v-tooltip="'Delete'"
							small
							icon
							@click="$refs.crud.delete(item.id)"
						>
							<v-icon small>mdi-delete</v-icon>
						</v-btn>
						<v-btn v-if="checkPermission('products.update')" v-tooltip="'Edit'" small icon @click="$refs.crud.edit(item.id)">
							<v-icon small>mdi-pencil</v-icon>
						</v-btn>
						<!-- <v-btn
							v-tooltip="'Show product details in factories'"
							icon
							small
							:to="localePath({ name: 'product-in-factories', params: { id: item.id } })"
						>
							<v-icon small>mdi-factory</v-icon>
						</v-btn> -->
					</div>
				</template>

				<template #item.price="{ item }">
					{{ item.price }}
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>
			</data-table>
			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>

			<crud
				ref="crud"
				v-slot="{ item }"
				width="400"
				:default="defaultItem"
				item-name="Product"
				api="/v1/portal/products?ids=sub_category"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<field-text-field
					v-model="item.name"
					name="name"
					label="Product Name"
					:rules="[$rules.required('name')]"
				></field-text-field>

				<!-- <vc-select
					v-model="item.sub_category_id"
					name="sub_category_id"
					api="/v1/lookups/product-sub-categories"
					label="Product Sub category"
					:rules="[$rules.required('Product Sub category')]"
				></vc-select> -->

				<!-- <div v-if="isEdit">
					<div class="my-4">
						<strong>Product details in factories</strong>
					</div>

					<div v-for="(field, i) in item.factories" :key="field">
						<div>
							<vc-select
								:key="i + 'key'"
								v-model="field.factory_id"
								name="factory_id"
								api="/v1/lookups/factories"
								label="Factory"
								:rules="[$rules.required('Factory')]"
							></vc-select>
							<v-text-field :key="i + 'value'" v-model="field.price" label="price" type="number"> </v-text-field>
							<v-text-field :key="i + 'value'" v-model="field.code" label="code"> </v-text-field>
							<v-text-field :key="i + 'value'" v-model="field.description" label="description"> </v-text-field>
							<v-text-field :key="i + 'value'" v-model="field.quantity" label="quantity"> </v-text-field>

							<v-btn icon small class="float-end ms-1" color="primary" @click="item.factories.splice(i, 1)">
								<v-icon>mdi-trash-can</v-icon>
							</v-btn>
						</div>
					</div>

					<v-btn
						v-tooltip="'add new'"
						icon
						class="float-end"
						@click="item.factories.push({ factory_id: null, price: null, code: null, description: null, quantity: null, })"
					>
						<v-icon>mdi-plus</v-icon>
					</v-btn>
				</div> -->
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "Product Name",
					sortable: true,
					value: "name",
				},
				{
					text: "Created at",
					sortable: true,
					value: "created_at",
				},
				{
					text: "Updated at",
					sortable: true,
					value: "updated_at",
				},
				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],
			defaultItem: {
				name: null,
				sub_category_id: this.$route.params.sub_category_id,

				// factories: [
				// 	{
				// 		factory_id: null,
				// 		price: null,
				// 		name: null,
				// 		code: null,
				// 		description: null,
				// 		quantity: null,
				// 	},
				// ],
			},
			subCategoryName: null,
		};
	},
	computed: {},
	watch: {},

	created() {
		this.$axios.$get(`/v1/lookups/product-sub-categories`).then((resp) => {
			const productSubCategories = resp;

			// Find the category that matches the route param
			const matchingCategory = productSubCategories.find(
				(category) => category.value === parseInt(this.$route.params.sub_category_id)
			);

			if (matchingCategory) {
				this.subCategoryName = matchingCategory.text;
			}
		});
	},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

