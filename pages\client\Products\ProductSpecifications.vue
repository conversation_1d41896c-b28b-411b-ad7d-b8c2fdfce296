<template>
	<page title="Product Specifications" desc="">
		<template #actions>
			<v-btn color="success" @click="$refs.crud.new()"> <v-icon left>mdi-plus</v-icon>Add Specification</v-btn>
		</template>
		<div>
			<data-table ref="dataTable"  :columns="columns" api="/v1/portal/product-specifications">
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="'Delete'" small icon @click="$refs.crud.delete(item.id)">
							<v-icon small>mdi-delete</v-icon>
						</v-btn>
						<v-btn v-tooltip="'Edit'" small icon @click="$refs.crud.edit(item.id)">
							<v-icon small>mdi-pencil</v-icon>
						</v-btn>
					</div>
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>
			</data-table>

			<crud
				ref="crud"
				v-slot="{ item }"
				width="400"
				:default="defaultItem"
				item-name="Country"
				api="/v1/portal/product-specifications"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
			<field-text-field
				v-model="item.name"
				name="name"
				label="Specification Name"
				:rules="[$rules.required('name')]"
			></field-text-field>

			
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "Specification Name",
					sortable: true,
					value: "name",
				},
			    {
			    	text: "Created at",
			    	sortable: true,
			    	value: "created_at",
			    },
			    {
			    	text: "Updated at",
			    	sortable: true,
			    	value: "updated_at",
			    },
				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],
			defaultItem: {
				id: null,
           		name: null,
			},
		};
	},
	computed: {},
	watch: {},
	
	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

