export const state = () => ({
	myAccount:{
		role:{},
	},
	collectionErrors: {},
	initialDataLoaded: false,

});

export const getters = {
	isAnyCollectionError(state) {
		return Object.keys(state.collectionErrors).length > 0;
	},
	isInitialDataLoaded(state) {
		return state.initialDataLoaded;
	},
};

export const mutations = {
	setCollectionItem(state, { key, item }) {
		state[key] = item;
	},
	setCollectionError(state, { key, error }) {
		state.collectionErrors[key] = error;
	},
	setInitialDataStatus(state, payload) {
		state.initialDataLoaded = payload;
	},
};

export const actions = {
	getCollection({ commit, state }, params) {
		for (const key in params) {
			params[key] = this.$to.base64(params[key]);
		}
		// params.anas = true;
		return this.$axios
			.$get("/v1/collection", { params })

			.catch((error) => {
				console.error("Store Collection:", error.response);
			});
	},
};
