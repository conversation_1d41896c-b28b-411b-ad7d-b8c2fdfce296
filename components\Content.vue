<template>
	<div class="content-container relative">
		<v-progress-linear
			v-if="!hideLoader"
			v-show="$fetchState.pending"
			active
			:indeterminate="$fetchState.pending"
			absolute
		></v-progress-linear>
		<div class="d-flexn">
			<advanced-filter
				v-if="$scopedSlots.filter"
				v-model="filterDrawerModel"
				:width="filterWidth"
				:filter-data="models"
				@filter="filter"
				@reset="resetFilter"
			>
				<slot name="filter" v-bind="bindObject"></slot>
			</advanced-filter>

			<v-sheet class="flex-grow-1" :class="{ fullscreen: isFullscreen }">
				<v-toolbar v-if="!hideToolbar" flat>
					<v-badge v-if="$scopedSlots.filter" dot :value="isAnyFiltration" overlap class="me-2">
						<v-btn v-tooltip="'Advanced Filter'" icon width="32" height="32" @click="filterDrawerModel = !filterDrawerModel">
							<v-icon>mdi-filter-cog-outline</v-icon>
						</v-btn>
					</v-badge>
					<v-text-field
						v-model="models.search"
						chips
						multiple
						single-line
						hide-details=""
						clearable
						placeholder="Search"
						append-icon="mdi-magnify"
						style="max-width: 350px"
						autofocus
						@input="search"
						@keyup.enter="filter"
						@click:append="filter"
						@keydown.ctrl.c="clearSearch"
					>
					</v-text-field>
					<v-spacer />
					<slot name="prepend-action" v-bind="bindObject" />
					<v-btn v-tooltip="'Fullscreen'" class="me-2" icon width="32" height="32" @click="isFullscreen = !isFullscreen">
						<v-icon v-if="isFullscreen">mdi-fullscreen-exit</v-icon>
						<v-icon v-else>mdi-fullscreen</v-icon>
					</v-btn>

					<v-menu offset-y>
						<!-- <template #activator="{ on }">
							<v-btn
								v-tooltip="'Save Filter'"
								class="me-2"
								icon
								width="32"
								height="32"
								v-on="on"
							>
								<v-icon>mdi-filter-plus-outline</v-icon>
							</v-btn>
						</template> -->
						<v-card>
							<v-list-item v-for="item in savedFilters" :key="item" link dense @click="loadFilter(item)">
								<v-list-item-content>
									<v-list-item-title>{{ item.name }} </v-list-item-title>
								</v-list-item-content>
								<v-list-item-action>
									<v-menu offset-y>
										<template #activator="{ on }">
											<v-btn small icon v-on="on"><v-icon>mdi-dots-vertical</v-icon></v-btn>
										</template>
										<v-card>
											<v-list-item dense link @click="deleteFilter(item)">
												<v-list-item-icon>
													<v-icon>mdi-delete</v-icon>
												</v-list-item-icon>
												<v-list-item-title> Delete </v-list-item-title>
											</v-list-item>
										</v-card>
									</v-menu>
								</v-list-item-action>
							</v-list-item>
							<v-list-item v-if="!savedFilters.length">
								<v-list-item-content>
									<v-list-item-subtitle class="text-center">You don't have any filter</v-list-item-subtitle>
								</v-list-item-content>
							</v-list-item>
							<v-divider></v-divider>
							<v-list-item dense @click="filterNameDialogModel = true">
								<v-list-item-icon><v-icon>mdi-content-save</v-icon></v-list-item-icon>
								<v-list-item-content>
									<v-list-item-title>Save Current Filter </v-list-item-title>
								</v-list-item-content>
							</v-list-item>
						</v-card>
					</v-menu>
					<v-btn v-tooltip="'Refresh'" :loading="$fetchState.pending" class="me-2" icon width="32" height="32" @click="refresh">
						<v-icon>mdi-refresh</v-icon></v-btn
					>
					<slot name="append-action" v-bind="bindObject" />
					<!-- <v-menu>
								<template #activator="{ on }"> -->
					<!-- <v-btn class="me-2" text height="32"> <v-icon left>mdi-export</v-icon>Export</v-btn> -->
					<!-- </template>
								<v-card>
									<v-list dense>
										<v-list-item-group v-model="selectedColumns" multiple>
											<v-list-item
												v-for="(col, i) in selectableColumns"
												:key="'col' + col.value"
												:disabled="selectedColumns.length === 1 && selectedColumns.includes(i)"
											>
												<template #default="{ active }">
													<v-list-item-action>
														<v-checkbox :input-value="active"></v-checkbox>
													</v-list-item-action>

													<v-list-item-content>
														<v-list-item-title>{{ col.text }}</v-list-item-title>
													</v-list-item-content>
												</template>
											</v-list-item>
										</v-list-item-group>
									</v-list>
								</v-card>
							</v-menu> -->

					<!-- <v-btn plain color="primary" small @click="filterDrawerModel = !filterDrawerModel">Advanced</v-btn> -->
				</v-toolbar>

				<!-- <slot name="no-data" v-if="!$fetchState.pending && !items.length" v-bind="bindObject">
					<v-alert type="info" class="mx-4" text>{{ $t("common.no-data-available") }}</v-alert>
				</slot> -->
				<div class="pb-4">
					<slot v-bind="bindObject" />
				</div>
				<div v-if="!hidePagination" class="pb-4">
					<slot v-bind="bindObject" name="pagination">
						<v-pagination
							v-show="!$fetchState.pending && pagination.pages > 1"
							v-model="pagination.page"
							:length="pagination.pages"
							total-visible="10"
							@input="paginationHandler({ page: $event, itemsPerPage: pagination.per_page })"
						></v-pagination>
					</slot>
				</div>
			</v-sheet>
		</div>
		<v-dialog v-if="!hideToolbar" v-model="filterNameDialogModel" max-width="400">
			<v-form ref="filterNameForm">
				<v-card>
					<v-card-title>Save Filter</v-card-title>
					<v-card-text>
						<v-text-field v-model="filterName" :rules="[$rules.required('filter Name')]" label="Filter Name"></v-text-field>
					</v-card-text>
					<v-card-actions>
						<v-spacer></v-spacer>
						<v-btn text @click="filterNameDialogModel = false">Cancel</v-btn>
						<v-btn text color="primary" @click="saveFilter">Save</v-btn>
					</v-card-actions>
				</v-card>
			</v-form>
		</v-dialog>
	</div>
</template>

<script>
import responsive from "~/mixins/responsive";
let controller;

export default {
	mixins: [responsive],
	props: {
		columns: {
			type: Array,
			default: () => [],
		},

		filterWidth: {
			type: [Number, String],
			default: 300,
		},
		api: {
			type: String,
			default: null,
		},
		demo: {
			type: Boolean,
			default: null,
		},

		itemClass: {
			type: [Function, String],
			default: undefined,
		},
		ignoreModelsBadge: {
			type: Array,
			default: () => [],
		},
		hideToolbar: {
			type: Boolean,
			default: false,
		},
		hideLoader: {
			type: Boolean,
			default: false,
		},
		hidePagination: {
			type: Boolean,
			default: false,
		},
		paginationHistory: {
			type: Boolean,
			default: true,
		},
		perPage: {
			type: [Number, String],
			default: 16,
		},
		getItemsMap: {
			type: Function,
			default: (items) => items,
		},
		itemsKey: {
			type: String,
			default: "items",
		},
		paginationKey: {
			type: String,
			default: "pagination",
		},
		filtersKey: {
			type: String,
			default: "filters",
		},
	},
	data() {
		return {
			items: [],
			filterDrawerModel: false,
			parsedFilter: [],
			searchTimerId: null,
			// filterData: {
			// 	...deserialize(this.$route.query),
			// },
			filterOptions: {},
			isFullscreen: false,
			selectedColumns: [],
			pagination: {
				total: 0,
				count: 0,
				per_page: this.perPage,
				page: 1,
			},
			itemsPerPageOptions: [15, 30, 50, 100, 500],
			// filterValues
			// filterValues: { ...this.$route.query },
			filterObject: {},
			models: { ...this.$route.query },
			// savedFilters: ["roles to be reviewed", "departments with no roles"],
			filterName: null,
			filterNameDialogModel: false,
		};
	},
	async fetch() {
		controller = new AbortController();
		if (!this.api) {
			this.$toast.debug("No API provided");
			return;
		}
		const paginationQuery = { per_page: this.pagination.per_page, page: this.pagination.page };
		const apiQuery = this.trimQuery({ ...paginationQuery, ...this.$route.query });

		await this.$axios
			.$get(this.api, {
				params: apiQuery,
				signal: controller.signal,
			})
			.then((resp) => {
				this.items = this.getItemsMap(this.itemsKey ? resp[this.itemsKey] : resp);
				this.$emit("update:items", this.items);
				if (this.paginationKey ? resp[this.paginationKey] : resp) {
					this.pagination = resp[this.paginationKey];
				}
				if (this.filtersKey ? resp[this.filtersKey] : resp) {
					this.filterObject = resp[this.filtersKey];
					this.$set(this, "models", this.filterToModel(this.filterObject));
				}
			})
			.catch((error) => {
				this.genericErrorHandler(error);
			});
	},
	computed: {
		selectableColumns() {
			return this.columns.filter((column, index) => !!column.text);
		},
		apiURL() {
			return this.api || this.$route.meta.api;
		},
		options() {
			const keys = Object.keys(this.filterObject);
			const options = {};
			for (const key of keys) {
				options[key] = this.filterObject[key].options;
			}
			return options;
		},
		trimmedModels() {
			return this.trimQuery(this.models);
		},
		trimmedModelsKeys() {
			return Object.keys(this.trimmedModels);
		},
		isAnyFiltration() {
			// return this.trimmedModelsKeys.filter((key) => !this.ignoreModelsBadge.includes(key)).length > 0;
			return null;
		},
		bindObject() {
			return {
				loading: this.$fetchState.pending,
				paginationHandler: this.paginationHandler,
				pagination: this.pagination,
				items: this.items,
				models: this.models,
				filter: this.filter,
				refresh: this.refresh,
				options: this.options,
				isFullscreen: this.isFullscreen,
			};
		},
		browserQuery() {
			const models = this.$cloneDeep(this.models);

			const routeQuery = this.$cloneDeep(this.$route.query);

			const browserQuery = this.trimQuery({ ...routeQuery, ...models });

			if (this.paginationHistory) {
				if (this.pagination.per_page !== this.perPage) {
					browserQuery.per_page = this.pagination.per_page;
				}
				if (Number(this.pagination.page) !== 1) {
					browserQuery.page = this.pagination.page;
				} else {
					browserQuery.page = undefined;
				}
			}
			return browserQuery;
		},
		savedFilters() {
			// return this.getPreferencesByRoute.content?.filters || [];
			return [];
		},
	},

	watch: {
		// "$route.query": {
		// 	deep: true,
		// 	handler(q) {
		// 		for (const key in q) {
		// 			if (this.filterObject[key]) {
		// 				this.models[key] = q[key];
		// 			}
		// 		}
		// 	},
		// },
	},
	mounted() {
		// close full screen when esc key clicked
		this.$nextTick(() => document.addEventListener("keydown", (e) => this.escKeyHandler(e)));
	},
	beforeDestroy() {
		document.removeEventListener("keydown", (e) => this.escKeyHandler(e));
	},
	methods: {
		async resetFilter() {
			this.models = {};
			await this.$router.push({ query: {} });
			this.filter();
		},
		refresh() {
			this.$fetch();
			this.$emit("refresh");
		},
		async filter() {
			const browserQuery = this.$cloneDeep(this.browserQuery);
			const url = this.$router.resolve({
				params: { ...this.$route.params },
				query: browserQuery,
			}).href;

			if (this.$route.fullPath !== url) {
				await this.$router.push(url);
			}
			this.$fetch();
		},
		paginationHandler(newPaginationData) {
			console.log(this.pagination.page, newPaginationData.page, this.pagination.per_page, newPaginationData.itemsPerPage);
			// prevent double request
			if (this.pagination.page === newPaginationData.page && this.pagination.per_page === newPaginationData.itemsPerPage) return;

			this.pagination.page = newPaginationData.page;
			this.pagination.per_page = newPaginationData.itemsPerPage;
			const animationPeriod = 300;
			try {
				this.$vuetify.goTo(".content-container", { duration: animationPeriod, offset: 70, easing: "easeInOutQuad" });
			} catch (error) {}

			setTimeout(() => {
				this.filter();
			}, animationPeriod);
		},
		trimQuery(q) {
			// remove null and undefined values
			return Object.fromEntries(Object.entries(q).filter(([key, value]) => value !== null && value !== undefined && value !== ""));
		},
		clearSearch() {
			this.models.search = null;
			this.filter();
		},
		search() {
			clearTimeout(this.searchTimerId);
			controller.abort();
			// if (!this.models.search) return;
			// delay new call 500ms
			this.searchTimerId = setTimeout(() => {
				this.filter();
			}, 500);
		},
		filterToModel(filterObject) {
			const filterObjectValues = {};
			for (const key in filterObject) {
				filterObjectValues[key] = filterObject[key].value;
			}
			return filterObjectValues;
		},
		escKeyHandler(e) {
			if (e.key === "Escape") {
				this.isFullscreen = false;
			}
		},
		saveFilter() {
			if (!this.$refs.filterNameForm.validate()) {
				return;
			}
			// this.savedFilters.unshift(this.filterName);
			this.setPreferencesByRoute([`content.filters`, [...this.savedFilters, { name: this.filterName, value: this.filterObject }]]);
			this.filterName = null;
			this.$refs.filterNameForm.reset();
			this.filterNameDialogModel = false;
		},
		loadFilter(filter) {
			this.models = this.filterToModel(filter.value);
			this.filter();
		},
		deleteFilter(filter) {
			this.$confirm(this.$t("messages.confirm-delete-text"), { title: this.$t("messages.confirm-delete-title") }).then(
				(isAccepted) => {
					if (isAccepted) {
						this.setPreferencesByRoute([`content.filters`, this.savedFilters.filter((f) => f.name !== filter.name)]);
					}
				}
			);
		},
	},
};
</script>
<style lang="scss">
.fullscreen {
	// padding-top: 8px;
	position: fixed;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	overflow-y: hidden;
	z-index: 5;
}
</style>
