<template>
	<v-app :dark="$vuetify.theme.dark">
		<toasts id="toast" />
		<div class="relative fill-height">
			<v-sheet :class="contentClass" color="background" class="fill-height">
				<slot />
			</v-sheet>
		</div>
	</v-app>
</template>

<script>
export default {
	name: "Base",

	props: {
		contentClass: {
			type: [String, Array, Object],
			default: null,
		},
	},
	data() {
		return {
			nuxtServerInit: false,
			isInitiating: false,
		};
	},
	head() {
		return {
			link: [
				{
					rel: "stylesheet",
					href: `/fonts/en.css`,
				},
			],
		};
	},

	created() {
		// this.$vuetify.theme.dark = this.storage?.theme === "dark";
	},
	mounted() {
		window.onNuxtReady((app) => {
			this.$vuetify.theme.dark = this.storage?.theme === "dark";
		});

		window.addEventListener(
			"keydown",
			function (e) {
				// console.log(e);
				if (e.key === "F8") {
					/* eslint-disable-next-line */
					debugger;
				}
			},
			false
		);

		// this.$store.dispatch("nuxtServerInit", this.$nuxt.context);
	},
};
</script>
