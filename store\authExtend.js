import dot from "dot-object";
export const state = () => ({
	permissions: {},
	preferences: {
		// routeName.componentName.propertyName	: value
		global: {},
	},
	isSaving: false,
	stat: {},
});

// export const getters = {
// 	getPreferencesByRoute: (state) => (routeName) => {
// 		return state.preferences[routeName] || {};
// 	},
// 	profileStrength: (state) => {
// 		return state.stat.profile_strength;
// 	},
// };
export const mutations = {
	setPermissions(state, permissions) {
		// convert array to object
		const permissionsAsObject = {};
		permissions.forEach((p) => {
			permissionsAsObject[p] = {};
		});
		const permissionsResult = dot.object(permissionsAsObject);
		state.permissions = permissionsResult;
	},
	// setPreferences(state, preferences) {
	// 	state.preferences = preferences;
	// },
	setPreferences(state, preferences) {
		// convert dot notation to object

		const preferencesResult = dot.object(preferences);
		// merge the new preferences with the existing preferences
		state.preferences = { ...state.preferences, ...preferencesResult };
	},
	setIsSaving(state, isSaving) {
		state.isSaving = isSaving;
	},
	setStat(state, stat) {
		state.stat = stat;
	},
};

export const actions = {
	login({ commit }, { email, password }) {
		const form = new FormData();
		form.append("email", email);
		form.append("password", password);

    return this.$auth.loginWith("local", { data: form, headers: { "Content-Type": "application/form-data" } }).then((resp) => {
      console.log('vm',this._vm)

			// this.$router.push(this.localePath({ path: "/" }));
			// this.isLoggingIn = false;
		});
	},
};
