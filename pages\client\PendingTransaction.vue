<template>
	<page title="Assignments" desc="">
		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/portal/pending-quotes">
				<template #item.actions="{ item }">
					<div class="d-flex">
						<!-- <v-btn v-tooltip="'Delete'" small icon @click="$refs.crud.delete(item.id)">
							<v-icon small>mdi-delete</v-icon>
						</v-btn> -->
						<v-btn
							v-tooltip="'Edit Transaction'"
							small
							icon
							:to="localePath({ name: 'new-quotes', params: { quote_id: item.id } })"
						>
							<v-icon small>mdi-eye</v-icon>
						</v-btn>
					</div>
				</template>

				<template #item.status="{ item }">
					<v-chip v-if="item.status === 0" small> Draft </v-chip>
					<v-chip v-else-if="item.status === 1" small outlined color="orange"> Under Review </v-chip>
					<v-chip v-else-if="item.status === 2" small outlined color="orange"> Under Approval </v-chip>
					<v-chip v-else-if="item.status === 3" small outlined color="green">Approved </v-chip>
					<v-chip v-else-if="item.status === 4" small outlined color="purple">Tender </v-chip>
					<v-chip v-else-if="item.status === 5" small outlined color="blue">Achieved </v-chip>
					<v-chip v-else-if="item.status === 6" small outlined color="red">On Going </v-chip>
					<v-chip v-else-if="item.status === 7" small outlined color="red">Lost </v-chip>
					<v-chip v-else-if="item.status === 8" small outlined color="red">Dead </v-chip>
					<v-chip v-else-if="item.status === 9" small outlined color="orange"> Pre Qualification </v-chip>
					<v-chip v-else-if="item.status === 10" small outlined color="orange"> Material Submittal </v-chip>
					<v-chip v-else-if="item.status === 11" small outlined color="orange"> Sample Request </v-chip>
					<v-chip v-else-if="item.status === 12" small outlined color="green"> Expected Order </v-chip>
					<v-chip v-else-if="item.status === 13" small outlined color="green"> Ordered </v-chip>
				</template>

				<template #item.currentStep="{ item }">
					<v-chip v-if="item.currentStep === 0" small> Draft </v-chip>
					<v-chip v-else-if="item.current_step === 1" small color="#b71f23" class="white--text"> Define Project </v-chip>
					<v-chip v-else-if="item.current_step === 2" small color="orange" class="white--text"> Take Off </v-chip>
					<v-chip v-else-if="item.current_step === 3" small color="orange" class="white--text">Factory Costing </v-chip>
					<v-chip v-else-if="item.current_step === 4" small color="purple" class="white--text">Pricing </v-chip>
					<v-chip v-else-if="item.current_step === 5" small color="#ebebeb" class="white--text">Shipping </v-chip>
					<v-chip v-else-if="item.current_step === 6" small color="green" class="white--text">Payment </v-chip>
					<v-chip v-else-if="item.current_step === 7" small color="green" class="white--text">Formal Quotation </v-chip>
					<v-chip v-else-if="item.current_step === 8" small color="green" class="white--text">Ordered </v-chip>
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>
			</data-table>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "Reference Number",
					sortable: true,
					value: "reference_number",
				},
				{
					text: "Product Category",
					sortable: true,
					value: "product_category.name",
				},
				{
					text: "Client",
					sortable: true,
					value: "client.name",
				},
				{
					text: "Quotation Status",
					sortable: true,
					value: "status",
				},
				{
					text: "Workflow",
					sortable: true,
					value: "currentStep",
				},
				{
					text: "Created at",
					sortable: true,
					value: "created_at",
				},
				{
					text: "Updated at",
					sortable: true,
					value: "updated_at",
				},
				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],
			defaultItem: {
				id: null,
				name: null,
				code: null,
				phone_code: null,
				region: null,
				language: null,
			},
		};
	},
	computed: {},
	watch: {},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

