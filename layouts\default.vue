<template>
	<base-view>
		<v-navigation-drawer
			v-model="drawer"
			:expand-on-hover="storage.miniDrawer"
			:mini-variant="storage.miniDrawer"
			clipped
			:fixed="isDesktop"
			app
			:right="isRTL"
			:permanent="isDesktop"
			width="300"
		>
			<template #prepend>
				<div v-if="isMobile" class="d-flex align-center justify-center mt-4">
					<logo />
				</div>
				<v-text-field
					ref="searchInput"
					v-model="searchMenu"
					autofocus
					filled
					dense
					hide-details
					solo
					clearable
					flat
					solo-inverted
					:placeholder="$t('menu.use-ctrl-s-to-search')"
					prepend-inner-icon="mdi-magnify"
					style="max-width: 300px"
					class="mx-2 mt-5 flex-grow-0"
					@keydown.ctrl.c="searchMenu = null"
					@keydown.down="selectNextItem"
					@keydown.up="selectPreviousItem"
					@keydown.enter="goToSelectedItem"
					@keydown.esc="searchMenu = null"
					@input="selectFirstItem"
				>
				</v-text-field>
			</template>
			<div class="d-flex flex-column fill-height">
				<v-list v-model="selectedItem" nav dense class="align-self-start fill-width">
					<v-subheader v-if="searchMenu">{{ $t("menu.search-result") }}</v-subheader>
					<div v-if="!filteredItem.length" type="grey" text icon="mdi-magnify-remove-outline" class="ma-2 text-caption muted-2">
						<v-icon small>mdi-magnify-remove-outline</v-icon>
						<span>{{ $t("menu.there-is-no-data-matching") }} "{{ searchMenu }}"</span>
					</div>
					<template v-for="(item, i) in filteredItem">
						<!-- {{ item.hasPermission }}-{{ i }} -->

						<v-list-group
							v-if="item.children && item.hasPermission"
							:key="'group' + item.title"
							:prepend-icon="item.action"
							no-action
						>
							<template #activator>
								<v-list-item-action>
									<v-icon>{{ item.icon }}</v-icon>
								</v-list-item-action>
								<v-list-item-content>
									<v-list-item-title>{{ item.title }} </v-list-item-title>
								</v-list-item-content>
							</template>

							<template v-for="child in item.children">
								<v-list-item v-if="child.hasPermission" :key="child.title" dense :to="child.to">
									<v-list-item-content>
										<v-list-item-title>{{ child.title }} </v-list-item-title>
									</v-list-item-content>
								</v-list-item>
							</template>
						</v-list-group>
						<template v-else>
							<v-list-item
								v-if="item.hasPermission"
								:key="i"
								:to="item.to"
								router
								:exact="i === 0"
								color="primary"
								exact-path
								:input-value="i === selectedItem"
								:class="{ 'colored-bg': i === selectedItem }"
							>
								<!-- <pre>
                {{ item }}
              </pre
								> -->
								<v-list-item-action>
									<v-icon>{{ item.icon }}</v-icon>
								</v-list-item-action>
								<v-list-item-content>
									<v-list-item-title v-html="searchMenu ? item.highlightedTitle : item.title" />
									<v-list-item-subtitle v-if="item.subheader" v-text="item.subheader"></v-list-item-subtitle>
								</v-list-item-content>
							</v-list-item>
						</template>
					</template>
				</v-list>

				<v-spacer />
			</div>
		</v-navigation-drawer>
		<v-app-bar ref="header" v-mutate="onMutate" :clipped-left="!isRTL" :clipped-right="isRTL" fixed app elevate-on-scroll>
			<template v-if="!isMobile">
				<v-app-bar-nav-icon @click.stop="drawerHandler">
					<v-scale-transition leave-absolute>
						<v-icon :key="'miniDrawer' + storage.miniDrawer">
							<template v-if="storage.miniDrawer">mdi-menu</template>
							<template v-else>mdi-menu-open</template>
						</v-icon>
					</v-scale-transition>
				</v-app-bar-nav-icon>
				<div class="d-flex align-center mt-4" :style="{ minWidth: isDesktop ? 'calc(300px - 54px)' : 0 }">
					<logo />
				</div>

				<v-spacer />
				<notifications />

				<v-btn icon @click="switchTheme"><v-icon>mdi-theme-light-dark</v-icon> </v-btn>

				<v-menu open-on-hover offset-y>
					<template #activator="{ on }">
						<v-badge avatar color="green" bordered overlap dot value="100">
							<avatar class="pointer" :src="$auth.user.profile_pic" v-on="on" />
						</v-badge>
					</template>
					<v-card flat min-width="250">
						<v-card-text class="d-flex">
							<v-progress-circular width="4" color="grey" size="84" :value="100" rotate="-90">
								<avatar size="80" />
							</v-progress-circular>
							<div class="ps-4 flex-grow-1">
								<div class="font-weight-bold">{{ $store.state.auth.user.name }}</div>
								<div class="text--secondary line-clamp-1 mb-2">{{ $store.state.auth.user.email }}</div>
							</div>
						</v-card-text>
					</v-card>
					<v-list dense>
						<v-list-item :to="localePath({ name: 'profile' })">
							<v-list-item-icon>
								<v-icon>mdi-cog</v-icon>
							</v-list-item-icon>
							<v-list-item-content>
								<v-list-item-title>
									<span>Account Details</span>
								</v-list-item-title>
							</v-list-item-content>
						</v-list-item>

						<v-list-item @click="changePasswordDialog = true">
							<v-list-item-icon>
								<v-icon>mdi-lock-check</v-icon>
							</v-list-item-icon>
							<v-list-item-content>
								<v-list-item-title>
									<span>Change Password</span>
								</v-list-item-title>
							</v-list-item-content>
						</v-list-item>

						<v-divider />
						<v-list-item :to="localePath({ name: 'logout' })">
							<v-list-item-icon>
								<v-icon>mdi-logout</v-icon>
							</v-list-item-icon>
							<v-list-item-content>
								<v-list-item-title>
									<span>Logout</span>
								</v-list-item-title>
							</v-list-item-content>
						</v-list-item>
					</v-list>
				</v-menu>
			</template>
			<template v-else>
				<v-app-bar-nav-icon @click.stop="drawerHandler" />
				<v-spacer />
				<v-toolbar-title id="page-title"></v-toolbar-title>
				<v-spacer />
			</template>
		</v-app-bar>
		<v-main>
			<v-divider></v-divider>
			<v-container fluid class="fill-height align-start">
				<v-row class="fill-height">
					<v-col class="fill-height">
						<Nuxt :nuxt-child-key="$route.name" />
					</v-col> </v-row
			></v-container>
		</v-main>

		<v-bottom-navigation v-if="isMobile" hide-on-scroll grow fixed color="primary" shift>
			<v-btn :to="localePath('/')">
				<span>Home</span>
				<v-icon>mdi-home</v-icon>
			</v-btn>

			<v-btn :to="localePath({ name: 'profile' })">
				<span>My Account</span>
				<v-icon>mdi-account</v-icon>
			</v-btn>
		</v-bottom-navigation>

		<v-dialog v-model="changePasswordDialog" absolute width="600px">
			<v-card>
				<v-card-title>
					<div class="mb-3">Change Password</div>
					<v-spacer></v-spacer>
					<v-btn icon @click="changePasswordDialog = false">
						<v-icon>mdi-close</v-icon>
					</v-btn>
				</v-card-title>
				<v-card-text>
					<v-form>
						<v-container>
							<vc-password
								v-model="passwordFormData.current_password"
								name="current_password"
								:label="`Current Password`"
								class="mb-8"
								dense
							>
							</vc-password>

							<vc-password v-model="passwordFormData.password" name="password" :label="`New Password`" dense class="mb-8">
							</vc-password>

							<vc-password
								v-model="passwordFormData.password_confirmation"
								:rules="[
									$rules.required($t('profile.new-password-confirmation')),
									$rules.match(
										$t('profile.new-password-confirmation'),
										passwordFormData.password_confirmation,
										passwordFormData.password
									),
								]"
								name="new_password_confirmation"
								:label="`New Password Confirmation`"
								dense
							>
							</vc-password>
						</v-container>
					</v-form>
				</v-card-text>
				<v-card-actions>
					<v-spacer></v-spacer>
					<v-btn text @click="changePasswordDialog = false">Cancel</v-btn>
					<v-btn color="primary" text @click="changePassword()">Save</v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>
	</base-view>
</template>

<script>
import darkColors from "~/assets/dark.scss";
import responsive from "~/mixins/responsive";
import authMixins from "~/mixins/auth";
export default {
	name: "DefaultLayout",
	mixins: [responsive, authMixins],
	middleware: ["auth"],
	data() {
		return {
			myAccount: {},
			changePasswordDialog: false,
			passwordFormData: {},
			drawer: true,
			fixed: false,
			searchMenu: null,
			bottomNavigationModel: 0,
			darkColors,
			right: true,
			rightDrawer: false,
			selectedItem: 1,
			isPermissionsLoaded: false,
			isPreferencesLoaded: false,
			isStatLoaded: false,
		};
	},

	computed: {
		filteredItem() {
			let items = [];
			if (this.searchMenu) {
				this.items.forEach((item) => {
					if (item.children) {
						const children = item.children.filter((child) => {
							return child.title.toLowerCase().includes(this.searchMenu.toLowerCase());
						});
						if (children.length > 0) {
							children.forEach((child) => {
								child.subheader = item.title;
								child.icon = item.icon;
								child.highlightedTitle = this.highlightTitle(child.title);
								items.push(child);
							});
						}
					} else if (!item.children && item.title.toLowerCase().includes(this.searchMenu.toLowerCase())) {
						item.highlightedTitle = this.highlightTitle(item.title);
						items.push(item);
					}
				});
			} else {
				items = this.items;
			}
			// check permissions for items and its children
			return items
				.map((item) => {
					if (item.children) {
						item.children = item.children.filter((child) => {
							if (!child.permission) return true;
							return this.$gates.hasPermission(child.permission);
						});
					}
					return item;
				})
				.filter((item) => {
					if (item.children) {
						return item.children.length > 0;
					} else if (item.permission) {
						return this.$gates.hasPermission(item.permission);
					} else {
						return true;
					}
				});
		},
		items() {
			return [
				{
					icon: "mdi-view-dashboard",
					title: "Dashboard",
					to: this.localePath("/"),
					// hasPermission: this.checkPermission("dashboard.stats"),
					hasPermission: true,
				},

				{
					icon: "mdi-tooltip-account",
					title: "Assignments",
					to: this.localePath({ name: "assignments" }),
					hasPermission: this.checkPermission("assignments.view"),
				},

				{
					icon: "mdi-newspaper",
					title: "Quotes",
					to: this.localePath({
						name: "quotes",
					}),
					hasPermission: this.checkPermission("quotes.view"),
				},
				{
					icon: "mdi-account-cog",
					title: "Users Management",
					hasPermission:
						this.checkPermission("users.view") ||
						this.checkPermission("permissions.view") ||
						this.checkPermission("activity_logs.view"),
					children: [
						{
							title: "Users",
							to: this.localePath({
								name: "users-management",
							}),
							hasPermission: this.checkPermission("users.view"),
						},
						{
							title: "Roles And Permissions",
							to: this.localePath({
								name: "roles-and-permissions",
							}),
							hasPermission: this.checkPermission("permissions.view"),
						},
						{
							title: "Activity Logs",
							to: this.localePath({ name: "activity-logs" }),
							hasPermission: this.checkPermission("activity_logs.view"),
						},
					],
				},
				{
					icon: "mdi-arrange-bring-forward",
					title: "Product Management",
					to: this.localePath({
						name: "product-categories",
					}),
					hasPermission: this.checkPermission("product_categories.view"),
				},
				{
					icon: "mdi-database-check",
					title: "Data Management",
					hasPermission:
						this.checkPermission("clients.view") ||
						this.checkPermission("currencies.view") ||
						this.checkPermission("factories.view") ||
						this.checkPermission("countries.view") ||
						this.checkPermission("bank_accounts.view") ||
						this.checkPermission("cities.view") ||
						this.checkPermission("delivery_methods.view") ||
						this.checkPermission("freight_forwarders.view") ||
						this.checkPermission("legalization_companies.view") ||
						this.checkPermission("insurance_companies.view") ||
						this.checkPermission("payment_methods.view") ||
						this.checkPermission("data_migration.view"),
					children: [
						{
							icon: "mdi-account-group-outline",
							title: "Clients",
							to: this.localePath({
								name: "clients",
							}),
							hasPermission: this.checkPermission("clients.view"),
						},
						{
							icon: "mdi-cash",
							title: "Currencies",
							to: this.localePath({
								name: "currencies",
							}),
							hasPermission: this.checkPermission("currencies.view"),
						},
						{
							icon: "mdi-bank",
							title: "Bank Accounts",
							to: this.localePath({
								name: "bank-accounts",
							}),
							hasPermission: this.checkPermission("bank_accounts.view"),
						},
						{
							icon: "mdi-factory",
							title: "Factories",
							to: this.localePath({
								name: "factories",
							}),
							hasPermission: this.checkPermission("factories.view"),
						},
						{
							icon: "mdi-earth",
							title: "Countries",
							to: this.localePath({
								name: "countries",
							}),
							hasPermission: this.checkPermission("countries.view"),
						},
						{
							icon: "mdi-city",
							title: "Cities",
							to: this.localePath({
								name: "cities",
							}),
							hasPermission: this.checkPermission("cities.view"),
						},
						{
							icon: "mdi-truck-delivery",
							title: "Delivery Methods",
							to: this.localePath({
								name: "delivery-methods",
							}),
							hasPermission: this.checkPermission("delivery_methods.view"),
						},
						{
							icon: "mdi-account-hard-hat",
							title: "Freight Forwarders",
							to: this.localePath({
								name: "freight-forwarders",
							}),
							hasPermission: this.checkPermission("freight_forwarders.view"),
						},
						{
							icon: "mdi-domain",
							title: "Legalization Companies",
							to: this.localePath({
								name: "legalization-companies",
							}),
							hasPermission: this.checkPermission("legalization_companies.view"),
						},
						{
							icon: "mdi-domain",
							title: "Insurance Companies",
							to: this.localePath({
								name: "insurance-companies",
							}),
							hasPermission: this.checkPermission("insurance_companies.view"),
						},
						{
							icon: "mdi-cash-multiple",
							title: "Payment Methods",
							to: this.localePath({
								name: "payment-methods",
							}),
							hasPermission: this.checkPermission("payment_methods.view"),
						},
						{
							icon: "mdi-database-plus",
							title: "Data Migration",
							to: this.localePath({
								name: "data-migration",
							}),
							hasPermission: this.checkPermission("data_migration.view"),
						},
						{
							icon: "mdi-domain-plus",
							title: "Branches",
							to: this.localePath({
								name: "branches",
							}),
							hasPermission: this.checkPermission("branches.view"),
						},
					],
				},

				{
					icon: "mdi-cog",
					title: "General Settings",
					to: this.localePath({ name: "general-sitting" }),
					hasPermission: this.checkPermission("settings.general"),
				},
				{
					icon: "mdi-chart-bar",
					title: "Reports",
					hasPermission:
						this.checkPermission("reports.sales") ||
						this.checkPermission("reports.price_history") ||
						this.checkPermission("reports.freight") ||
						this.checkPermission("reports.receivables") ||
						this.checkPermission("reports.payables") ||
						this.checkPermission("reports.orders") ||
						this.checkPermission("reports.employee_performance"),
					children: [
						{
							title: "Sales Report",
							to: this.localePath({ name: "sales-report" }),
							hasPermission: this.checkPermission("reports.sales"),
						},
						{
							title: "Price History",
							to: this.localePath({ name: "price-history" }),
							hasPermission: this.checkPermission("reports.price_history"),
						},
						{
							title: "Freight Report",
							to: this.localePath({ name: "freight-report" }),
							hasPermission: this.checkPermission("reports.freight"),
						},
						{
							title: "Receivables Report",
							to: this.localePath({ name: "receivables-report" }),
							hasPermission: this.checkPermission("reports.receivables"),
						},
						{
							title: "Payable Report",
							to: this.localePath({ name: "payable-report" }),
							hasPermission: this.checkPermission("reports.payables"),
						},
						{
							title: "Orders Report",
							to: this.localePath({ name: "orders-report" }),
							hasPermission: this.checkPermission("reports.orders"),
						},
						{
							title: "Employee Performance Report",
							to: this.localePath({ name: "employee-performance-report" }),
							hasPermission: this.checkPermission("reports.employee_performance"),
						},
					],
				},
			];
		},
	},

	mounted() {
		window.addEventListener("keydown", (e) => {
			if (e.key === "s" && e.ctrlKey) {
				e.preventDefault();
				this.$refs.searchInput.focus();
			}
		});
	},
	methods: {
		increaseLimit() {
			if (this.limit < this.countries.length) {
				this.limit += 12;
			}
		},
		onMutate() {
			let height = 0;
			const header = this.$refs.header;
			if (header) {
				height = `${header.$el.offsetHeight}px`;
			}
			document.querySelector(":root").style.setProperty("--headerHeight", height);
		},
		drawerHandler() {
			if (this.isDesktop) {
				this.storage.miniDrawer = !this.storage.miniDrawer;
			} else {
				this.storage.miniDrawer = false;
				this.drawer = !this.drawer;
			}
		},
		switchTheme() {
			this.$vuetify.theme.dark = !this.$vuetify.theme.dark;
			this.storage.theme = this.$vuetify.theme.dark ? "dark" : "light";
		},
		highlightTitle(item) {
			return item.replace(new RegExp(this.searchMenu, "gi"), (match) => `<b class="primary--text yellow">${match}</b>`);
		},
		goToSelectedItem() {
			if (this.selectedItem > -1) {
				this.$router.push(this.filteredItem[this.selectedItem].to);
			}
		},
		selectNextItem() {
			if (this.selectedItem < this.filteredItem.length - 1) {
				this.selectedItem++;
			}
		},
		selectPreviousItem() {
			if (this.selectedItem > 0) {
				this.selectedItem--;
			}
		},
		selectFirstItem(input) {
			if (this.filteredItem.length > 0 && input) {
				this.selectedItem = 0;
			} else {
				this.selectedItem = -1;
			}
		},
		changePassword() {
			this.$axios
				.$put(`/v1/auth/change-password`, this.passwordFormData)
				.then((resp) => {
					this.changePasswordDialog = false;
					this.$router.push(this.localePath({ name: "logout" })).then(() => {
						this.$toast.success("has been updated successfully");
					});
				})
				.catch(this.genericErrorHandler);
		},
		showKyc() {
			this.$nuxt.$emit("show-kyc");
		},
	},
};
</script>
<style lang="scss">
@import "~/assets/mixins";

.logo {
	@include responsive("height", 42px, 42px, 48px);
}
.unread-notification {
	background-color: #fcf6f6;
}
</style>
