<template>
	<page title="My Profile" desc="">
		<v-row>
			
			<v-col md="9">
				<nuxt-child />
			</v-col>
		</v-row>
	</page>
</template>

<script>
export default {
	computed: {
		items() {
			return [
				// {
				// 	title: this.$t("settings.profile"),
				// 	icon: "mdi-account",
				// 	to: "/settings/profile",
				// },
				// {
				// 	title: this.$t("settings.security"),
				// 	icon: "mdi-shield-lock",
				// 	to: "/settings/security",
				// },
				// {
				// 	title: this.$t("settings.notifications"),
				// 	icon: "mdi-bell",
				// 	to: "/settings/notifications",
				// },

				// {
				// 	title: this.$t("settings.verification"),
				// 	icon: "mdi-account-check",
				// 	to: "/settings/verification",
				// },
				// {
				// 	title: this.$t("settings.affiliate"),
				// 	icon: "mdi-account-group",
				// 	to: "/settings/affiliate",
				// },
				// {
				// 	title: this.$t("settings.2fa"),
				// 	icon: "mdi-shield-lock",
				// 	to: "/settings/2fa",
				// },

				{
					title: this.$t("settings.profile-information"),
					icon: "mdi-account",
					to: this.localePath({ name: "profile" }),
				},

				{
					title: this.$t("settings.account-preferences"),
					icon: "mdi-cog",
					to: this.localePath({ name: "account-preferences" }),
				},
			];
		},
	},
};
</script>
