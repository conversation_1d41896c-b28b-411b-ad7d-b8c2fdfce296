<template>
	<page title="Create Order" desc="">
		<div>
			<v-form ref="form" class="fill-height">
				<v-row class="my-3">
					<v-col md="6" cols="12">
						<v-card flat>
							<v-card-title class="font-weight-bold d-flex mb-3">
								<span>Order Details</span> <v-spacer></v-spacer>
							</v-card-title>
							<v-card-text>
								<vc-autocomplete
									v-model="orderDate.account_manager_id"
									outlined
									api="/v1/lookups/users"
									label="Account Manager"
									name="account_manager_id"
									item-text="text"
									item-value="value"
									:rules="[$rules.required('account manager')]"
								>
								</vc-autocomplete>

								<!-- <field-date v-model="orderDate.factory_quote_date" outlined label="Factory Quote Date"></field-date> -->

								<!-- <vc-text-field
									v-model="orderDate.factory_quote_number"
									outlined
									class="me-1"
									name="factory_quote_number"
									label="Factory Quote Number"
									:rules="[$rules.required('Factory Quote Number')]"
								></vc-text-field> -->

								<field-wysiwyg
									v-model="orderDate.shipping_instructions"
									name="shipping_instructions"
									:value="value"
									label="Shipping Instructions"
									:rules="[$rules.required('Shipping Instructions')]"
									@input="update"
									v-on="on"
								></field-wysiwyg>
							</v-card-text>
						</v-card>
					</v-col>
					<v-col md="6" cols="12">
						<v-card>
							<v-card-text>
								<field-wysiwyg
									v-model="orderDate.delivery_notes"
									name="delivery_notes"
									:value="value"
									label="Delivery Notes"
									@input="update"
									v-on="on"
								></field-wysiwyg>
								<field-wysiwyg
									v-model="orderDate.terms"
									name="terms"
									:value="value"
									label="Terms"
									@input="update"
									v-on="on"
								></field-wysiwyg>
							</v-card-text>
						</v-card>
					</v-col>
				</v-row>

				<v-divider class="py-1" />

				<v-row class="my-3">
					<v-col md="6" cols="12">
						<v-card flat>
							<v-card-title class="font-weight-bold d-flex mb-3">
								<span>Proforma Invoice</span>
							</v-card-title>
							<v-card-text>
								<vc-text-field
									v-model="orderDate.proforma_invoice.trn_number"
									outlined
									class="me-1"
									name="trn_number"
									label="Trn Number"
									:rules="[$rules.required('trn number')]"
								></vc-text-field>

								<vc-autocomplete
									v-model="orderDate.proforma_invoice.bank_accounts"
									outlined
									multiple
									api="/v1/lookups/bank-accounts"
									label="Bank Accounts"
									name="bank_accounts"
									item-text="text"
									item-value="value"
									:rules="[$rules.required('bank accounts')]"
								>
									<template #item="{ item }">
										<v-list-item-content>
											<v-list-item-title>
												<span>
													{{ item.meta?.bank_name }} /
													<span class="mx-1 body-2"> {{ item.meta.branch_name }}</span>
												</span>
											</v-list-item-title>
										</v-list-item-content>
									</template>
									<template #selection="{ item }">
										<v-list-item-content>
											<v-list-item-title>
												<span>
													{{ item.meta?.bank_name }} /
													<span class="mx-1 body-2"> {{ item.meta.branch_name }}</span>
												</span>
											</v-list-item-title>
										</v-list-item-content>
									</template>
								</vc-autocomplete>
							</v-card-text>
						</v-card>
					</v-col>
					<v-col md="6" cols="12">
						<v-card>
							<v-card-text>
								<field-wysiwyg
									v-model="orderDate.proforma_invoice.terms"
									name="terms"
									:value="value"
									label="Invoice Terms"
									@input="update"
									v-on="on"
								></field-wysiwyg>
							</v-card-text>
						</v-card>
					</v-col>
				</v-row>

				<v-divider class="py-1" />

				<v-row class="my-3">
					<v-col md="6" cols="12">
						<v-card flat>
							<v-card-title class="font-weight-bold d-flex mb-3">
								<span>Commercial Invoice</span>
							</v-card-title>
							<v-card-text>
								<vc-text-field
									v-model="orderDate.commercial_invoice.invoice_number"
									outlined
									class="me-1"
									name="invoice_number"
									label="Invoice Number"
									:rules="[$rules.required('invoice number')]"
								></vc-text-field>

								<field-date
									v-model="orderDate.commercial_invoice.invoice_date"
									:rules="[$rules.required('invoice date')]"
									outlined
									label="Iinvoice Date"
								></field-date>
							</v-card-text>
						</v-card>
					</v-col>
					<v-col md="6" cols="12">
						<v-card>
							<v-card-text>
								<field-wysiwyg
									v-model="orderDate.commercial_invoice.terms"
									name="terms"
									:value="value"
									label="Commercial Terms"
									@input="update"
									v-on="on"
								></field-wysiwyg>
							</v-card-text>
						</v-card>
					</v-col>
				</v-row>

				<v-divider class="py-1" />

				<v-row class="my-3">
					<v-col md="6" cols="12">
						<v-card flat>
							<v-card-title class="font-weight-bold d-flex mb-3">
								<span>Pick Ticket</span>
							</v-card-title>
							<v-card-text>
								<field-wysiwyg
									v-model="orderDate.pick_ticket.terms"
									name="terms"
									:value="value"
									label="Pick Ticket Terms"
									@input="update"
									v-on="on"
								></field-wysiwyg>
							</v-card-text>
						</v-card>
					</v-col>
				</v-row>

				<v-divider class="py-1" />

				<!-- <v-row class="my-3">
					<v-col md="6" cols="12">
						<v-card flat>
							<v-card-title class="font-weight-bold d-flex mb-3">
								<span>Acknowledgement Invoice</span>
							</v-card-title>
							<v-card-text>
								<field-wysiwyg
									v-model="orderDate.acknowledgement_invoice.terms"
									name="terms"
									:value="value"
									label="Acknowledgement Invoice Terms"
									@input="update"
									v-on="on"
								></field-wysiwyg>
							</v-card-text>
							<v-card-text>
								<field-wysiwyg
									v-model="orderDate.acknowledgement_invoice.notes"
									name="terms"
									:value="value"
									label="Acknowledgement Invoice Notes"
									@input="update"
									v-on="on"
								></field-wysiwyg>
							</v-card-text>
						</v-card>
					</v-col>
				</v-row> -->

				<v-card flat class="mt-3">
					<v-card-actions>
						<v-spacer />
						<v-btn color="primary" @click="saveOrderData()">Save</v-btn>
					</v-card-actions>
				</v-card>
			</v-form>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			orderDate: {
				quote_id: this.$route.params.quote_id,
				account_manager_id: null,
				factory_quote_date: null,
				factory_quote_number: null,
				shipping_instructions: null,
				delivery_notes: null,
				terms: null,
				proforma_invoice: {
					trn_number: null,
					terms: null,
					should_create_revision: false,
					bank_accounts: [],
				},
				commercial_invoice: {
					invoice_number: null,
					invoice_date: null,
					terms: null,
					should_create_revision: false,
				},
				pick_ticket: {
					terms: null,
					should_create_revision: false,
				},
				acknowledgement_invoice: {
					terms: null,
					notes: null,
					should_create_revision: false,
				},
			},
		};
	},

	async fetch() {
		await this.$axios.$get(`/v1/portal/orders/${this.$route.params.order_id}`).then((resp) => {
			this.orderDate = resp;
		});
		console.log("this.$route.params.order_id", this.$route.params.order_id);
	},

	computed: {},
	watch: {},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},

		saveOrderData() {
			if (!this.$refs.form.validate()) return;

			if (this.$route.params.order_id && this.$route.params.order_id !== "order-id") {
				this.$axios
					.$put(`/v1/portal/orders/${this.$route.params.order_id}`, this.orderDate)
					.then((resp) => {
						this.$toast.success("thas been successfully");
						this.$router.push(this.localePath({ name: "show-order-po", params: { order_id: this.$route.params.order_id } }));
					})
					.catch(this.genericErrorHandler);
			} else {
				this.$axios
					.$post(`/v1/portal/orders`, this.orderDate)
					.then((resp) => {
						this.$toast.success("thas been successfully");
						this.$router.push(this.localePath({ name: "show-order-po", params: { order_id: resp.id } }));
					})
					.catch(this.genericErrorHandler);
			}
		},

		// goToShowOrder() {
		// 	 this.$router.push(this.localePath({ name: "show-order-po", params: { order_id: this.$route.params.quote_id } }));
		// },
	},
};
</script>

<style lang="scss">
.tiptap-vuetify-editor__content {
	min-height: 100px;
}
</style>