<template>
	<page title="Product Categories" desc="">
		<template #actions>
			<v-btn v-if="checkPermission('product_categories.create')" small class="me-2" color="success" @click="$refs.crud.new()">
				<v-icon left>mdi-plus</v-icon>Add Product Category
			</v-btn>
		</template>
		<div>
			<data-table
				v-if="checkPermission('product_categories.view')"
				ref="dataTable"
				:columns="columns"
				api="/v1/portal/product-categories"
			>
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn
							v-if="checkPermission('product_categories.delete')"
							v-tooltip="'Delete'"
							small
							icon
							@click="$refs.crud.delete(item.id)"
						>
							<v-icon small>mdi-delete</v-icon>
						</v-btn>
						<v-btn
							v-if="checkPermission('product_categories.update')"
							v-tooltip="'Edit'"
							small
							icon
							@click="$refs.crud.edit(item.id)"
						>
							<v-icon small>mdi-pencil</v-icon>
						</v-btn>
						<v-btn
							v-if="checkPermission('product_sub_categories.view')"
							v-tooltip="'Show'"
							icon
							small
							:to="localePath({ name: 'product-sub-categories', params: { category_id: item.id } })"
						>
							<v-icon small>mdi-eye</v-icon>
						</v-btn>
					</div>
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>

				<template #item.photo="{ item }">
					<router-link
						v-if="checkPermission('product_sub_categories.view')"
						:to="localePath({ name: 'product-sub-categories', params: { category_id: item.id } })"
					>
						<v-img :src="item.photo" style="width: 150px"></v-img>
					</router-link>
				</template>
			</data-table>

			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>

			<crud
				ref="crud"
				v-slot="{ item }"
				width="400"
				:default="defaultItem"
				item-name="product categories"
				api="/v1/portal/product-categories"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<field-text-field
					v-model="item.name"
					name="name"
					label="Category Name"
					:rules="[$rules.required('name')]"
				></field-text-field>

				<field-text-field
					v-model="item.code"
					name="code"
					label="Category Code"
					:rules="[$rules.required('code')]"
				></field-text-field>

				<v-textarea v-model="item.description" :counter="300" name="description" label="Category Description" required></v-textarea>

				<field-upload
					v-model="item.photo"
					:value="value"
					:types="['png', 'jpg']"
					label="Photo"
					@click="handler"
					@input="update"
				></field-upload>
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "Image",
					sortable: true,
					value: "photo",
				},
				{
					text: "Name",
					sortable: true,
					value: "name",
				},
				// {
				// 	text: "Slug",
				// 	sortable: true,
				// 	value: "slug",
				// },
				{
					text: "Code",
					sortable: true,
					value: "code",
				},
				{
					text: "Description",
					sortable: true,
					value: "description",
				},
				{
					text: "Created at",
					sortable: true,
					value: "created_at",
				},
				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],
			defaultItem: {
				name: null,
				slug: null,
				code: null,
				description: null,
			},
		};
	},
	computed: {},
	watch: {},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

