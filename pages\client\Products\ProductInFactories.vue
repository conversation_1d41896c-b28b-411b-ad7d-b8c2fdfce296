<template>

<v-container>
	<v-row>
		<v-col md="12">
			<v-data-table
				:headers="headers"
				:items="productInFactories.factories"
			></v-data-table>
		</v-col>
	</v-row>
</v-container>
	
	
</template>

<script>
  export default {
    data () {
      return {
        headers: [
			{ text: 'Factory Name', value: 'factory_name' },
          	{ text: 'Price', value: 'price' },
          	{ text: 'Name', value: 'name' },
          	{ text: 'Code', value: 'code' },
          	{ text: 'Description', value: 'description' },
          	{ text: 'Quantity', value: 'quantity' },
        ],
       
		productInFactories:{
			factories:[],
		}
      }
    },

	async fetch() {
		await this.$axios.$get(`/v1/portal/products/${this.$route.params.id}`).then((resp) => {
			// this.products = resp;
			this.productInFactories = resp;
		});
	},
  }
</script>
