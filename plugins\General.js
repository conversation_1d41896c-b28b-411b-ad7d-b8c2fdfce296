import Vue from "vue";
import Teleport from "vue2-teleport";
import vuePositionSticky from "vue-position-sticky";
import { TiptapVuetifyPlugin } from "tiptap-vuetify";
import DatetimePicker from "vuetify-datetime-picker";
import ReactiveStorage from "./ReactiveLocalStorage";
import responsive from "~/mixins/responsive";
import config from "~/mixins/config";
import errorHandler from "~/mixins/errorHandler";
import general from "~/mixins/general";
import "tiptap-vuetify/dist/main.css";

export default ({ app }, inject) => {
	Vue.filter("dateFromNow", (value) => {
		return app
			.$dayjs(value)
			.fromNow();
	});
	Vue.filter("date", (value) => {
		return app
			.$dayjs(value)
			.format("YYYY-MM-DD");
	});
	Vue.filter("dateTime", (value) => {
		if (!value) return "";
		return app
			.$dayjs(value)
			.format("YYYY-MM-DD h:mm A");
	});
	Vue.filter("time", (value) => {
		return app
			.$dayjs(value)
			.format("h:mm A");
	});
	Vue.filter("niceDate", (value) => {
		return app
			.$dayjs(value)
			.format("ll");
	});

	Vue.filter("phone", (value) => {
		return value.replace(/\D+/g, "").replace(/(\d{1})(\d{3})(\d{3})(\d{4})/, "+$1 $2 $3$4");
	});
	Vue.filter("progress", (value) => {
		return `${Math.round(value * 10000) / 100}`;
	});
	Vue.mixin(responsive);
	Vue.mixin(config);
	Vue.mixin(errorHandler);
	Vue.mixin({ components: { Teleport } });

	// Set initial values
	Vue.use(ReactiveStorage, {
		theme: "light",
		loginHistory: [],
		miniDrawer: false,
		bordered: false,
		debugPermissions: false,
		debugEntity: "",
	});

	Vue.use(vuePositionSticky);

	Vue.use(TiptapVuetifyPlugin, {
		vuetify: app.vuetify,
		iconsGroup: "mdi",
	});

	Vue.use(DatetimePicker);
	Vue.mixin(general);
};
