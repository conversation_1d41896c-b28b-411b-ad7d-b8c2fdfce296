<template>
	<page title="Sales Report" desc="">
		<div>
			<data-table v-if="checkPermission('reports.sales')" ref="dataTable" :columns="columns" api="/v1/portal/reports/sales">
				<template #filter="{ models }">
					<vc-autocomplete
						key="client_id"
						v-model="models.client_id"
						api="/v1/lookups/clients"
						label="Client"
						name="client_id"
						item-text="text"
						item-value="value"
					>
					</vc-autocomplete>

					<vc-autocomplete
						key="country_id"
						v-model="models.country_id"
						api="/v1/lookups/countries"
						label="Country"
						name="countries"
						item-text="text"
						item-value="value"
					>
					</vc-autocomplete>

					<vc-autocomplete
						key="factory_id"
						v-model="models.factory_id"
						api="/v1/lookups/factories"
						label="Factories"
						name="factory_id"
						item-text="text"
						item-value="value"
					>
					</vc-autocomplete>

					<vc-autocomplete
						key="product_category_id"
						v-model="models.product_category_id"
						api="/v1/lookups/product-categories"
						label="Product Category"
						name="product_category_id"
						item-text="text"
						item-value="value"
					>
					</vc-autocomplete>
				</template>

				<template #item.po_date="{ item }">
					{{ item.po_date | dateTime }}
				</template>
			</data-table>
			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "Reference Number",
					sortable: true,
					value: "quote_reference_number",
				},
				{
					text: "Country Name",
					sortable: true,
					value: "country_name",
				},
				{
					text: "Client Name",
					sortable: true,
					value: "client_name",
				},
				{
					text: "Factory Name",
					sortable: true,
					value: "factory_name",
				},
				{
					text: "Product Category",
					sortable: true,
					value: "product_category_name",
				},
				{
					text: "PO Number",
					sortable: true,
					value: "po_number",
				},
				{
					text: "PO Date",
					sortable: true,
					value: "po_date",
				},
				{
					text: "Selling Price",
					sortable: true,
					value: "selling_price",
				},
				{
					text: "Year",
					sortable: true,
					value: "year",
				},
			],
		};
	},
	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

