export default {
	methods: {
		stripTags(originalString) {
			if (typeof originalString !== "string") {
				return originalString;
			}
			originalString = originalString.replace(/&nbsp;/gi, " ");
			return originalString.replace(/(<([^>]+)>)/gi, "");
    },

    checkPermission(name) {
			const userPermissions = this.$store.state.auth.user.role.permissions_names;
			return userPermissions.includes(name);
    },

    localePath(route) {
      return route
    },
    $t(t) {
      return t
    }
	},
};
