<template>
	<page title="Orders" desc="">
		<div>
			<data-table ref="dataTable" :columns="columns" api="/v1/portal/orders">
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="'Delete'" small icon @click="$refs.crud.delete(item.id)">
							<v-icon small>mdi-delete</v-icon>
						</v-btn>
						<v-btn
							v-tooltip="'Edit'"
							small
							icon
							:to="localePath({ name: 'order-form', params: { quote_id: 'qoute-id', order_id: item.id } })"
						>
							<v-icon small>mdi-pencil</v-icon>
						</v-btn>
						<v-btn v-tooltip="'Show'" small icon :to="localePath({ name: 'show-order-po', params: { order_id: item.id } })">
							<v-icon small>mdi-eye</v-icon>
						</v-btn>
					</div>
				</template>

				<template #item.quote="{ item }">
					{{ item.quote?.reference_number }}
				</template>

				<template #item.accountManager="{ item }">
					{{ item.account_manager?.name }}
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>
			</data-table>

			<crud
				ref="crud"
				v-slot="{ item }"
				width="400"
				:default="defaultItem"
				item-name="Country"
				api="/v1/portal/orders"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<field-text-field v-model="item.id" name="language" label="Language"></field-text-field>
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "Order Reference Number",
					sortable: true,
					value: "reference_number",
				},
				{
					text: "Factory Quote Number",
					sortable: true,
					value: "factory_quote_number",
				},
				{
					text: "Quote Reference Number",
					sortable: true,
					value: "quote",
				},
				{
					text: "Account Manager",
					sortable: true,
					value: "accountManager",
				},
				{
					text: "Created at",
					sortable: true,
					value: "created_at",
				},
				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],
		};
	},
	computed: {},
	watch: {},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

