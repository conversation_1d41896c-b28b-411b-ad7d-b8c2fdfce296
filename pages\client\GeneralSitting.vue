<template>
	<page title="General Settings" desc="">
		<v-simple-table v-if="checkPermission('settings.general')">
			<template #default>
				<thead>
					<tr>
						<th class="text-left">Id</th>
						<th class="text-left">Key</th>
						<th class="text-left">Create At</th>
						<th class="text-left">Actions</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td>{{ sittingsData.id }}</td>
						<td>{{ sittingsData.key }}</td>
						<td>{{ sittingsData.created_at | date }}</td>
						<td>
							<v-btn v-tooltip="'Edit'" small icon @click="workflowDialog = true">
								<v-icon small>mdi-eye</v-icon>
							</v-btn>
						</td>
					</tr>
				</tbody>
			</template>
		</v-simple-table>

		<div v-else class="pa-5">
			<v-icon left>mdi-alert-circle</v-icon>
			<span class="pt-2">Sorry, you do not have permission!</span>
		</div>

		<v-dialog v-model="workflowDialog" absolute width="900px">
			<v-card>
				<v-card-title>
					<div class="mb-6">Edit Workflow <span class="body-2">(Assign To Role)</span></div>
					<v-spacer></v-spacer>
					<v-btn icon @click="workflowDialog = false">
						<v-icon>mdi-close</v-icon>
					</v-btn>
				</v-card-title>
				<v-card-text>
					<div v-for="(item, key, i) in sittingsData.value" :key="key" class="d-flex mb-4">
						<div style="width: 25%; align-self: center" class="d-flex">
							<v-icon class="me-3">mdi-numeric-{{ ++i }}-circle</v-icon>
							<h4>
								<span class="text-uppercase">{{ key }}</span>
							</h4>
						</div>

						<div style="width: 75%">
							<vc-select
								key="assign_to_role"
								v-model="item.assign_to_role"
								hide-details
								outlined
								name="assign_to_role"
								label="Assign To Role"
								api="/v1/lookups/roles"
							>
								<template #item="{ item }">
									<v-list-item-content>
										<v-list-item-title>
											<span class="text-capitalize">{{ item.text }} </span>
										</v-list-item-title>
									</v-list-item-content>
								</template>
								<template #selection="{ item }">
									<v-list-item-content>
										<v-list-item-title>
											<span class="text-capitalize"> {{ item.text }} </span>
										</v-list-item-title>
									</v-list-item-content>
								</template>
							</vc-select>
						</div>
					</div>
				</v-card-text>

				<v-card-actions>
					<v-btn text @click="workflowDialog = false">Cancel</v-btn>
					<v-spacer></v-spacer>
					<v-btn color="success" @click="workflowHandler()">Save</v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>
	</page>
</template>

<script>
export default {
	data() {
		return {
			sittingsData: {},
			workflowDialog: false,
		};
	},

	async fetch() {
		await this.$axios.$get(`/v1/portal/settings/quote_workflow`).then((resp) => {
			//	this.sittingsData = resp;
			const sortedValue = {
				define_project: resp.value.define_project,
				take_off: resp.value.take_off,
				data_entry: resp.value.data_entry,
				pricing: resp.value.pricing,
				shipping: resp.value.shipping,
				payment: resp.value.payment,
				formal_quote: resp.value.formal_quote,
				order: resp.value.order,
			};

			// Assign the reordered object back to the settingsData
			this.sittingsData = {
				...resp,
				value: sortedValue,
			};
		});
	},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},

		workflowHandler() {
			this.$axios
				.$put(`/v1/portal/settings/quote_workflow`, this.sittingsData)
				.then((resp) => {
					this.$toast.success("updated successfully");
					this.workflowDialog = false;
				})
				.catch(this.genericErrorHandler);
		},
	},
};
</script>

