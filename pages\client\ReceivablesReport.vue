<template>
	<page title="Receivables Report" desc="">
		<div>
			<data-table
				v-if="checkPermission('reports.receivables')"
				ref="dataTable"
				:columns="columns"
				api="/v1/portal/reports/receivables"
			>
				<!-- <template #filter="{ models }">
					<vc-autocomplete
						key="client_id"
						v-model="models.client_id"
						api="/v1/lookups/clients"
						label="Client"
						name="client_id"
						item-text="text"
						item-value="value"
					>
					</vc-autocomplete>

					<vc-autocomplete
						key="country_id"
						v-model="models.country_id"
						api="/v1/lookups/countries"
						label="Country"
						name="countries"
						item-text="text"
						item-value="value"
					>
					</vc-autocomplete>

					<vc-autocomplete
						key="product_category_id"
						v-model="models.product_category_id"
						api="/v1/lookups/product-categories"
						label="Product Category"
						name="product_category_id"
						item-text="text"
						item-value="value"
					>
					</vc-autocomplete>
				</template> -->

				<template #item.po_date="{ item }">
					{{ item.po_date | dateTime }}
				</template>

				<template #item.shipment_date="{ item }">
					{{ item.shipment_date | dateTime }}
				</template>

				<template #item.ci_date="{ item }">
					{{ item.ci_date | dateTime }}
				</template>

				<template #item.due_days="{ item }">
					{{ item.due_days | dateTime }}
				</template>
			</data-table>
			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "Client Name",
					sortable: true,
					value: "client_name",
				},
				{
					text: "PO Number",
					sortable: true,
					value: "po_number",
				},
				{
					text: "PO Date",
					sortable: true,
					value: "po_date",
				},
				{
					text: "Shipment Date",
					sortable: true,
					value: "shipment_date",
				},
				{
					text: "CI Number",
					sortable: true,
					value: "ci_number",
				},
				{
					text: "CI Date",
					sortable: true,
					value: "ci_date",
				},
				{
					text: "Payment Terms",
					sortable: true,
					value: "payment_terms",
				},
				{
					text: "Received Amount",
					sortable: true,
					value: "received_amount",
				},
				{
					text: "Due Days",
					sortable: true,
					value: "due_days",
				},
			],
		};
	},
	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

