<template>
		<div>
			<Teleport to="#actions">
			<v-btn color="success" depressed @click="$refs.crud.new()">
				<v-icon left>mdi-plus</v-icon>
				Add New
		   </v-btn>
		</Teleport>
			<data-table ref="dataTable"  :columns="columns" api="/v1/portal/products">
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="'Delete'" small icon @click="$refs.crud.delete(item.id)">
							<v-icon small>mdi-delete</v-icon>
						</v-btn>
						<v-btn v-tooltip="'Edit'" small icon @click="$refs.crud.edit(item.id)">
							<v-icon small>mdi-pencil</v-icon>
						</v-btn>
						<v-btn
							v-tooltip="'Show product details in factories'"
							icon
							small
							:to="localePath({ name: 'product-in-factories', params: { id: item.id } })"
						>
							<v-icon small>mdi-factory</v-icon>
						</v-btn>
					</div>
				</template>

				<template #item.price="{ item }">
					{{ item.price }}
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>
			</data-table>

			<crud
				ref="crud"
				v-slot="{ item , isEdit}"
				width="400"
				:default="defaultItem"
				item-name="Product"
				api="/v1/portal/products?ids=sub_category"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<field-text-field
					v-model="item.name"
					name="name"
					label="Product Name"
					:rules="[$rules.required('name')]"
				></field-text-field>

				<field-text-field
					v-model="item.code"
					name="code"
					label="Product Code"
					:rules="[$rules.required('code')]"
				></field-text-field>

				<field-text-field
					v-model="item.price"
					name="price"
					type="number"
					label="Product Price"
					:rules="[$rules.required('price')]"
				></field-text-field>

				<field-text-field
					v-model="item.description"
					name="description"
					label="Description"
					:rules="[$rules.required('description')]"
				></field-text-field>

				<!-- <field-text-field
					v-model="item.sub_category_id"
					name="sub_category_id"
					label="Product category"
					:rules="[$rules.required('Product category')]"
				></field-text-field> -->

				<vc-select
					v-model="item.sub_category_id"
					name="sub_category_id"
					api="/v1/lookups/product-sub-categories"
					label="Product category"
					:rules="[$rules.required('Product category')]"
				></vc-select>

				<!-- ********************************* -->

				<div v-if="isEdit">
					<div class="my-4">
						<strong>Product details in factories</strong>
					</div>

					<div v-for="(field, i) in item.factories" :key="field">
						<div>
							<!-- <v-text-field :key="i + 'key'" v-model="field.factory_id" label="factory_id"> </v-text-field> -->
							<vc-select
								:key="i + 'key'"
								v-model="field.factory_id"
								name="factory_id"
								api="/v1/lookups/factories"
								label="Factory"
								:rules="[$rules.required('Factory')]"
							></vc-select>
							<v-text-field :key="i + 'value'" v-model="field.price" label="price" type="number"> </v-text-field>
							<v-text-field :key="i + 'value'" v-model="field.code" label="code"> </v-text-field>
							<v-text-field :key="i + 'value'" v-model="field.description" label="description"> </v-text-field>
							<v-text-field :key="i + 'value'" v-model="field.quantity" label="quantity"> </v-text-field>

							<v-btn icon small class="float-end ms-1" color="primary" @click="item.factories.splice(i, 1)">
								<v-icon>mdi-trash-can</v-icon>
							</v-btn>
						</div>
					</div>

					<v-btn
						v-tooltip="'add new'"
						icon
						class="float-end"
						@click="item.factories.push({ factory_id: null, price: null, code: null, description: null, quantity: null, })"
					>
						<v-icon>mdi-plus</v-icon>
					</v-btn>
				</div>

				

			</crud>
		</div>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				// {
				// 	text: "Logo",
				// 	sortable: true,
				// 	value: "logo",
				// },
				{
					text: "Product Name",
					sortable: true,
					value: "name",
				},
				{
					text: "Product Code",
					sortable: true,
					value: "code",
				},
				{
					text: "Areel Price",
					sortable: true,
					value: "price",
				},
				{
					text: "Description",
					sortable: true,
					value: "description",
				},
			    {
			    	text: "Created at",
			    	sortable: true,
			    	value: "created_at",
			    },
			    // {
			    // 	text: "Updated at",
			    // 	sortable: true,
			    // 	value: "updated_at",
			    // },
				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],
			defaultItem: {
				id: null,
				name: null,
				code: null,
				price:null,
				description: null,
				sub_category_id: null,

				factories:
					[
						{
							factory_id: null,
							price: null,
							name: null,
							code: null,
							description: null,
							quantity: null
					}
				]
			},
		};
	},
	computed: {},
	watch: {},
	
	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

