<template>
	<page title="Factories" desc="">
		<template #actions>
			<v-btn v-if="checkPermission('factories.create')" color="success" @click="$refs.crud.new()">
				<v-icon left>mdi-plus</v-icon>Add factory
			</v-btn>
		</template>
		<div>
			<data-table v-if="checkPermission('factories.view')" ref="dataTable" :columns="columns" api="/v1/portal/factories">
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn
							v-if="checkPermission('factories.delete')"
							v-tooltip="'Delete'"
							small
							icon
							@click="$refs.crud.delete(item.id)"
						>
							<v-icon small>mdi-delete</v-icon>
						</v-btn>
						<v-btn v-if="checkPermission('factories.update')" v-tooltip="'Edit'" small icon @click="$refs.crud.edit(item.id)">
							<v-icon small>mdi-pencil</v-icon>
						</v-btn>
					</div>
				</template>

				<template #item.country="{ item }">
					{{ item.country.name }}
				</template>

				<template #item.logo="{ item }">
					<thumbnail :src="item.logo" :preview-title="item.title" />
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>
			</data-table>
			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>

			<crud
				ref="crud"
				v-slot="{ item }"
				width="400"
				:default="defaultItem"
				item-name="Factory"
				api="/v1/portal/factories?ids=country,productCategories,productSubCategories"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<v-text-field v-model="item.name" name="name" label="Name" :rules="[$rules.required('name')]"></v-text-field>

				<v-text-field
					v-model="item.email"
					name="email"
					label="Email"
					:rules="[$rules.required('email'), $rules.email('email')]"
				></v-text-field>

				<vc-autocomplete
					v-model="item.country_id"
					name="country"
					api="/v1/lookups/countries"
					item-text="text"
					item-value="value"
					label="Country"
					:rules="[$rules.required('country')]"
				></vc-autocomplete>

				<vc-autocomplete
					v-model="item.currency_id"
					name="currency"
					api="/v1/lookups/currencies"
					item-text="text"
					item-value="value"
					label="Currency"
					:rules="[$rules.required('currency')]"
				></vc-autocomplete>

				<v-text-field v-model="item.address" name="address" label="Address" :rules="[$rules.required('address')]"></v-text-field>

				<vc-autocomplete
					v-model="item.product_categories"
					multiple
					:api="`/v1/lookups/product-categories`"
					label="Product Categories"
					name="product_categories"
					item-text="text"
					item-value="value"
				>
				</vc-autocomplete>

				<vc-autocomplete
					:key="item.product_categories"
					v-model="item.product_sub_categories"
					multiple
					:api="`/v1/lookups/product-sub-categories?category_id=${item.product_categories}`"
					label="Product Sub Categories"
					name="product_sub_categories"
					item-text="text"
					item-value="value"
				>
				</vc-autocomplete>

				<field-upload
					v-model="item.logo"
					:value="value"
					:types="['png', 'jpg', 'jpeg', 'webp', 'svg']"
					label="Logo"
					@click="handler"
					@input="update"
				></field-upload>
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "Logo",
					sortable: true,
					value: "logo",
				},
				{
					text: "Factory Name",
					sortable: true,
					value: "name",
				},
				{
					text: "Factory Email",
					sortable: true,
					value: "email",
				},
				{
					text: "Factory Address",
					sortable: true,
					value: "address",
				},
				{
					text: "Contact People",
					sortable: true,
					value: "contact_people_count",
				},
				{
					text: "Country",
					sortable: true,
					value: "country",
				},
				{
					text: "Created at",
					sortable: true,
					value: "created_at",
				},
				// {
				// 	text: "Updated at",
				// 	sortable: true,
				// 	value: "updated_at",
				// },
				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],
			defaultItem: {
				id: null,
				name: null,
				email: null,
				address: null,
				logo: null,
				sales_people_count: 0,
				country_id: null,
				currency_id: null,
				product_categories: [],
				product_sub_categories: [],
			},
		};
	},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>
