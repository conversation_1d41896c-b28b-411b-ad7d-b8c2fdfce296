<template>
	<crud-drawer
		ref="crudDrawer"
		v-model="drawerModel"
		right
		app
		temporary
		:loading="isGetting"
		:error="error"
		:permanent="isPersistent"
		:width="width"
		:title="(isEdit ? 'Edit' : 'New') + ' ' + itemName"
	>
		<slot v-bind="{ item, isEdit, keyId }" />
		<template #actions>
			<slot name="actions" v-bind="{ get, post, put, isPosting, isPutting, isGetting }">
				<v-btn text @click="close">cancel</v-btn>
				<v-spacer />
				<v-btn v-if="isEdit" :loading="isPutting" color="primary" text :disabled="!!error" @click="put">save</v-btn>
				<v-btn v-else color="primary" :loading="isPosting" text @click="post">save</v-btn>
			</slot>
		</template>
	</crud-drawer>
</template>

<script>
export default {
	props: {
		itemName: {
			type: String,
			default: "Item",
		},
		width: {
			type: [Number, String],
			default: 800,
		},
		api: {
			type: [String, Function],
			default: null,
		},
		getApi: {
			type: [String, Function],
			default: null,
		},
		putApi: {
			type: [String, Function],
			default: null,
		},
		postApi: {
			type: [String, Function],
			default: null,
		},
		getItemMap: {
			type: Function,
			default: null,
		},
		postItemMap: {
			type: Function,
			default: null,
		},
		putItemMap: {
			type: Function,
			default: null,
		},
		default: {
			type: Object,
			default: () => {
				return {};
			},
		},
	},
	data() {
		return {
			drawerModel: false,
			isEdit: false,
			isPosting: false,
			isPutting: false,
			isGetting: false,
			item: this.$cloneDeep(this.default),
			keyId: null,
			confirms: [],
			error: null,
		};
	},
	computed: {
		isPersistent() {
			return this.confirms.length > 0;
		},

		bind() {
			return {
				keyId: this.keyId,
				item: this.item,
			};
		},
	},
	watch: {
		default: {
			handler() {
				this.item = this.$cloneDeep(this.default);
			},
			deep: true,
		},
		drawerModel: {
			handler(v) {
				if (!v) {
					this.$refs.crudDrawer.reset();
					// this.item = this.$cloneDeep(this.default);
					setTimeout(() => {
						this.item = this.$cloneDeep(this.default);
					}, 500);
					this.$emit("closed");
				} else {
					this.$emit("opened");
				}
			},
		},
	},
	// mounted() {
	// 	this.$nuxt.$on("confirm-box", this.confirmBoxHandler);
	// },
	// destroyed() {
	// 	this.$nuxt.$off("confirm-box", this.confirmBoxHandler);
	// },
	methods: {
		joinPaths(arr = []) {
			let query = null;
			arr = arr
				.map((segment) => {
					segment = String(segment);
					// if segment has ? or #, remove it and everything after it
					if (segment.includes("?")) {
						query = segment.substring(segment.indexOf("?"));
						segment = segment.substring(0, segment.indexOf("?"));
					}
					if (segment.endsWith("/")) {
						return segment.substring(0, segment.length - 1);
					}

					return segment;
				})
				.filter((item) => !!item);
			console.log("🚀 ~ file: crud.vue:126 ~ arr=arr.map ~ arr", arr);

			return arr.join("/") + (query || "");
		},
		edit(id) {
			this.isEdit = true;
			this.openDrawer();

			this.keyId = id;
			this.get();
		},
		new(obj) {
			this.isEdit = false;
			if (obj) {
				console.log(obj);
				this.item = { ...this.item, ...obj };
			}
			this.openDrawer();
		},
		get() {
			this.error = null;
			this.isGetting = true;

			if (this.getApi === false) {
				this.isGetting = false;
				return;
			}

			const url = this.getApi
				? typeof this.getApi === "function"
					? this.getApi(this.bind)
					: this.getApi
				: this.joinPaths([this.api, this.keyId]);

			this.$axios
				.$get(url)
				.then((res) => {
					if (this.getItemMap) res = this.getItemMap(res);
					// Object.assign(this.item, res);
					this.$set(this, "item", res);
					this.$nextTick(() => {
						this.$emit("loaded", res);
					});
				})
				.catch((e) => {
					this.genericErrorHandler(e);
					this.error = e;
				})
				.finally(() => {
					this.isGetting = false;
				});
			// setTimeout(() => {
			// 	this.isGetting = false;
			// }, 500);
		},

		post() {
			if (!this.$refs.crudDrawer.validate()) return;

			this.isPosting = true;
			const url = this.postApi
				? typeof this.postApi === "function"
					? this.postApi(this.bind)
					: this.postApi
				: this.joinPaths([this.api]);

			const item = this.postItemMap ? this.postItemMap(this.$cloneDeep(this.item)) : this.$cloneDeep(this.item);

			this.$axios
				.$post(url, item)
				.then((res) => {
					this.close();
					this.$toast.success(`${this.itemName} has been created successfully`);
					this.$emit("created", res);
				})
				.catch((e) => {
					this.genericErrorHandler(e, this.$refs.crudDrawer.$form);
				})
				.finally(() => {
					this.isPosting = false;
				});
		},
		put() {
			console.log("put", this.$refs.crudDrawer.validate());
			if (!this.$refs.crudDrawer.validate()) return;
			this.isPutting = true;
			const url = this.putApi
				? typeof this.putApi === "function"
					? this.putApi(this.bind)
					: this.putApi
				: this.joinPaths([this.api, this.keyId]);
			const item = this.putItemMap ? this.putItemMap(this.$cloneDeep(this.item)) : this.$cloneDeep(this.item);
			this.$axios
				.$put(url, item)
				.then((res) => {
					this.isPutting = false;
					this.close();
					this.$toast.success(`${this.itemName} has been updated successfully`);
					this.$emit("updated", res);
				})
				.catch((e) => {
					this.genericErrorHandler(e, this.$refs.crudDrawer.$form);
				})
				.finally(() => {
					this.isPutting = false;
				});
		},
		delete(data) {
			if (typeof data === "object") {
				this.keyId = data.id;
			} else {
				this.keyId = data;
			}

			this.$confirm(null, { title: "Are you sure to delete the item?"}).then(
				(isAccepted) => {
					if (isAccepted) {
						this._delete();
					}
				}
			);

			// this._delete();
		},
		_delete() {
			this.isDeleting = true;

			this.$axios
				.$delete(this.joinPaths([this.api, this.keyId]), this.item)
				.then((res) => {
					this.isDeleting = false;

					this.$toast.success(`${this.itemName} has been deleted successfully`);
					this.$emit("deleted", res);
				})
				.catch((e) => {
					this.genericErrorHandler(e);
				});
		},

		close() {
			this.drawerModel = false;
		},

		closeDrawer() {
			this.drawerModel = false;
		},
		openDrawer() {
			this.drawerModel = true;
		},
		confirmBoxHandler(obj) {
			if (obj.value === true) {
				this.confirms.push(obj.id);
			} else {
				this.confirms = this.confirms.filter((id) => id !== obj.id);
			}
		},
		reset() {
			this.item = this.$cloneDeep(this.default);
		},
	},
};
</script>
