<template>
		<div>
			<Teleport to="#actions">
			<v-btn color="success" depressed @click="$refs.crud.new()">
				<v-icon left>mdi-plus</v-icon>
				Add New
		   </v-btn>
		</Teleport>
			<data-table ref="dataTable"  :columns="columns" :api="`/v1/portal/products/${this.$route.params.id}`">
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="'Delete'" small icon @click="$refs.crud.delete(item.id)">
							<v-icon small>mdi-delete</v-icon>
						</v-btn>
						<v-btn v-tooltip="'Edit'" small icon @click="$refs.crud.edit(item.id)">
							<v-icon small>mdi-pencil</v-icon>
						</v-btn>
					</div>
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>
			</data-table>

			<crud
				ref="crud"
				v-slot="{ item }"
				width="400"
				:default="defaultItem"
				item-name="Product"
				:api="`/v1/portal/products/${this.$route.params.id}`"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>

				<vc-select
					v-model="item.factory_id"
					name="factory_id"
					api="/v1/lookups/factories"
					label="Factory Name"
					:rules="[$rules.required('Factory Name')]"
				></vc-select>

				<field-text-field
					v-model="item.price"
					name="price"
					label="Product Price"
					:rules="[$rules.required('price')]"
				></field-text-field>

				<field-text-field
					v-model="item.code"
					name="code"
					label="Product Code"
					:rules="[$rules.required('code')]"
				></field-text-field>

				<field-text-field
					v-model="item.description"
					name="description"
					label="Description"
					:rules="[$rules.required('description')]"
				></field-text-field>

			</crud>
		</div>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "Product Name",
					sortable: true,
					value: "name",
				},
				{
					text: "Factory Name",
					sortable: true,
					value: "factory_id",
				},
				{
					text: "Price",
					sortable: true,
					value: "price",
				},
				{
					text: "Quantity",
					sortable: true,
					value: "quantity",
				},
				{
					text: "Description",
					sortable: true,
					value: "description",
				},
			    {
			    	text: "Created at",
			    	sortable: true,
			    	value: "created_at",
			    },
				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],
			defaultItem: [
				{
					id: null,
					name: null,
					code: null,
					price: null,
					description: null,
					sub_category_id: null
				}
			],
		};
	},
	computed: {},
	watch: {},
	
	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

