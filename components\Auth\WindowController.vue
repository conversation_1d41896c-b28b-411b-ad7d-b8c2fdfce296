<template>
	<v-window v-model="windowModel">
		<v-window-item value="main">
			<slot v-bind="{ setWindow }" />
		</v-window-item>
		<v-window-item value="loading">
			<logging-in />
		</v-window-item>
		<v-window-item value="otp">
			<auth-otp />
		</v-window-item>
	</v-window>
</template>

<script>
export default {
	data() {
		return {
			windowModel: "main",
		};
	},

	methods: {
		// disclaimerHandler(v) {
		// 	this.windowModel = v ? "disclaimer" : "main";
		// },
		setWindow(v) {
			this.windowModel = v;
		},
	},
};
</script>

