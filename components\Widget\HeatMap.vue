<template>
	<v-card>
		<v-card-text>
			<v-row>
				<v-col cols="12" md="7">
					<heat-map country-stroke-color="#fff" :high-color="highColor" :country-data="itemsObj" />
				</v-col>
				<v-col offset="1" md="4">
					<v-card-title>
						<span> Clients by Country </span>
						<v-spacer></v-spacer>
						<!-- <span class="text-subtitle-1 font-weight-bold">60,000 <span class="text-caption">Clients</span></span> -->
					</v-card-title>
					<v-list flat disabled dense>
						<v-list-item-group v-model="selectedItem" color="primary">
							<template v-for="(item, i) in items.slice(0, 10)">
								<v-list-item :key="item.code_2 + i">
									<v-list-item-icon>
										<img
											class="rounded-sm elevation-1"
											:src="'/flag-icons/flags/4x3/' + item.code_2.toLowerCase() + '.svg'"
										/>
									</v-list-item-icon>
									<v-list-item-content>
										<v-list-item-title>
											<b> {{ item.country }}</b>
										</v-list-item-title>
									</v-list-item-content>
									<v-list-item-action> {{ item.count }} </v-list-item-action>
								</v-list-item>
							</template>
						</v-list-item-group>
					</v-list>
				</v-col>
			</v-row>
			<v-tooltip v-for="item in items" :key="item.code_2" :activator="'#' + item.code_2" top
				>{{ item.country }}: {{ item.count }}</v-tooltip
			>
		</v-card-text>
	</v-card>
</template>

<script>
import heatMap from "vue-geo-heat-maps";

export default {
	components: {
		heatMap,
	},
	data() {
		return {
			highColor: this.$vuetify.theme.defaults.light.primary,
			tabModel: "Registration",

			items: [],

			dataObj: {},
			generateKeyMap: 0,
		};
	},

	computed: {
		itemsObj() {
			const obj = {};
			this.items.forEach((item) => {
				obj[item.code_2] = item.count;
			});
			return obj;
		},
	},

	mounted() {
		this.generateData();
	},

	methods: {
		generateData() {
			this.$axios.$get("/v1/portal/charts/clients-per-country").then((resp) => {
				this.items = resp.datasets[0].data;
			});
		},
	},
};
</script>
