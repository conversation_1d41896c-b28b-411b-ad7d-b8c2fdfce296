<template>
	<v-autocomplete v-bind="$attrs" v-model="localValue" item-value="id" item-text="alias" :items="items" :loading="$fetchState.pending">
		<template #item="{ attr, on, item, selected }">
			<v-list-item v-bind="attr" :input-value="selected" :disabled="['disabled'].includes(item.status)" v-on="on">
				<!-- <v-list-item-icon>
					{{ item.type }}
				</v-list-item-icon> -->
				<v-list-item-content>
					<v-list-item-title> {{ item.alias }} ({{ item.accountNumber }}) </v-list-item-title>
					<v-list-item-subtitle>
						<v-chip x-small color="grey" dark>
							{{ item.type }}
						</v-chip>
						<v-chip light x-small color="grey lighten-4">
							<money>{{ item.balance }}</money>
						</v-chip>
						<v-spacer />
					</v-list-item-subtitle>
				</v-list-item-content>
				<v-list-item-action>
					<status solid :items="statuses">
						{{ item.status }}
					</status>
				</v-list-item-action>
			</v-list-item>
		</template>
	</v-autocomplete>
</template>

<script>
export default {
	props: {
		value: {
			type: [Number, String],
			default: null,
		},
	},
	data() {
		return {
			items: [],
			statuses: [
				{ text: "Active", name: "active", color: "success" },
				{ text: "Liquidated", name: "disabled", color: "warning" },
			],
		};
	},
	async fetch() {
		/* eslint-disable-next-line */
		await new Promise((resolve, reject) => {
			/* eslint-disable-next-line */
			setTimeout(() => {
				this.items = [
					{
						id: 1,
						alias: "my main account",
						accountNumber: ********,
						type: "MT5",
						balance: 1630,
						currency: "USD",
						status: "active",
					},
					{
						id: 2,
						alias: "my testing account",
						accountNumber: ********,
						type: "MT5",
						balance: 1330,
						currency: "JOD",
						status: "disabled",
					},
				];
				resolve();
			}, 1000);
		});
	},
	computed: {
		localValue: {
			get() {
				return this.value;
			},
			set(v) {
				this.$emit("input", v);
			},
		},
	},
};
</script>

