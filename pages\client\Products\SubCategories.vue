<template>
	<page :title="'Product Sub Categories for ' + categoryName" desc="">
		<template #actions>
			<v-btn v-if="checkPermission('product_sub_categories.create')" color="success" @click="$refs.crud.new()">
				<v-icon left>mdi-plus</v-icon>Add Sub Category
			</v-btn>
		</template>
		<div>
			<data-table
				v-if="checkPermission('product_sub_categories.view')"
				ref="dataTable"
				:columns="columns"
				:api="`/v1/portal/product-sub-categories?category_id=${$route.params.category_id}`"
			>
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn
							v-if="checkPermission('product_sub_categories.delete')"
							v-tooltip="'Delete'"
							small
							icon
							@click="$refs.crud.delete(item.id)"
						>
							<v-icon small>mdi-delete</v-icon>
						</v-btn>
						<v-btn
							v-if="checkPermission('product_sub_categories.update')"
							v-tooltip="'Edit'"
							small
							icon
							@click="$refs.crud.edit(item.id)"
						>
							<v-icon small>mdi-pencil</v-icon>
						</v-btn>
						<v-btn v-tooltip="'Show'" icon small :to="localePath({ name: 'products', params: { sub_category_id: item.id } })">
							<v-icon small>mdi-eye</v-icon>
						</v-btn>
					</div>
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>

				<template #item.photo="{ item }">
					<v-img :src="item.photo" style="max-width: 150px"></v-img>
				</template>
			</data-table>
			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>

			<crud
				ref="crud"
				v-slot="{ item }"
				width="400"
				:default="defaultItem"
				item-name="product sub categories"
				api="/v1/portal/product-sub-categories"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<field-text-field
					v-model="item.name"
					name="name"
					label="Sub category Name"
					:rules="[$rules.required('name')]"
				></field-text-field>

				<!-- <vc-select
					v-model="item.category_id"
					name="category_id"
					api="/v1/lookups/product-categories"
					item-text="text"
					item-value="value"
					label="Product Category"
					:rules="[$rules.required('Category')]"
				></vc-select> -->

				<v-textarea v-model="item.description" :counter="300" name="description" label="Description" required></v-textarea>

				<field-upload v-model="item.photo" :value="value" label="Photo" @click="handler" @input="update"></field-upload>
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "Image",
					sortable: true,
					value: "photo",
				},
				{
					text: "Sub Category Name",
					sortable: true,
					value: "name",
				},
				{
					text: "Main Category",
					sortable: true,
					value: "category.name",
				},
				{
					text: "Description",
					sortable: true,
					value: "description",
				},
				{
					text: "Created at",
					sortable: true,
					value: "created_at",
				},
				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],
			defaultItem: {
				name: null,
				photo: null,
				category_id: this.$route.params.category_id,
			},
			categoryName: null,
		};
	},
	computed: {},
	watch: {},

	created() {
		this.$axios.$get(`/v1/lookups/product-categories`).then((resp) => {
			const productCategories = resp;

			// Find the category that matches the route param
			const matchingCategory = productCategories.find((category) => category.value === parseInt(this.$route.params.category_id));

			if (matchingCategory) {
				// Do something with the matching category text
				console.log(matchingCategory.text); // For example, log the text
				// You can store it in a data property or use it directly in your template
				this.categoryName = matchingCategory.text;
			}
		});
	},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

