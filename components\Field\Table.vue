<template>
	<div>
		<v-toolbar flat>
			<v-spacer></v-spacer>
			<v-btn v-if="configs.showAddNewButton" color="primary" @click="$refs.newCrud.edit()">{{ configs.addNewButton }}</v-btn>
		</v-toolbar>
		<v-data-table :headers="parsedColumns" :items="items">
			<template #item.actions="{ item }">
				<v-btn icon @click="$refs.editCrud.edit(item.id)"><v-icon>mdi-pencil</v-icon></v-btn>
				<v-btn icon @click="$refs.editCrud.delete(item.id)"><v-icon>mdi-trash-can</v-icon></v-btn>
			</template>
		</v-data-table>
		<crud
			ref="newCrud"
			:items-key="false"
			item-name="item"
			:api="configs.addNewApiUrl"
			:get-api="configs.addNewApiUrl"
			:get-item-map="getHandler"
			:post-item-map="postHandler"
			@updated="$emit('refresh')"
			@created="$emit('refresh')"
		>
			<template #default="{ item }">
				<dynamic-form :form="item" />
			</template>
			<template #actions="{ post, isPosting }">
				<v-spacer></v-spacer> <v-btn color="primary" :loading="isPosting" text @click="post">{{ $t("common.save") }}</v-btn>
			</template>
		</crud>

		<crud
			ref="editCrud"
			v-slot="{ item }"
			:items-key="false"
			item-name="item"
			:api="configs.editApiUrl"
			:get-item-map="getHandler"
			:post-item-map="postHandler"
			:put-item-map="postHandler"
			@updated="$emit('refresh')"
			@created="$emit('refresh')"
			@deleted="$emit('refresh')"
		>
			<dynamic-form :form="item" />
		</crud>
	</div>
</template>

<script>
export default {
	props: {
		configs: {
			type: Object,
			default: () => ({}),
		},
		items: {
			type: Array,
			default: () => [],
		},
		columns: {
			type: Array,
			default: () => [],
		},
	},
	computed: {
		parsedColumns() {
			return [...this.columns, { text: "", value: "actions" }];
		},
	},
	methods: {
		postHandler(item) {
			const clonedItem = this.$cloneDeep(item);
			for (const key in clonedItem) {
				clonedItem[key] = clonedItem[key].value;
			}

			return clonedItem;
		},
		getHandler(item) {
			for (const key in item) {
				item[key].component = this.mapComponent(item[key].component);
			}

			return item;
		},
	},
};
</script>
