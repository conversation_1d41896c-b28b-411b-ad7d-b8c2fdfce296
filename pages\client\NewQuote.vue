<template>
	<page
		:title="
			quoteData.order
				? quoteData.order.po_reference_number
					? quoteData.order.po_reference_number + ' - ' + quoteData.project_name + ' - ' + quoteData.client?.name
					: quoteData.reference_number + ' - ' + quoteData.project_name + ' - ' + quoteData.client?.name
				: 'Quote'
		"
		title-size="headline"
		transparent
	>
		<template v-if="quoteData.id" #actions>
			<v-btn color="primary" :disabled="quoteData.can_pick_it" @click="pickItem(quoteData.id)">
				<v-icon left>mdi-account-arrow-left-outline</v-icon>Pick
			</v-btn>
		</template>
		<v-row>
			<v-col md="9" class="my-0 py-0">
				<v-stepper v-model="step" non-linear color="success">
					<v-stepper-header style="display: flex !important; flex-flow: nowrap !important">
						<v-stepper-step editable :complete="step > 1" step="1" @click="backFunction(1)"> Open Project </v-stepper-step>

						<v-divider></v-divider>

						<v-stepper-step
							:editable="checkPermission('quotes.process_take_off')"
							:complete="step > 2"
							step="2"
							@click="checkPermission('quotes.process_take_off') ? backFunction(2) : ''"
						>
							Take Off
						</v-stepper-step>

						<v-divider></v-divider>

						<v-stepper-step
							:editable="checkPermission('quotes.process_factory_costing')"
							:complete="step > 3"
							step="3"
							@click="checkPermission('quotes.process_factory_costing') ? backFunction(3) : ''"
						>
							Factory Costing
						</v-stepper-step>

						<v-divider></v-divider>

						<v-stepper-step
							:editable="checkPermission('quotes.process_pricing')"
							:complete="step > 4"
							step="4"
							@click="checkPermission('quotes.process_pricing') ? backFunction(4) : ''"
						>
							Pricing
						</v-stepper-step>

						<v-divider></v-divider>

						<v-stepper-step
							:editable="checkPermission('quotes.process_shipping')"
							:complete="step > 5"
							step="5"
							@click="checkPermission('quotes.process_shipping') ? backFunction(5) : ''"
						>
							Shipping
						</v-stepper-step>

						<v-divider></v-divider>

						<v-stepper-step
							:editable="checkPermission('quotes.process_payment')"
							:complete="step > 6"
							step="6"
							@click="checkPermission('quotes.process_payment') ? backFunction(6) : ''"
						>
							Payment
						</v-stepper-step>

						<v-divider></v-divider>

						<v-stepper-step
							:editable="checkPermission('quotes.generate_formal_quote')"
							:complete="step > 7"
							step="7"
							@click="checkPermission('quotes.generate_formal_quote') ? backFunction(7) : ''"
						>
							Formal Quote
						</v-stepper-step>

						<v-divider></v-divider>

						<v-stepper-step
							:editable="checkPermission('quotes.convert_to_order')"
							:complete="step > 8"
							step="8"
							@click="checkPermission('quotes.convert_to_order') ? backFunction(8) : ''"
						>
							Order
						</v-stepper-step>
					</v-stepper-header>

					<v-stepper-items ref="form">
						<v-form ref="stepOneValidation">
							<v-stepper-content step="1">
								<v-row class="mt-4 mb-2">
									<v-col md="3">
										<div class="d-flex align-center">
											<v-icon size="45" left> mdi-office-building-marker</v-icon>
											<vc-select
												v-model="quoteData.branch_id"
												hide-details
												outlined
												dense
												name="branch"
												label="Branch"
												api="/v1/lookups/branches"
												:rules="[$rules.required('Branch Name')]"
											></vc-select>
										</div>
									</v-col>
									<v-spacer />
									<v-col md="3" class="text-end">
										<v-btn small dark @click="ShowClientDialog = true">
											<v-icon small left>mdi-plus</v-icon> add new client
										</v-btn>
									</v-col>
								</v-row>

								<v-card outlined class="mb-12">
									<v-card-text>
										<v-row>
											<v-col md="3" cols="12">
												<vc-autocomplete
													v-model="quoteData.status"
													outlined
													name="Quote Status"
													item-text="text"
													item-value="value"
													:items="statusItems"
													label="Status"
												></vc-autocomplete>
											</v-col>
											<v-col md="3" cols="12">
												<field-text-field
													key="project_name"
													v-model="quoteData.project_name"
													outlined
													name="project_name"
													label="Project Name"
													:rules="[$rules.required('Project Name')]"
												></field-text-field>
											</v-col>
											<v-col md="3" cols="12">
												<vc-autocomplete
													v-model="quoteData.factory_id"
													outlined
													api="/v1/lookups/factories"
													label="Source - Factory"
													name="factory_id"
													item-text="text"
													item-value="value"
													:rules="[$rules.required('Source')]"
												>
												</vc-autocomplete>
											</v-col>
											<v-col md="3" cols="12">
												<vc-autocomplete
													:key="quoteData.factory_id"
													v-model="quoteData.product_category_id"
													outlined
													:api="`/v1/lookups/product-categories?factory_id=${quoteData.factory_id}`"
													label="Product Category"
													name="Product Category"
													item-text="text"
													item-value="value"
													:rules="[$rules.required('product category')]"
												>
												</vc-autocomplete>
											</v-col>
											<v-col md="3" cols="12">
												<vc-autocomplete
													v-model="quoteData.client_id"
													outlined
													clearable
													deletable-chips
													small-chips
													api="/v1/lookups/client-contact-people"
													label="Client Name"
													name="client_id"
													item-text="meta.client_name"
													item-value="meta.client_id"
													:filter="clientFilter"
													:rules="[$rules.required('client')]"
													@change="resetContactPeople()"
												>
													<template #item="{ item }">
														<v-list-item-content>
															<v-list-item-title>
																<span>
																	<v-icon small class="ms-1"> mdi-office-building</v-icon>
																	{{ item.meta?.client_name }}

																	<span class="mx-1 body-2">
																		<v-icon small>mdi-account</v-icon>
																		{{ item.text }}
																	</span>
																	<span class="ms-1 body-2"> # {{ item.meta?.client_reference }} </span>
																</span>
															</v-list-item-title>
														</v-list-item-content>
													</template>
													<template #selection="{ item }">
														<v-list-item-content>
															<v-list-item-title>
																{{ item.meta?.client_name }}
															</v-list-item-title>
														</v-list-item-content>
													</template>
												</vc-autocomplete>
											</v-col>

											<v-col md="3" cols="12">
												<vc-autocomplete
													:key="quoteData.client_id"
													v-model="quoteData.contact_people"
													small-chips
													deletable-chips
													clearable
													multiple
													outlined
													:api="`/v1/lookups/client-contact-people?client_id=${quoteData.client_id}`"
													label="Contact People"
													name="Contact People"
													item-text="text"
													item-value="value"
													:rules="[$rules.required('Contact People')]"
												>
													<template #item="{ item }">
														<v-list-item-content>
															<v-list-item-title>
																<span class="mx-1 body-2">
																	{{ item.text }}
																</span>
																<span class="ms-1 body-2"> # {{ item.meta?.client_reference }} </span>
															</v-list-item-title>
														</v-list-item-content>
													</template>
												</vc-autocomplete>
											</v-col>

											<v-col md="3" cols="12">
												<vc-autocomplete
													key="country_id"
													v-model="quoteData.country_id"
													name="country_id"
													api="/v1/lookups/countries"
													label="Project Country"
													outlined
													:rules="[$rules.required('Project Country')]"
												></vc-autocomplete>
											</v-col>
											<v-col md="3" cols="12">
												<vc-autocomplete
													:key="quoteData.country_id"
													v-model="quoteData.city_id"
													name="city_id"
													:api="`/v1/lookups/city?country_id=${quoteData.country_id}`"
													label="Project City"
													outlined
												></vc-autocomplete>
											</v-col>
										</v-row>
									</v-card-text>
								</v-card>

								<div class="d-flex mt-6 steep-action">
									<v-spacer></v-spacer>
									<v-btn color="primary" @click="validationStep('define_project', 2)"> Next </v-btn>
								</div>

								<v-dialog v-model="ShowClientDialog" absolute width="900px">
									<v-card>
										<v-card-title>
											<div class="mb-3">New Client</div>
											<v-spacer></v-spacer>
											<v-btn icon @click="ShowClientDialog = false">
												<v-icon>mdi-close</v-icon>
											</v-btn>
										</v-card-title>
										<v-card-text>
											<v-form ref="newClientValidate">
												<v-card outlined>
													<v-card-title>
														<span class="font-weight-bold subtitle-1"> Client Section:</span>
													</v-card-title>
													<v-card-text>
														<v-container>
															<v-row>
																<v-col md="4">
																	<field-text-field
																		v-model="newClientData.name"
																		outlined
																		hide-details
																		dense
																		name="name"
																		label="Client Name"
																		:rules="[$rules.required('name')]"
																	></field-text-field>
																</v-col>
																<v-col md="4">
																	<field-text-field
																		v-model="newClientData.email"
																		outlined
																		hide-details
																		dense
																		name="email"
																		label="Client Email"
																		:rules="[$rules.required('email'), $rules.email('email')]"
																	></field-text-field>
																</v-col>
																<v-col md="4">
																	<vc-autocomplete
																		v-model="newClientData.country_id"
																		outlined
																		hide-details
																		dense
																		name="country"
																		api="/v1/lookups/countries"
																		item-text="text"
																		item-value="value"
																		label="Client Country"
																	></vc-autocomplete>
																</v-col>
															</v-row>
														</v-container>
													</v-card-text>
												</v-card>

												<v-card outlined class="mt-4">
													<v-card-title>
														<span class="font-weight-bold subtitle-1"> Contact Persons:</span>
													</v-card-title>
													<v-card-text>
														<v-container>
															<v-expansion-panels>
																<v-expansion-panel
																	v-for="(field, i) in newClientData.contact_people"
																	:key="field.id"
																>
																	<v-expansion-panel-header>
																		<span>Contact Person {{ i + 1 }}</span>
																		<div class="float-end text-end">
																			<v-btn
																				class="me-2"
																				small
																				icon
																				color="primary"
																				@click="newClientData.contact_people.splice(i, 1)"
																			>
																				<v-icon>mdi-trash-can</v-icon>
																			</v-btn>
																		</div>
																	</v-expansion-panel-header>
																	<v-expansion-panel-content>
																		<v-row>
																			<v-col md="12">
																				<v-text-field
																					:key="'name' + i"
																					v-model="field.name"
																					class="mb-2"
																					outlined
																					hide-details
																					dense
																					label="Contact People Name"
																				>
																				</v-text-field>
																			</v-col>
																		</v-row>

																		<v-row>
																			<v-col md="4">
																				<v-text-field
																					:key="'number' + i"
																					v-model="field.number"
																					class="mb-2"
																					outlined
																					hide-details
																					dense
																					label="Contact Number"
																				>
																				</v-text-field>
																			</v-col>

																			<v-col md="4">
																				<v-text-field
																					:key="'email_one' + i"
																					v-model="field.email_one"
																					class="mb-2"
																					outlined
																					hide-details
																					dense
																					label="Email One"
																				>
																				</v-text-field>
																			</v-col>
																			<v-col md="4">
																				<v-text-field
																					:key="'email_two' + i"
																					v-model="field.email_two"
																					class="mb-2"
																					outlined
																					hide-details
																					dense
																					label="Email Two"
																				>
																				</v-text-field>
																			</v-col>
																		</v-row>

																		<v-row>
																			<v-col md="4">
																				<v-text-field
																					:key="'phone' + i"
																					v-model="field.phone"
																					class="mb-2"
																					outlined
																					hide-details
																					dense
																					label="Phone"
																				>
																				</v-text-field>
																			</v-col>

																			<v-col md="4">
																				<v-text-field
																					:key="'mobile' + i"
																					v-model="field.mobile"
																					class="mb-2"
																					outlined
																					hide-details
																					dense
																					label="Mobile"
																				>
																				</v-text-field>
																			</v-col>

																			<v-col md="4">
																				<v-text-field
																					:key="'fax' + i"
																					v-model="field.fax"
																					class="mb-2"
																					outlined
																					hide-details
																					dense
																					label="Fax"
																				>
																				</v-text-field>
																			</v-col>
																		</v-row>

																		<v-row>
																			<v-col md="4">
																				<v-text-field
																					:key="'position' + i"
																					v-model="field.position"
																					class="mb-2"
																					outlined
																					hide-details
																					dense
																					label="Position"
																				>
																				</v-text-field>
																			</v-col>

																			<v-col md="4">
																				<v-text-field
																					:key="'address_one' + i"
																					v-model="field.address_one"
																					class="mb-2"
																					outlined
																					hide-details
																					dense
																					label="Address One"
																				>
																				</v-text-field>
																			</v-col>

																			<v-col md="4">
																				<v-text-field
																					:key="'address_two' + i"
																					v-model="field.address_two"
																					class="mb-2"
																					outlined
																					hide-details
																					dense
																					label="Address Two"
																				>
																				</v-text-field>
																			</v-col>
																		</v-row>
																	</v-expansion-panel-content>
																</v-expansion-panel>
															</v-expansion-panels>

															<v-btn
																v-tooltip="'add new'"
																class="success mt-3"
																small
																dence
																@click="
																	newClientData.contact_people.push({
																		name: null,
																		email_one: null,
																		email_two: null,
																		phone: null,
																		mobile: null,
																		fax: null,
																		position: null,
																		address_one: null,
																		address_two: null,
																	})
																"
															>
																<v-icon left>mdi-plus</v-icon>
																Add New Contact
															</v-btn>
														</v-container>
													</v-card-text>
												</v-card>
											</v-form>
										</v-card-text>
										<v-card-actions>
											<v-spacer></v-spacer>
											<v-btn text @click="ShowClientDialog = false">Cancel</v-btn>
											<v-btn color="primary" text @click="addClientMethod()">Save</v-btn>
										</v-card-actions>
									</v-card>
								</v-dialog>
							</v-stepper-content>
						</v-form>

						<v-stepper-content step="2">
							<div v-if="checkPermission('quotes.process_take_off')">
								<div class="text-end mb-3">
									<v-btn
										small
										color="success"
										@click="takeOffHandler(quoteData.products.length, 'new', quoteData.products.length)"
									>
										<v-icon left>mdi-plus</v-icon> Add New Products
									</v-btn>
									<v-btn small dark @click="copyTable('factoryPrice')">Copy Table </v-btn>
									<v-btn small dark @click="exportPdf('takeOffTable')"> Export PDF</v-btn>
								</div>

								<v-card id="takeOffTable" outlined class="mb-12">
									<v-card-title id="hiddenSearch">
										Products
										<v-spacer />
										<v-text-field
											v-model="search"
											append-icon="mdi-magnify"
											label="Search"
											single-line
											hide-details
											clearable
											class="hidden-on-copy"
											style="max-width: 300px"
										></v-text-field>
									</v-card-title>
									<v-data-table
										id="factoryPrice"
										ref="factoryPrice"
										class="custom-table-header"
										:headers="takeOffTable"
										:items="quoteData.products"
										:search="search"
									>
										<template #item.actions="{ item, index }">
											<div class="hidden-on-copy">
												<v-btn icon @click="takeOffHandler(item.id, 'edit', index)">
													<v-icon color="primary" small>mdi-pencil</v-icon>
												</v-btn>
												<v-btn icon @click="quoteData.products.splice(index, 1)">
													<v-icon color="primary" small>mdi-delete</v-icon>
												</v-btn>
											</div>
										</template>

										<template #item.id="{ index }">
											{{ ++index }}
										</template>

										<template #item.description="{ item }">
											<div class="cutting-paragraph">{{ item.description }}</div>
										</template>

										<template #item.is_optional="{ item }">
											<v-icon class="hidden-on-copy" :color="item.is_optional ? 'success' : 'red'">
												{{ item.is_optional ? "mdi-check" : "mdi-close" }}
											</v-icon>
										</template>
										<template #item.is_hidden="{ item }">
											<v-icon class="hidden-on-copy" :color="item.is_hidden ? 'success' : 'red'">
												{{ item.is_hidden ? "mdi-check" : "mdi-close" }}
											</v-icon>
										</template>
									</v-data-table>
								</v-card>

								<div class="d-flex mt-6 steep-action">
									<!-- <v-btn text @click="step = 1"> Back </v-btn> -->
									<v-btn class="mt-8" text @click="backFunction(1)"> Back </v-btn>
									<v-spacer></v-spacer>
									<v-btn color="primary" @click="validationStep('take_off', 3)"> Next </v-btn>
								</div>

								<v-dialog id="selector" ref="selector" v-model="selector" persistent absolute width="1000px">
									<v-card>
										<v-card-title>
											<div>
												<span v-if="step == 2">Take Off</span>
												<span v-else-if="step == 3">Factory Costing</span>
												<span v-else-if="step == 4">Pricing</span>
											</div>
											<v-spacer></v-spacer>
											<v-btn icon @click="selector = false">
												<v-icon>mdi-close</v-icon>
											</v-btn>
										</v-card-title>
										<v-card-text class="mt-8">
											<v-form ref="selectorFormValidate">
												<div class="d-flex justify-end">
													<div>
														<input ref="filepicker" type="file" class="d-none" @change="uploadFile" />

														<v-menu rounded="rounded" offset-y>
															<template #activator="{ attrs, on }">
																<v-btn
																	:disabled="step !== 2"
																	small
																	class="mb-3 mt-5 me-3"
																	v-bind="attrs"
																	v-on="on"
																>
																	<v-icon left>mdi-download</v-icon> As Template
																</v-btn>
															</template>
															<v-list>
																<v-list-item dense link @click="exportTemplate">
																	<v-list-item-title>
																		<v-icon small left>mdi-numeric-1-circle</v-icon>
																		<span>Download Template</span>
																	</v-list-item-title>
																</v-list-item>
																<v-list-item dense link @click="openFilePicker">
																	<v-list-item-title>
																		<v-icon small left>mdi-numeric-2-circle</v-icon>
																		<span>Import Template</span>
																	</v-list-item-title>
																</v-list-item>
															</v-list>
														</v-menu>
													</div>

													<v-btn
														class="mb-3 mt-5"
														small
														color="success"
														:disabled="step !== 2"
														@click="
															selectorComponent === 'selector-cat-6-fact-3'
																? quoteData.products.push({
																		product_id: null,
																		specifications: {
																			options: {},
																		},
																  })
																: quoteData.products.push({
																		product_id: null,
																		specifications: {},
																  })
														"
													>
														<v-icon left>mdi-plus</v-icon> Add New Product
													</v-btn>
												</div>
												<v-expansion-panels v-model="selectorPanel" class="d-block">
													<v-form ref="stepTwoValidation" lazy-validation>
														<v-expansion-panel
															v-for="(field, i) in quoteData.products"
															:id="`product-${i + 1}`"
															:key="i + 'selectorPanel' + selectorRandomKeyComputed"
															class="pa-1 mb-3"
														>
															<div>
																<v-expansion-panel-header>
																	<div class="d-flex width-100">
																		<span class="mt-2 font-weight-bold"> Product {{ i + 1 }}</span>
																		<span class="ms-4 mt-2">
																			{{ quoteData.products[i]?.part_number }}
																		</span>
																		<v-spacer />

																		<v-btn
																			small
																			:disabled="step !== 3"
																			color="primary"
																			outlined
																			class="me-3"
																			@click.stop="productActivitesMethod(field)"
																		>
																			<v-icon left small>mdi-history</v-icon>
																			Activities
																		</v-btn>
																		<v-btn
																			small
																			:disabled="step == 3"
																			color="primary"
																			outlined
																			class="me-3"
																			@click="quoteData.products.splice(i, 1)"
																		>
																			<v-icon left small>mdi-delete</v-icon>
																			Remove
																		</v-btn>
																	</div>
																</v-expansion-panel-header>
																<v-expansion-panel-content :key="selectorRandomKeyComputed + i">
																	<v-row>
																		<v-col cols="12" md="12" class="mt-2">
																			<!-- factory section -->
																			<v-card v-if="step == 3" outlined class="my-5 justify-center">
																				<v-card-title class="text-body-1 font-weight-medium">
																					Factory Details
																				</v-card-title>
																				<v-card-text>
																					<v-row>
																						<v-col md="12">
																							<v-checkbox
																								v-model="field.is_compared"
																								class="mt-0 pt-0"
																								hide-details
																								label="Is Compared"
																							></v-checkbox>
																						</v-col>
																						<v-col md="4">
																							<field-text-field
																								:key="i + 'factory_part_number'"
																								v-model="field.factory_part_number"
																								outlined
																								hide-details
																								name="factory_part_number"
																								label="Factory Part Number"
																							></field-text-field>
																						</v-col>
																						<v-col md="4">
																							<field-text-field
																								:key="i + 'assumed_price'"
																								v-model="field.assumed_price"
																								outlined
																								hide-details
																								type="number"
																								min="0"
																								name="assumed_price"
																								label="Assumed Price"
																								:suffix="
																									quoteData.factory_currency?.symbol
																										? quoteData.factory_currency?.symbol
																										: quoteData.factory?.currency
																												?.symbol
																								"
																							>
																							</field-text-field>
																						</v-col>
																						<v-col md="4">
																							<field-text-field
																								:key="i + 'factory_price'"
																								v-model="field.factory_price"
																								outlined
																								hide-details
																								type="number"
																								min="0"
																								name="factory_price"
																								label="Factory Price"
																								:suffix="
																									quoteData.factory_currency?.symbol
																										? quoteData.factory_currency?.symbol
																										: quoteData.factory?.currency
																												?.symbol
																								"
																							>
																							</field-text-field>
																						</v-col>

																						<v-col md="12">
																							<!-- -->
																							<v-textarea
																								:key="i + 'factory_description'"
																								v-model="field.factory_description"
																								outlined
																								hide-details
																								name="factory_description"
																								label="Factory Description"
																								required
																							></v-textarea>
																						</v-col>
																					</v-row>
																				</v-card-text>
																			</v-card>
																			<v-row class="fill-height">
																				<v-col
																					:md="
																						selectorComponent === 'selector-cat-1-fact-1' ||
																						selectorComponent === 'selector-cat-4-fact-1'
																							? 12
																							: 6
																					"
																					cols="12"
																				>
																					<v-card
																						outlined
																						class="mb-3 justify-center fill-height"
																					>
																						<v-card-text>
																							<v-row>
																								<v-col cols="12" md="12" class="mt-2">
																									<div class="d-flex mb-0">
																										<div class="me-4">
																											<v-checkbox
																												v-model="field.is_optional"
																												class="mt-0 pt-0"
																												hide-details
																												label="Is Optional Product"
																											></v-checkbox>
																										</div>
																										<div>
																											<v-checkbox
																												v-model="field.is_hidden"
																												class="mt-0 pt-0"
																												hide-details
																												label="Is Hidden Product"
																											></v-checkbox>
																										</div>
																									</div>
																								</v-col>

																								<template
																									v-if="
																										selectorComponent ===
																										'selector-cat-1-fact-1'
																									"
																								>
																									<v-col md="6" cols="12">
																										<vc-autocomplete
																											:key="i + 'sub_category_id'"
																											v-model="field.sub_category_id"
																											outlined
																											:api="`/v1/lookups/product-sub-categories?category_id=${productCategory}&factory_id=${quoteData.factory_id}`"
																											label="Sub Categories"
																											name="sub_category_id"
																											item-text="text"
																											item-value="value"
																											hide-details
																											:rules="[
																												$rules.required(
																													'Sub Category'
																												),
																											]"
																										>
																										</vc-autocomplete>
																									</v-col>

																									<v-col md="6" cols="12">
																										<vc-autocomplete
																											:key="field.sub_category_id"
																											v-model="field.group"
																											outlined
																											:api="`/v1/lookups/product-groups?sub_category_id=${field.sub_category_id}`"
																											label="Groups"
																											name="group"
																											item-text="text"
																											item-value="value"
																											hide-details
																											:rules="[
																												$rules.required(
																													'product Groups'
																												),
																											]"
																											@change="getSelectorValues(i)"
																										>
																										</vc-autocomplete>
																									</v-col>
																								</template>
																								<!--***************** selector-cat-4-fact-1 ******************-->
																								<template
																									v-else-if="
																										selectorComponent ===
																										'selector-cat-4-fact-1'
																									"
																								>
																									<!-- <div>
																										<pre>{{ quoteData.products }}</pre>
																									</div> -->
																									<v-col cols="12">
																										<vc-autocomplete
																											:key="i + 'product_id'"
																											v-model="field.product_id"
																											outlined
																											:api="`/v1/lookups/products`"
																											label="Products"
																											name="products"
																											item-text="text"
																											item-value="value"
																											hide-details
																											:rules="[
																												$rules.required('products'),
																											]"
																											@change="getSelectorValues(i)"
																										>
																										</vc-autocomplete>
																									</v-col>
																								</template>
																								<template v-else>
																									<v-col
																										:cols="
																											selectorComponent !==
																												'selector-cat-5-fact-9' &&
																											selectorComponent !==
																												'selector-cat-6-fact-3' &&
																											selectorComponent !==
																												'selector-cat-1-fact-4'
																												? '6'
																												: '12'
																										"
																									>
																										<vc-autocomplete
																											:key="i + 'sub_category_id'"
																											v-model="field.sub_category_id"
																											outlined
																											:api="
																												!isDefaultSelector
																													? `/v1/lookups/product-sub-categories?category_id=${productCategory}&factory_id=${quoteData.factory_id}`
																													: `/v1/lookups/product-sub-categories?category_id=${productCategory}`
																											"
																											:label="
																												selectorComponent !==
																													'selector-cat-5-fact-9' &&
																												selectorComponent !==
																													'selector-cat-6-fact-3' &&
																												selectorComponent !==
																													'selector-cat-1-fact-1' &&
																												selectorComponent !==
																													'selector-cat-1-fact-4'
																													? 'Sub Categories'
																													: 'Products (sub)'
																											"
																											name="sub_category_id"
																											item-text="text"
																											item-value="value"
																											hide-details
																											class="mb-2"
																											:rules="
																												isDefaultSelector
																													? []
																													: [
																															$rules.required(
																																'Sub Category'
																															),
																													  ]
																											"
																											@change="getProductId(i)"
																										>
																											<!-- :rules="[$rules.required('Sub Category'),]" -->
																										</vc-autocomplete>
																									</v-col>
																								</template>

																								<v-col
																									v-if="
																										selectorComponent ===
																										'selector-cat-1-fact-1'
																									"
																									md="12"
																									cols="12"
																								>
																									<vc-autocomplete
																										:key="field.group"
																										v-model="field.product_id"
																										outlined
																										:api="`/v1/lookups/products?group=${field.group}&sub_category_id=${field.sub_category_id}`"
																										label="Products"
																										name="products"
																										item-text="text"
																										item-value="value"
																										hide-details
																										:rules="[
																											$rules.required('products'),
																										]"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>
																								<v-col
																									v-else-if="
																										selectorComponent !==
																											'selector-cat-5-fact-9' &&
																										selectorComponent !==
																											'selector-cat-6-fact-3' &&
																										selectorComponent !==
																											'selector-cat-1-fact-1' &&
																										selectorComponent !==
																											'selector-cat-1-fact-4' &&
																										selectorComponent !==
																											'selector-cat-4-fact-1'
																									"
																									md="6"
																									cols="12"
																								>
																									<vc-autocomplete
																										:key="field.sub_category_id"
																										v-model="field.product_id"
																										outlined
																										:api="`/v1/lookups/products?sub_category_id=${field.sub_category_id}`"
																										label="Products"
																										name="products"
																										item-text="text"
																										hide-details
																										item-value="value"
																										:rules="
																											isDefaultSelector
																												? []
																												: [
																														$rules.required(
																															'products'
																														),
																												  ]
																										"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>
																							</v-row>
																						</v-card-text>
																					</v-card>
																				</v-col>

																				<v-col
																					v-if="
																						selectorComponent !== 'selector-cat-1-fact-1' &&
																						selectorComponent !== 'selector-cat-4-fact-1'
																					"
																					md="6"
																					cols="12"
																				>
																					<v-card class="mb-4 fill-height" outlined>
																						<v-card-title>General</v-card-title>
																						<v-card-text>
																							<v-row>
																								<v-col md="6" cols="12">
																									<v-text-field
																										:key="i + 'quantity'"
																										v-model="field.quantity"
																										outlined
																										type="number"
																										min="0"
																										name="quantity"
																										label="Quantity"
																										hide-details
																										class="mb-2"
																										:rules="[
																											$rules.required('quantity'),
																										]"
																									></v-text-field>
																								</v-col>
																								<v-col md="6" cols="12">
																									<v-text-field
																										:key="i + 'client_tag'"
																										v-model="field.client_tag"
																										outlined
																										hide-details
																										class="mb-2"
																										name="client_tag"
																										label="Client Tags"
																									></v-text-field>
																								</v-col>
																							</v-row>
																						</v-card-text>
																					</v-card>
																				</v-col>
																			</v-row>
																		</v-col>
																	</v-row>

																	<v-form :ref="'selectorsSpecifications' + i">
																		<!-- Start Selectors -->
																		<!-- <component :is="selectorComponent" /> -->
																		<!-- Nystrom -->
																		<v-template v-if="selectorComponent === 'selector-cat-1-fact-1'">
																			<v-row>
																				<v-col md="12" cols="12">
																					<v-card outlined>
																						<v-card-text class="d-flex">
																							<vc-autocomplete
																								:key="field.product_id"
																								v-model="field.specifications.model_group"
																								outlined
																								:api="`/v1/lookups/product-model-groups?product_id=${field.product_id}`"
																								label="Model Groups"
																								name="specifications.model_group"
																								item-text="text"
																								item-value="value"
																								hide-details
																								class="me-5"
																								:rules="[$rules.required('Model Groups')]"
																								@change="getSelectorValues(i)"
																							>
																							</vc-autocomplete>

																							<vc-autocomplete
																								:key="field.specifications.model_group"
																								v-model="field.specifications.model"
																								outlined
																								:api="`/v1/lookups/product-models?group_name=${field.specifications.model_group}`"
																								label="Model"
																								name="specifications.model"
																								item-text="text"
																								item-value="value"
																								hide-details
																								:rules="[$rules.required('model')]"
																								@change="getSelectorValues(i)"
																							>
																							</vc-autocomplete>
																						</v-card-text>
																					</v-card>
																				</v-col>
																			</v-row>

																			<v-row>
																				<v-col md="7" cols="12">
																					<v-card outlined>
																						<v-card-text>
																							<div class="d-flex">
																								<v-text-field
																									:key="i + 'part_number'"
																									v-model="field.part_number"
																									outlined
																									name="part_number"
																									label="Areel Part Number"
																									hide-details
																									class="mb-2 me-2"
																									:rules="[
																										$rules.required('part_number'),
																									]"
																									@change="getSelectorValues(i)"
																								></v-text-field>

																								<v-text-field
																									:key="i + 'options'"
																									v-model="field.options"
																									outlined
																									name="options"
																									label="Options"
																									hide-details
																									class="mb-2"
																									style="width: 20%"
																									@change="getSelectorValues(i)"
																								></v-text-field>
																							</div>

																							<vc-textarea
																								:key="i + 'description'"
																								v-model="field.description"
																								outlined
																								label="Description"
																								required
																								hide-details
																								class="mb-2"
																							></vc-textarea>

																							<vc-textarea
																								:key="i + 'comment'"
																								v-model="field.comment"
																								outlined
																								label="Note"
																								hide-details
																								required
																								class="mb-2"
																							></vc-textarea>
																						</v-card-text>
																					</v-card>
																				</v-col>

																				<v-col md="5" cols="12">
																					<v-card class="mb-4">
																						<v-card-title>Specifications</v-card-title>
																						<v-card-text>
																							<v-row>
																								<v-col md="12" cols="12">
																									<v-text-field
																										:key="i + 'uom'"
																										v-model="field.uom"
																										outlined
																										name="uom"
																										label="UOM"
																										hide-details
																									></v-text-field>
																								</v-col>
																								<v-col md="6" cols="12">
																									<v-text-field
																										v-if="field.specifications"
																										:key="i + 'width'"
																										v-model="field.specifications.width"
																										outlined
																										hide-details
																										name="specifications.width"
																										label="R.O Width (mm)"
																										suffix="mm"
																										:rules="[
																											$rules.required('R.O Width'),
																										]"
																										@change="getSelectorValues(i)"
																									></v-text-field>
																								</v-col>
																								<v-col md="6" cols="12">
																									<v-text-field
																										v-if="field.specifications"
																										:key="i + 'height'"
																										v-model="
																											field.specifications.height
																										"
																										outlined
																										hide-details
																										name="specifications.height"
																										label="R.O Height / Length"
																										suffix="mm"
																										:rules="[
																											$rules.required('R.O Height'),
																										]"
																										@change="getSelectorValues(i)"
																									></v-text-field>
																								</v-col>
																								<v-col md="12" cols="12">
																									<vc-select
																										v-if="field.specifications"
																										:key="i + 'horizontal_or_vertical'"
																										v-model="
																											field.specifications
																												.horizontal_or_vertical
																										"
																										name="horizontal_or_vertical"
																										outlined
																										hide-details
																										:items="horizontalVerticalItems"
																										label="Horizontal / Vertical"
																										@change="getSelectorValues(i)"
																									></vc-select>
																								</v-col>
																							</v-row>
																						</v-card-text>
																					</v-card>
																					<v-card>
																						<v-card-title>General</v-card-title>
																						<v-card-text>
																							<v-row>
																								<v-col md="6" cols="12">
																									<field-text-field
																										:key="i + 'quantity'"
																										v-model="field.quantity"
																										outlined
																										type="number"
																										min="0"
																										name="quantity"
																										label="Quantity"
																										:rules="[
																											$rules.required('quantity'),
																										]"
																										hide-details
																										class="mb-2"
																									></field-text-field>
																								</v-col>
																								<v-col md="6" cols="12">
																									<field-text-field
																										:key="i + 'client_tag'"
																										v-model="field.client_tag"
																										hide-details
																										class="mb-2"
																										outlined
																										name="client_tag"
																										label="Client Tags"
																									></field-text-field>
																								</v-col>
																							</v-row>
																						</v-card-text>
																					</v-card>
																				</v-col>
																			</v-row>
																		</v-template>

																		<!-- Form -->
																		<template v-else-if="selectorComponent === 'selector-cat-1-fact-4'">
																			<v-row class="mt-4">
																				<v-col cols="12" md="12" class="mt-2">
																					<v-card outlined>
																						<v-card-title>Specifications</v-card-title>
																						<v-card-text>
																							<v-row class="mt-2">
																								<v-col md="3" cols="12" class="pt-0">
																									<vc-autocomplete
																										:key="i + 'door_material'"
																										v-model="
																											field.specifications
																												.door_material
																										"
																										outlined
																										api="/v1/lookups/materials"
																										label="Door Material"
																										name="specifications.door_material"
																										item-text="text"
																										item-value="value"
																										hide-details
																										class="mb-2"
																										:rules="[
																											$rules.required('Door Materia'),
																										]"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<v-text-field
																										:key="i + 'door_material_thickness'"
																										v-model="
																											field.specifications
																												.door_material_thickness
																										"
																										outlined
																										hide-details
																										class="mb-2"
																										name="specifications.door_material_thickness"
																										label="Door Material Thickness"
																										:rules="[
																											$rules.required(
																												'Material Thicknes'
																											),
																										]"
																										@change="getSelectorValues(i)"
																									></v-text-field>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<v-text-field
																										:key="i + 'rough_opening_width'"
																										v-model="
																											field.specifications
																												.rough_opening_width
																										"
																										outlined
																										hide-details
																										class="mb-2"
																										name="specifications.rough_opening_width"
																										label="Rough Opening Width"
																										:rules="[
																											$rules.required(
																												'Rough Opening Width'
																											),
																										]"
																										@change="getSelectorValues(i)"
																									></v-text-field>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<v-text-field
																										:key="i + 'rough_opening_height'"
																										v-model="
																											field.specifications
																												.rough_opening_height
																										"
																										outlined
																										hide-details
																										class="mb-2"
																										name="specifications.rough_opening_height"
																										label="Rough Opening Height"
																										:rules="[
																											$rules.required(
																												'Rough Opening Height'
																											),
																										]"
																										@change="getSelectorValues(i)"
																									></v-text-field>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<vc-autocomplete
																										:key="i + 'door_material_type'"
																										v-model="
																											field.specifications
																												.door_material_type
																										"
																										outlined
																										hide-details
																										class="mb-2"
																										api="/v1/lookups/material-types"
																										label="Door S/D"
																										name="specifications.door_material_type"
																										item-text="text"
																										item-value="value"
																										:rules="[
																											$rules.required('Door S/D'),
																										]"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<vc-autocomplete
																										:key="i + 'door_insulation_value'"
																										v-model="
																											field.specifications
																												.door_insulation_value
																										"
																										outlined
																										hide-details
																										class="mb-2"
																										api="/v1/lookups/insulation-values"
																										label="Insulation (mm)"
																										name="specifications.door_insulation_value"
																										item-text="text"
																										item-value="value"
																										:rules="[
																											$rules.required(
																												'Insulation (mm)'
																											),
																										]"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<vc-autocomplete
																										:key="i + 'door_insulation_type'"
																										v-model="
																											field.specifications
																												.door_insulation_type
																										"
																										outlined
																										hide-details
																										class="mb-2"
																										api="/v1/lookups/insulation-types"
																										label="Insulation Type"
																										name="specifications.door_insulation_type"
																										item-text="text"
																										item-value="value"
																										:rules="[
																											$rules.required(
																												'Insulation Type'
																											),
																										]"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<vc-autocomplete
																										:key="i + 'door_surface'"
																										v-model="
																											field.specifications
																												.door_surface
																										"
																										outlined
																										hide-details
																										class="mb-2"
																										api="/v1/lookups/surfaces"
																										label="Surface"
																										name="specifications.door_surface"
																										item-text="text"
																										item-value="value"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<vc-autocomplete
																										:key="i + 'curb_material'"
																										v-model="
																											field.specifications
																												.curb_material
																										"
																										outlined
																										hide-details
																										class="mb-2"
																										api="/v1/lookups/materials"
																										label="Curb Material"
																										name="specifications.curb_material"
																										item-text="text"
																										item-value="value"
																										:rules="[
																											$rules.required(
																												'Curb Material'
																											),
																										]"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<v-text-field
																										:key="i + 'curb_material_thickness'"
																										v-model="
																											field.specifications
																												.curb_material_thickness
																										"
																										outlined
																										hide-details
																										class="mb-2"
																										name="specifications.curb_material_thickness"
																										label="Material Thickness"
																										:rules="[
																											$rules.required(
																												'Material Thickness'
																											),
																										]"
																										@change="getSelectorValues(i)"
																									></v-text-field>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<vc-autocomplete
																										:key="i + 'curb_material_type'"
																										v-model="
																											field.specifications
																												.curb_material_type
																										"
																										outlined
																										hide-details
																										class="mb-2"
																										api="/v1/lookups/material-types"
																										label="Curb S/D"
																										name="specifications.curb_material_type"
																										item-text="text"
																										item-value="value"
																										:rules="[
																											$rules.required('Curb S/D'),
																										]"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<vc-autocomplete
																										:key="i + 'curb_insulation_value'"
																										v-model="
																											field.specifications
																												.curb_insulation_value
																										"
																										outlined
																										hide-details
																										class="mb-2"
																										api="/v1/lookups/insulation-values"
																										label="Curb Insulation (mm)"
																										name="specifications.curb_insulation_value"
																										item-text="text"
																										item-value="value"
																										:rules="[
																											$rules.required(
																												'Curb Insulation (mm)'
																											),
																										]"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<vc-autocomplete
																										:key="i + 'curb_insulation_type'"
																										v-model="
																											field.specifications
																												.curb_insulation_type
																										"
																										outlined
																										hide-details
																										class="mb-2"
																										api="/v1/lookups/insulation-types"
																										label="Curb Insulation Type"
																										name="specifications.curb_insulation_type"
																										item-text="text"
																										item-value="value"
																										:rules="[
																											$rules.required(
																												'Curb Insulation Type'
																											),
																										]"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<v-text-field
																										:key="i + 'curb_height'"
																										v-model="
																											field.specifications.curb_height
																										"
																										outlined
																										hide-details
																										class="mb-2"
																										name="specifications.curb_height"
																										label="Curb Height"
																										:rules="[
																											$rules.required('Curb Height'),
																										]"
																										@change="getSelectorValues(i)"
																									></v-text-field>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<v-text-field
																										:key="i + 'flange_width'"
																										v-model="
																											field.specifications
																												.flange_width
																										"
																										outlined
																										hide-details
																										class="mb-2"
																										name="specifications.flange_width"
																										label="Flange Width"
																										:rules="[
																											$rules.required('Flange Width'),
																										]"
																										@change="getSelectorValues(i)"
																									></v-text-field>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<v-text-field
																										:key="i + 'ral_number '"
																										v-model="
																											field.specifications.ral_number
																										"
																										:rules="[$rules.required('RAL')]"
																										outlined
																										hide-details
																										class="mb-2"
																										name="specifications.ral_number "
																										label="RAL"
																										@change="getSelectorValues(i)"
																									></v-text-field>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<vc-autocomplete
																										:key="i + 'gas_strut'"
																										v-model="
																											field.specifications.gas_strut
																										"
																										outlined
																										hide-details
																										class="mb-2"
																										api="/v1/lookups/gas-struts"
																										label="Gas Strut"
																										name="specifications.gas_strut"
																										item-text="text"
																										item-value="value"
																										:rules="[
																											$rules.required('Gas Strut'),
																										]"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<vc-autocomplete
																										:key="i + 'hardware'"
																										v-model="
																											field.specifications.hardware
																										"
																										outlined
																										hide-details
																										class="mb-2"
																										api="/v1/lookups/hardware"
																										label="HDW"
																										name="specifications.hardware"
																										item-text="text"
																										item-value="value"
																										:rules="[$rules.required('HDW')]"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<vc-autocomplete
																										:key="i + 'finish'"
																										v-model="
																											field.specifications.finish
																										"
																										outlined
																										hide-details
																										class="mb-2"
																										api="/v1/lookups/finishes"
																										label="Finish"
																										name="specifications.finish"
																										item-text="text"
																										item-value="value"
																										:rules="[$rules.required('Finish')]"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>
																							</v-row>
																						</v-card-text>
																					</v-card>
																				</v-col>
																			</v-row>

																			<v-row class="mt-6">
																				<v-col :md="step === 2 ? 6 : 12" cols="12" class="pt-0">
																					<v-card outlined>
																						<v-card-title>Areel</v-card-title>
																						<v-card-text>
																							<v-text-field
																								:key="i + 'part_number'"
																								v-model="field.part_number"
																								hide-details
																								class="mb-2"
																								outlined
																								name="specifications.part_number"
																								label="Areel Part Number"
																								:rules="[$rules.required('part_number')]"
																								@change="getSelectorValues(i)"
																							></v-text-field>

																							<vc-textarea
																								:key="i + 'description'"
																								v-model="field.description"
																								outlined
																								hide-details
																								class="mb-2"
																								name="description"
																								label="Areel Description"
																								required
																							></vc-textarea>
																						</v-card-text>
																					</v-card>
																				</v-col>

																				<v-col v-if="step === 2" md="6" cols="12" class="pt-0">
																					<v-card outlined>
																						<v-card-title>Form</v-card-title>
																						<v-card-text>
																							<field-text-field
																								:key="i + 'factory_part_number'"
																								v-model="field.factory_part_number"
																								outlined
																								hide-details
																								class="mb-2"
																								name="part_number"
																								label="Form Part Number"
																								:rules="[
																									$rules.required('factory_part_number'),
																								]"
																							></field-text-field>

																							<vc-textarea
																								:key="i + 'factory_description'"
																								v-model="field.factory_description"
																								outlined
																								hide-details
																								class="mb-2"
																								label="Form Description"
																								required
																							></vc-textarea>
																						</v-card-text>
																					</v-card>
																				</v-col>
																			</v-row>
																			<v-row class="mt-4">
																				<v-col>
																					<vc-textarea
																						:key="i + 'comment'"
																						v-model="field.comment"
																						outlined
																						label="Note"
																						hide-details
																						class="mb-2"
																						required
																					></vc-textarea>
																				</v-col>
																			</v-row>
																		</template>

																		<!-- Bach -->
																		<template v-else-if="selectorComponent === 'selector-cat-5-fact-9'">
																			<v-row :key="selectorComponent + i" class="mt-4">
																				<v-col cols="12" md="12" class="mt-2">
																					<v-card outlined>
																						<v-card-title>Select Model</v-card-title>
																						<v-card-text>
																							<v-row class="mt-1">
																								<v-col md="4" cols="12" class="pt-0">
																									<vc-autocomplete
																										:key="i + 'horizontal_or_vertical'"
																										v-model="
																											field.specifications
																												.horizontal_or_vertical
																										"
																										outlined
																										:items="horizontalVerticalItems"
																										label="Horizontal / Vertical"
																										name="specifications.horizontal_or_vertical"
																										item-text="text"
																										item-value="value"
																										:rules="[
																											$rules.required(
																												'Horizontal / Vertical'
																											),
																										]"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>

																								<v-col md="4" cols="12" class="pt-0">
																									<vc-autocomplete
																										:key="i + 'fixed_or_movable'"
																										v-model="
																											field.specifications
																												.fixed_or_movable
																										"
																										outlined
																										:items="fixedOrMovable"
																										label="Fixed / Movable"
																										name="specifications.fixed_or_movable"
																										item-text="text"
																										item-value="value"
																										:rules="[
																											$rules.required(
																												'Fixed / Movable'
																											),
																										]"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>

																								<v-col md="4" cols="12" class="pt-0">
																									<vc-autocomplete
																										:key="i + 'classification'"
																										v-model="
																											field.specifications
																												.classification
																										"
																										outlined
																										api="/v1/lookups/classifications"
																										label="Classification"
																										name="specifications.classification"
																										item-text="text"
																										item-value="value"
																										:rules="[
																											$rules.required(
																												'Classification'
																											),
																										]"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>

																								<v-col md="4" cols="12" class="pt-0">
																									<v-text-field
																										:key="i + 'width'"
																										v-model="field.specifications.width"
																										outlined
																										name="specifications.width"
																										label="Width (m)"
																										:rules="[$rules.required('Width')]"
																										@change="getSelectorValues(i)"
																									></v-text-field>
																								</v-col>

																								<v-col md="4" cols="12" class="pt-0">
																									<v-text-field
																										:key="i + 'height'"
																										v-model="
																											field.specifications.height
																										"
																										outlined
																										name="specifications.height"
																										label="Height (m)"
																										:rules="[$rules.required('Height')]"
																										@change="getSelectorValues(i)"
																									></v-text-field>
																								</v-col>
																							</v-row>
																						</v-card-text>
																					</v-card>

																					<v-alert
																						v-if="!field.result && field.alertSelector"
																						:key="field.result"
																						class="mt-4"
																						type="error"
																					>
																						There is no data for this model
																					</v-alert>

																					<v-card
																						:key="'specifications' + i"
																						outlined
																						class="mt-3"
																					>
																						<v-card-title>Specifications</v-card-title>
																						<v-card-text>
																							<v-row>
																								<v-col md="3">
																									<v-text-field
																										:key="i + 'tests_standards'"
																										v-model="
																											field.specifications
																												.tests_standards
																										"
																										outlined
																										name="specifications.tests_standards"
																										label="Tests Standards"
																										class="mb-2"
																									></v-text-field>

																									<v-text-field
																										:key="i + 'classification'"
																										v-model="
																											field.specifications
																												.classification
																										"
																										outlined
																										name="specifications.classification"
																										label="Classification"
																									></v-text-field>
																								</v-col>
																								<v-col md="6">
																									<vc-textarea
																										:key="i + 'fabric'"
																										v-model="
																											field.specifications.fabric
																										"
																										outlined
																										:counter="1000"
																										name="specifications.fabric"
																										label="Fabric"
																									></vc-textarea>
																								</v-col>
																								<v-col md="3">
																									<vc-textarea
																										:key="i + 'irrigation_system'"
																										v-model="
																											field.specifications
																												.irrigation_system
																										"
																										outlined
																										:counter="1000"
																										name="specifications.irrigation_system"
																										label="Irrigation System"
																									></vc-textarea>
																								</v-col>
																							</v-row>

																							<v-row>
																								<v-col md="6">
																									<v-text-field
																										:key="i + 'head_box_material'"
																										v-model="
																											field.specifications
																												.head_box_material
																										"
																										outlined
																										name="specifications.head_box_material"
																										label="Head Box / Material"
																										class="mb-2"
																									>
																									</v-text-field>

																									<v-text-field
																										:key="i + 'side_guides_material'"
																										v-model="
																											field.specifications
																												.side_guides_material
																										"
																										outlined
																										name="specifications.side_guides_material"
																										label="Side Guides / Material"
																									></v-text-field>
																								</v-col>

																								<v-col md="6">
																									<vc-textarea
																										:key="i + 'bottom_bar'"
																										v-model="
																											field.specifications.bottom_bar
																										"
																										outlined
																										name="specifications.bottom_bar"
																										label="Bottom Bar"
																									></vc-textarea>
																								</v-col>
																							</v-row>

																							<v-row>
																								<v-col md="3">
																									<vc-textarea
																										:key="i + 'head_box_size'"
																										v-model="
																											field.specifications
																												.head_box_size
																										"
																										outlined
																										name="specifications.head_box_size"
																										label="Head Box / Size"
																									></vc-textarea>
																								</v-col>

																								<v-col md="3">
																									<vc-textarea
																										:key="i + 'side_guides_material'"
																										v-model="
																											field.specifications
																												.side_guides_material
																										"
																										outlined
																										:counter="1000"
																										name="specifications.side_guides_material"
																										label="Side Guides / Material"
																									></vc-textarea>
																								</v-col>

																								<v-col md="6">
																									<vc-textarea
																										:key="i + 'crm_motor_box'"
																										v-model="
																											field.specifications
																												.crm_motor_box
																										"
																										outlined
																										:counter="1000"
																										name="specifications.crm_motor_box"
																										label="CRM Motor Box"
																									></vc-textarea>
																								</v-col>
																							</v-row>

																							<v-row>
																								<v-col md="3">
																									<vc-textarea
																										:key="i + 'head_box_installation'"
																										v-model="
																											field.specifications
																												.head_box_installation
																										"
																										outlined
																										name="specifications.head_box_installation"
																										label="Head Box / Installation"
																									></vc-textarea>
																								</v-col>

																								<v-col md="3">
																									<vc-textarea
																										:key="
																											i + 'side_guides_installation'
																										"
																										v-model="
																											field.specifications
																												.side_guides_installation
																										"
																										outlined
																										:counter="1000"
																										name="specifications.side_guides_installation"
																										label="Side Guides / Installation"
																									></vc-textarea>
																								</v-col>

																								<v-col md="6">
																									<vc-textarea
																										:key="i + 'cbm_control_panel'"
																										v-model="
																											field.specifications
																												.cbm_control_panel
																										"
																										outlined
																										:counter="1000"
																										name="specifications.cbm_control_panel"
																										label="CBM Control Panel"
																									></vc-textarea>
																								</v-col>
																							</v-row>

																							<v-row>
																								<v-col md="6">
																									<vc-textarea
																										:key="i + 'roller'"
																										v-model="
																											field.specifications.roller
																										"
																										outlined
																										:counter="1000"
																										name="specifications.roller"
																										label="Roller"
																									></vc-textarea>
																								</v-col>

																								<v-col md="6">
																									<vc-textarea
																										:key="i + 'electric_motor'"
																										v-model="
																											field.specifications
																												.electric_motor
																										"
																										outlined
																										:counter="1000"
																										name="specifications.electric_motor"
																										label="Electric Motor"
																									></vc-textarea>
																								</v-col>
																							</v-row>
																						</v-card-text>
																					</v-card>
																				</v-col>
																			</v-row>

																			<v-row class="mt-6">
																				<v-col :md="step === 2 ? 6 : 12" cols="12" class="pt-0">
																					<v-card outlined>
																						<v-card-title>Areel</v-card-title>
																						<v-card-text>
																							<v-text-field
																								:key="i + 'part_number'"
																								v-model="field.part_number"
																								outlined
																								name="part_number"
																								label="Areel Part Number"
																								:rules="[$rules.required('part_number')]"
																								@change="getSelectorValues(i)"
																							></v-text-field>

																							<vc-textarea
																								:key="i + 'description'"
																								v-model="field.description"
																								outlined
																								label="Areel Description"
																								required
																							></vc-textarea>
																						</v-card-text>
																					</v-card>
																				</v-col>

																				<v-col v-if="step === 2" md="6" cols="12" class="pt-0">
																					<v-card outlined>
																						<v-card-title>Bach</v-card-title>
																						<v-card-text>
																							<v-text-field
																								:key="i + 'factory_part_number'"
																								v-model="field.factory_part_number"
																								outlined
																								name="factory_part_number"
																								label="Form Part Number"
																								:rules="[
																									$rules.required('factory_part_number'),
																								]"
																							></v-text-field>

																							<vc-textarea
																								:key="i + 'factory_description'"
																								v-model="field.factory_description"
																								outlined
																								name="factory_description"
																								label="Form Description"
																								required
																							></vc-textarea>
																						</v-card-text>
																					</v-card>
																				</v-col>
																			</v-row>
																			<v-row class="mt-4">
																				<v-col>
																					<vc-textarea
																						:key="i + 'comment'"
																						v-model="field.comment"
																						outlined
																						label="Note"
																						name="comment"
																						required
																					></vc-textarea>
																				</v-col>
																			</v-row>
																		</template>

																		<!-- Rollsis  -->
																		<template v-else-if="selectorComponent === 'selector-cat-6-fact-3'">
																			<v-row :key="selectorComponent + i" class="mt-4">
																				<v-col cols="12" md="12" class="mt-2">
																					<v-card outlined>
																						<v-card-title>Select Model</v-card-title>
																						<v-card-text>
																							<v-row class="mt-1">
																								<v-col md="3" cols="12" class="pt-0">
																									<vc-autocomplete
																										:key="i + 'fire_rating'"
																										v-model="
																											field.specifications.fire_rating
																										"
																										outlined
																										label="Fire Rating"
																										name="specifications.fire_rating"
																										:items="fireFatingItems"
																										item-text="text"
																										item-value="value"
																										hide-details
																										class="mb-2"
																										:rules="[
																											$rules.required('Fire Rating'),
																										]"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<vc-autocomplete
																										:key="
																											field.specifications.fire_rating
																										"
																										v-model="
																											field.specifications
																												.materials_type
																										"
																										outlined
																										:api="`/v1/lookups/materials-types?fire-rating=${field.specifications.fire_rating}`"
																										label="Materials type"
																										name="specifications.materials_type"
																										item-text="text"
																										item-value="value"
																										hide-details
																										class="mb-2"
																										:rules="[
																											$rules.required(
																												'Materials type'
																											),
																										]"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<vc-autocomplete
																										:key="
																											field.specifications
																												.materials_type
																										"
																										v-model="field.specifications.skin"
																										outlined
																										:api="`/v1/lookups/skins?material-type=${field.specifications.materials_type}&fire-rating=${field.specifications.fire_rating}`"
																										label="Skin"
																										name="specifications.skin"
																										item-text="text"
																										item-value="value"
																										hide-details
																										class="mb-2"
																										:rules="[$rules.required('skin')]"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<vc-autocomplete
																										:key="field.specifications.skin"
																										v-model="
																											field.specifications
																												.formed_or_extruded
																										"
																										outlined
																										:api="`/v1/lookups/formed-or-extruded?skin=${field.specifications.skin}&?material-type=${field.specifications.materials_type}&fire-rating=${field.specifications.fire_rating}`"
																										label="Formed or Extruded"
																										name="specifications.formed_or_extruded"
																										item-text="text"
																										item-value="value"
																										hide-details
																										class="mb-2"
																										:rules="[
																											$rules.required(
																												'formed_or_extruded'
																											),
																										]"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<vc-autocomplete
																										:key="
																											field.specifications
																												.formed_or_extruded
																										"
																										v-model="
																											field.specifications.perforated
																										"
																										outlined
																										:api="`/v1/lookups/perforated?formed-or-extruded=${field.specifications.formed_or_extruded}&skin=${field.specifications.skin}&?material-type=${field.specifications.materials_type}&fire-rating=${field.specifications.fire_rating}`"
																										label="Perforated"
																										name="specifications.perforated"
																										item-text="text"
																										item-value="value"
																										hide-details
																										class="mb-2"
																										:rules="[
																											$rules.required('perforated'),
																										]"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>

																								<v-col md="3" cols="12" class="pt-0">
																									<vc-autocomplete
																										:key="
																											field.specifications.perforated
																										"
																										v-model="
																											field.specifications.insulation
																										"
																										outlined
																										:api="`/v1/lookups/insulations?perforated=${field.specifications.perforated}&formed-or-extruded=${field.specifications.formed_or_extruded}&skin=${field.specifications.skin}&?material-type=${field.specifications.materials_type}&fire-rating=${field.specifications.fire_rating}`"
																										label="Insulation"
																										name="specifications.insulation"
																										item-text="text"
																										item-value="value"
																										hide-details
																										class="mb-2"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>

																								<v-col md="2" cols="12" class="pt-0">
																									<vc-autocomplete
																										:key="
																											field.specifications.insulation
																												? field.specifications
																														.insulation
																												: field.specifications
																														.perforated
																										"
																										v-model="
																											field.specifications
																												.panel_height
																										"
																										outlined
																										:api="`/v1/lookups/panel-heights?insulation=${
																											field.specifications.insulation
																												? field.specifications
																														.insulation
																												: ``
																										}&perforated=${
																											field.specifications.perforated
																										}&formed-or-extruded=${
																											field.specifications
																												.formed_or_extruded
																										}&skin=${
																											field.specifications.skin
																										}&?material-type=${
																											field.specifications
																												.materials_type
																										}&fire-rating=${
																											field.specifications.fire_rating
																										}`"
																										label="Panel Height"
																										name="specifications.panel_height"
																										item-text="text"
																										item-value="value"
																										hide-details
																										class="mb-2"
																										:rules="[
																											$rules.required('panel_height'),
																										]"
																										@change="getSelectorValues(i)"
																									>
																									</vc-autocomplete>
																								</v-col>

																								<v-col md="2" cols="12" class="pt-0">
																									<v-text-field
																										:key="i + 'width'"
																										v-model="field.specifications.width"
																										outlined
																										hide-details
																										class="mb-2"
																										name="specifications.width"
																										label="Width"
																										suffix="mm"
																										@change="getSelectorValues(i)"
																									></v-text-field>
																								</v-col>

																								<v-col md="2" cols="12" class="pt-0">
																									<v-text-field
																										:key="i + 'height'"
																										v-model="
																											field.specifications.height
																										"
																										outlined
																										hide-details
																										class="mb-2"
																										name="specifications.height"
																										label="Height"
																										suffix="mm"
																										@change="getSelectorValues(i)"
																									></v-text-field>
																								</v-col>
																							</v-row>
																						</v-card-text>
																					</v-card>

																					<v-alert
																						v-if="!field.result && field.alertSelector"
																						:key="field.result"
																						class="mt-4"
																						type="error"
																					>
																						There is no data for this model
																					</v-alert>

																					<v-card :key="'options' + i" outlined class="mt-3">
																						<v-card-title>Options</v-card-title>
																						<v-card-text
																							v-if="
																								field.specifications &&
																								field.specifications.options
																							"
																						>
																							<v-row>
																								<v-col md="6">
																									<v-select
																										:key="i + 'hs_motor'"
																										v-model="
																											field.specifications.options
																												.hs_motor
																										"
																										outlined
																										name="specifications.options.hs_motor"
																										label="H.S Motor"
																										:items="[
																											{ text: 'Yes', value: 'yes' },
																											{ text: 'No', value: 'no' },
																										]"
																										hide-details
																										class="mb-2"
																									></v-select>
																								</v-col>
																								<v-col md="6">
																									<v-text-field
																										:key="i + 'motor_type'"
																										v-model="
																											field.specifications.options
																												.motor_type
																										"
																										outlined
																										:disabled="
																											field.specifications.options
																												.hs_motor === 'no'
																										"
																										hide-details
																										class="mb-2"
																										name="specifications.options.motor_type"
																										label="Motor Type"
																									></v-text-field>
																								</v-col>
																							</v-row>

																							<v-row>
																								<v-col md="3">
																									<v-text-field
																										:key="i + 'ral'"
																										v-model="
																											field.specifications.options.ral
																										"
																										outlined
																										type="number"
																										hide-details
																										class="mb-2"
																										name="specifications.options.ral"
																										label="Ral"
																									></v-text-field>
																								</v-col>

																								<v-col md="3">
																									<v-select
																										:key="i + 'pneumatic_security'"
																										v-model="
																											field.specifications.options
																												.pneumatic_security
																										"
																										outlined
																										hide-details
																										:items="[
																											{ text: 'Yes', value: 'yes' },
																											{ text: 'No', value: 'no' },
																										]"
																										class="mb-2"
																										name="specifications.options.pneumatic_security"
																										label="Pneumatic Security"
																									></v-select>
																								</v-col>

																								<v-col md="3">
																									<v-select
																										:key="i + 'photocell_security'"
																										v-model="
																											field.specifications.options
																												.photocell_security
																										"
																										outlined
																										hide-details
																										:items="[
																											{ text: 'Yes', value: 'yes' },
																											{ text: 'No', value: 'no' },
																										]"
																										class="mb-2"
																										name="specifications.options.photocell_security"
																										label="Photocell Security"
																									></v-select>
																								</v-col>

																								<v-col md="3">
																									<v-select
																										:key="i + 'vision_panel'"
																										v-model="
																											field.specifications.options
																												.vision_panel
																										"
																										outlined
																										:items="[
																											{ text: 'Yes', value: 'yes' },
																											{ text: 'No', value: 'no' },
																										]"
																										hide-details
																										class="mb-2"
																										name="specifications.options.vision_panel"
																										label="Vision Panel"
																									></v-select>
																								</v-col>
																							</v-row>

																							<v-divider class="my-4 d-block" />

																							<h3 class="mb-5">
																								For Use Width Section Doors
																							</h3>

																							<v-row>
																								<v-col md="4">
																									<v-text-field
																										:key="i + 'pedestrian_doors'"
																										v-model="
																											field.specifications.options
																												.pedestrian_doors
																										"
																										outlined
																										hide-details
																										class="mb-2"
																										name="specifications.options.pedestrian_doors"
																										label="Pedestrian Doors"
																									></v-text-field>
																								</v-col>
																								<v-col md="4">
																									<v-text-field
																										:key="i + 'pedestrian_doors_size'"
																										v-model="
																											field.specifications.options
																												.pedestrian_doors_size
																										"
																										outlined
																										hide-details
																										class="mb-2"
																										name="specifications.options.pedestrian_doors_size"
																										label="Pedestrian Doors Size"
																										suffix="CM"
																									></v-text-field>
																								</v-col>
																								<v-col md="4">
																									<v-text-field
																										:key="i + 'inner_ral'"
																										v-model="
																											field.specifications.options
																												.inner_ral
																										"
																										outlined
																										hide-details
																										class="mb-2"
																										name="specifications.options.inner_ral"
																										label="Inner Ral"
																									></v-text-field>
																								</v-col>
																							</v-row>
																						</v-card-text>
																					</v-card>
																				</v-col>
																			</v-row>

																			<v-row class="mt-6">
																				<v-col :md="step === 2 ? 6 : 12" cols="12" class="pt-0">
																					<v-card outlined>
																						<v-card-title>Areel</v-card-title>
																						<v-card-text>
																							<v-text-field
																								:key="i + 'part_number'"
																								v-model="field.part_number"
																								outlined
																								name="part_number"
																								label="Areel Part Number"
																								hide-details
																								class="mb-2"
																								:rules="[$rules.required('part_number')]"
																								@change="getSelectorValues(i)"
																							></v-text-field>

																							<vc-textarea
																								:key="i + 'description'"
																								v-model="field.description"
																								outlined
																								label="Areel Description"
																								required
																								hide-details
																								class="mb-2"
																							></vc-textarea>
																						</v-card-text>
																					</v-card>
																				</v-col>

																				<v-col v-if="step === 2" md="6" cols="12" class="pt-0">
																					<v-card outlined>
																						<v-card-title>Rollsis</v-card-title>
																						<v-card-text>
																							<v-text-field
																								:key="i + 'factory_part_number'"
																								v-model="field.factory_part_number"
																								outlined
																								hide-details
																								class="mb-2"
																								name="factory_part_number"
																								label="Form Part Number"
																								:rules="[
																									$rules.required('factory_part_number'),
																								]"
																							></v-text-field>

																							<vc-textarea
																								:key="i + 'factory_description'"
																								v-model="field.factory_description"
																								outlined
																								hide-details
																								class="mb-2"
																								label="Form Description"
																								required
																								name="factory_description"
																							></vc-textarea>
																						</v-card-text>
																					</v-card>
																				</v-col>
																			</v-row>

																			<v-row class="mt-4">
																				<v-col>
																					<vc-textarea
																						:key="i + 'comment'"
																						v-model="field.comment"
																						outlined
																						name="comment"
																						label="Note"
																						hide-details
																						class="mb-2"
																						required
																					></vc-textarea>
																				</v-col>
																			</v-row>
																		</template>

																		<!-- Stair Nosing  -->
																		<template v-else-if="selectorComponent === 'selector-cat-4-fact-1'">
																			<v-card outlined class="mt-6">
																				<v-card-text>
																					<v-row>
																						<v-col md="3" cols="12">
																							<vc-autocomplete
																								:key="i + 'sub_category_id'"
																								v-model="field.specifications.material"
																								outlined
																								api="/v1/lookups/stair-nosing/materials"
																								label="Material"
																								name="material"
																								item-text="text"
																								item-value="value"
																								hide-details
																								:rules="[$rules.required('Material')]"
																								@change="getSelectorValues(i)"
																							>
																							</vc-autocomplete>
																						</v-col>
																						<v-col md="3" cols="12">
																							<vc-autocomplete
																								:key="field.specifications.material"
																								v-model="field.specifications.material_type"
																								outlined
																								:api="`/v1/lookups/stair-nosing/material-types?material=${field.specifications.material}`"
																								label="Material Type"
																								name="material_type"
																								item-text="text"
																								item-value="value"
																								hide-details
																								:rules="[$rules.required('material type')]"
																								@change="getSelectorValues(i)"
																							>
																							</vc-autocomplete>
																						</v-col>
																						<v-col md="3" cols="12">
																							<vc-autocomplete
																								:key="field.specifications.material_type"
																								v-model="field.specifications.nosing"
																								outlined
																								:api="`/v1/lookups/stair-nosing/nosings?material=${field.specifications.material}&material_type=${field.specifications.material_type}`"
																								label="Nosing"
																								name="nosing"
																								item-text="text"
																								item-value="value"
																								hide-details
																								:rules="[$rules.required('nosing')]"
																								@change="getSelectorValues(i)"
																							>
																							</vc-autocomplete>
																						</v-col>
																						<v-col md="3" cols="12">
																							<vc-autocomplete
																								:key="field.specifications.nosing"
																								v-model="field.specifications.install"
																								outlined
																								:api="`/v1/lookups/stair-nosing/installs?material=${field.specifications.material}&material_type=${field.specifications.material_type}&nosing=${field.specifications.nosing}`"
																								label="Install"
																								name="install"
																								item-text="text"
																								item-value="value"
																								hide-details
																								:rules="[$rules.required('install')]"
																								@change="getSelectorValues(i)"
																							>
																							</vc-autocomplete>
																						</v-col>

																						<v-col md="3" cols="12">
																							<vc-autocomplete
																								:key="field.specifications.install"
																								v-model="field.specifications.width_in_inch"
																								outlined
																								:api="`/v1/lookups/stair-nosing/widths?material=${field.specifications.material}&material_type=${field.specifications.material_type}&nosing=${field.specifications.nosing}&install=${field.specifications.install}`"
																								label="Widths in (inch)"
																								name="width_in_inch"
																								item-text="text"
																								item-value="value"
																								hide-details
																								:rules="[$rules.required('width_in_inch')]"
																								@change="getSelectorValues(i)"
																							>
																							</vc-autocomplete>
																						</v-col>
																						<v-col md="3" cols="12">
																							<!-- <vc-autocomplete
																								:key="field.specifications.width_in_inch"
																								v-model="field.specifications.width_in_mm"
																								outlined
																								:api="`/v1/lookups/stair-nosing/widths?material=${field.specifications.material}&material_type=${field.specifications.material_type}&nosing=${field.specifications.nosing}&install=${field.specifications.install}&width_in_inch=${field.specifications.width_in_inch}`"
																								label="Width in (mm)"
																								name="width_in_mm"
																								item-text="text"
																								item-value="value"
																								hide-details
																								:rules="[$rules.required('width_in_mm')]"
																								@change="getSelectorValues(i)"
																							>
																							</vc-autocomplete> -->

																							<v-text-field
																								:key="field.specifications.width_in_inch"
																								v-model="field.specifications.width_in_mm"
																								outlined
																								label="Width in (mm)"
																								name="width_in_mm"
																								hide-details
																								class="mb-2"
																								:rules="[$rules.required('width_in_mm')]"
																								@change="getSelectorValues(i)"
																							></v-text-field>
																						</v-col>

																						<v-col md="3" cols="12">
																							<vc-autocomplete
																								:key="field.specifications.width_in_inch"
																								v-model="field.specifications.tread"
																								outlined
																								:api="`/v1/lookups/stair-nosing/treads?material=${field.specifications.material}&material_type=${field.specifications.material_type}&nosing=${field.specifications.nosing}&install=${field.specifications.install}&width_in_mm=${field.specifications.width_in_mm}`"
																								label="Tread"
																								name="tread"
																								item-text="text"
																								item-value="value"
																								hide-details
																								:rules="[$rules.required('tread')]"
																								@change="getSelectorValues(i)"
																							>
																							</vc-autocomplete>
																						</v-col>
																						<v-col md="3" cols="12">
																							<vc-autocomplete
																								:key="field.specifications.tread"
																								v-model="field.specifications.colors"
																								outlined
																								:api="`/v1/lookups/stair-nosing/colors?tread=${field.specifications.tread}`"
																								label="Colors"
																								name="colors"
																								item-text="text"
																								item-value="value"
																								hide-details
																								:rules="[$rules.required('colors')]"
																								@change="getSelectorValues(i)"
																							>
																							</vc-autocomplete>
																						</v-col>
																						<v-col md="12" cols="12">
																							<vc-autocomplete
																								:key="field.specifications.colors"
																								v-model="field.specifications.model"
																								outlined
																								:api="`/v1/lookups/stair-nosing/models`"
																								label="Model"
																								name="model"
																								item-text="text"
																								item-value="value"
																								hide-details
																								:rules="[$rules.required('model')]"
																								@change="getSelectorValues(i)"
																							>
																							</vc-autocomplete>
																						</v-col>

																						<v-col md="3" cols="12">
																							<div>
																								<v-checkbox
																									v-model="field.specifications.clear"
																									class="mt-0 pt-0 mb-3"
																									hide-details
																									label="Clear"
																									@change="getSelectorValues(i)"
																								></v-checkbox>
																							</div>
																						</v-col>
																						<v-col md="3" cols="12">
																							<v-checkbox
																								v-model="field.specifications.mitered_cuts"
																								class="mt-0 pt-0 mb-3"
																								hide-details
																								label="Mitered Cuts"
																								@change="getSelectorValues(i)"
																							></v-checkbox>
																							<v-img
																								width="150px"
																								src="/images/mitered_cuts.png"
																							/>
																						</v-col>
																						<v-col md="3" cols="12">
																							<v-checkbox
																								v-model="
																									field.specifications.painted_undersides
																								"
																								class="mt-0 pt-0 mb-3"
																								hide-details
																								label="Painted Undersides"
																								@change="getSelectorValues(i)"
																							></v-checkbox>
																							<v-img
																								width="150px"
																								src="/images/painted_undersides.png"
																							/>
																						</v-col>
																						<v-col md="3" cols="12">
																							<v-checkbox
																								v-model="field.specifications.taped_tops"
																								class="mt-0 pt-0 mb-3"
																								hide-details
																								label="Taped Tops"
																								@change="getSelectorValues(i)"
																							></v-checkbox>
																							<v-img
																								width="150px"
																								src="/images/taped_tops.png"
																							/>
																						</v-col>
																					</v-row>
																				</v-card-text>
																			</v-card>

																			<v-row class="mt-6">
																				<v-col :md="step === 2 ? 6 : 12" cols="12" class="pt-0">
																					<v-card outlined>
																						<v-card-title>Areel</v-card-title>
																						<v-card-text>
																							<v-text-field
																								:key="i + 'part_number'"
																								v-model="field.part_number"
																								outlined
																								name="part_number"
																								label="Areel Part Number"
																								hide-details
																								class="mb-2"
																							></v-text-field>

																							<vc-textarea
																								:key="i + 'description'"
																								v-model="field.description"
																								name="description"
																								outlined
																								label="Areel Description"
																								required
																							></vc-textarea>
																						</v-card-text>
																					</v-card>
																				</v-col>

																				<v-col v-if="step === 2" md="6" cols="12" class="pt-0">
																					<v-card outlined>
																						<v-card-title>Factory</v-card-title>
																						<v-card-text>
																							<v-text-field
																								:key="i + 'factory_part_number'"
																								v-model="field.factory_part_number"
																								outlined
																								hide-details
																								class="mb-2"
																								name="factory_part_number"
																								label="Form Part Number"
																							></v-text-field>

																							<vc-textarea
																								:key="i + 'factory_description'"
																								v-model="field.factory_description"
																								outlined
																								name="factory_description"
																								label="Form Description"
																								hide-details
																								class="mb-2"
																								required
																							></vc-textarea>
																						</v-card-text>
																					</v-card>
																				</v-col>
																			</v-row>
																		</template>

																		<!-- Else -->
																		<template v-else>
																			<v-row class="mt-6">
																				<v-col :md="step === 2 ? 6 : 12" cols="12" class="pt-0">
																					<v-card outlined>
																						<v-card-title>Areel</v-card-title>
																						<v-card-text>
																							<v-text-field
																								:key="i + 'part_number'"
																								v-model="field.part_number"
																								outlined
																								name="part_number"
																								label="Areel Part Number"
																								hide-details
																								class="mb-2"
																							></v-text-field>
																							<!-- :rules="[$rules.required('part_number')]" -->

																							<vc-textarea
																								:key="i + 'description'"
																								v-model="field.description"
																								name="description"
																								outlined
																								label="Areel Description"
																								required
																							></vc-textarea>
																						</v-card-text>
																					</v-card>
																				</v-col>

																				<v-col v-if="step === 2" md="6" cols="12" class="pt-0">
																					<v-card outlined>
																						<v-card-title>Factory</v-card-title>
																						<v-card-text>
																							<v-text-field
																								:key="i + 'factory_part_number'"
																								v-model="field.factory_part_number"
																								outlined
																								hide-details
																								class="mb-2"
																								name="factory_part_number"
																								label="Form Part Number"
																							></v-text-field>

																							<vc-textarea
																								:key="i + 'factory_description'"
																								v-model="field.factory_description"
																								outlined
																								name="factory_description"
																								label="Form Description"
																								hide-details
																								class="mb-2"
																								required
																							></vc-textarea>
																						</v-card-text>
																					</v-card>
																				</v-col>
																			</v-row>
																			<v-row class="mt-4">
																				<v-col>
																					<vc-textarea
																						:key="i + 'comment'"
																						v-model="field.comment"
																						outlined
																						hide-details
																						class="mb-2"
																						name="comment"
																						label="Note"
																						required
																					></vc-textarea>
																				</v-col>
																			</v-row>
																		</template>
																		<!-- End Selectors -->
																	</v-form>
																</v-expansion-panel-content>
															</div>
														</v-expansion-panel>
													</v-form>
												</v-expansion-panels>
											</v-form>
										</v-card-text>
										<v-card-actions>
											<v-btn text @click="selector = false"> Close </v-btn>
											<v-spacer></v-spacer>
											<v-btn v-if="step == 2" color="success" @click="validationStep('take_off', 2)"> Save </v-btn>
											<v-btn v-if="step == 3" color="success" @click="validationStep('take_off', 3)"> Save </v-btn>
										</v-card-actions>
									</v-card>
								</v-dialog>
							</div>
							<div v-else>
								<v-alert text type="error" icon="mdi-alert-circle" class="mx-2 my-8">
									You do not have a permission
								</v-alert>
								<v-btn id="test" class="mt-8" text @click="step = 1"> Back </v-btn>
							</div>
						</v-stepper-content>

						<v-stepper-content step="3">
							<div v-if="checkPermission('quotes.process_factory_costing')">
								<v-card outlined class="mb-6">
									<v-card-title class="text-body-1 font-weight-medium"> Factory Details </v-card-title>
									<v-card-text>
										<v-row>
											<v-col md="3">
												<field-text-field
													key="factory_quote_number"
													v-model="quoteData.factory_quote_number"
													outlined
													hide-details
													name="factory_quote_number"
													label="Factory Quote Number"
													:rules="[$rules.required('Factory Quote Number')]"
												></field-text-field>
											</v-col>
											<v-col md="3">
												<field-date
													v-model="quoteData.factory_quote_date"
													outlined
													hide-details
													no-future
													name="factory_quote_date"
													label="Factory Quote Date"
												></field-date>
											</v-col>
											<v-col md="3">
												<vc-autocomplete
													key="factory_shipping_method"
													v-model="quoteData.factory_delivery_method"
													hide-details
													outlined
													name="factory_shipping_method"
													label="Factory Shipping Method"
													api="/v1/lookups/delivery-methods"
													item-value="text"
													item-text="text"
												></vc-autocomplete>
											</v-col>
											<v-col md="3">
												<field-text-field
													key="factory_shipping_estimated_price"
													v-model="quoteData.factory_delivery_price"
													outlined
													hide-details
													type="number"
													min="0"
													:suffix="
														quoteData.factory?.currency?.symbol
															? quoteData.factory?.currency?.symbol
															: quoteData.factory_currency?.symbol
													"
													name="factory_shipping_estimated_price"
													label="Factory Shipping Estimated Price"
												></field-text-field>
											</v-col>
										</v-row>
									</v-card-text>
									<v-card-actions>
										<v-spacer></v-spacer>
										<v-btn color="success" small @click="validationStep('data_entry', 3)"> Save </v-btn>
									</v-card-actions>
								</v-card>

								<div class="d-flex mb-3">
									<v-spacer />
									<v-btn small color="primary" @click="factoryActivitiesMethod()">
										<v-icon left small>mdi-history</v-icon> Activities
									</v-btn>

									<v-btn small dark @click="factoryCurrencyModel = true">
										<v-icon left>mdi-cash-sync</v-icon>
										Factory Currency
									</v-btn>
								</div>

								<v-dialog v-model="factoryActivitiesModal" absolute width="900px">
									<v-card>
										<v-card-title>
											<div class="mb-3">Products Activity</div>
											<v-spacer></v-spacer>
											<v-btn icon @click="factoryActivitiesModal = false">
												<v-icon>mdi-close</v-icon>
											</v-btn>
										</v-card-title>
										<v-card-text>
											<v-expansion-panels focusable class="mt-4">
												<template v-if="selectedProductActivities.length > 0">
													<v-expansion-panel
														v-for="(item, i) in selectedProductActivities"
														:key="i + 'productActivities'"
													>
														<v-expansion-panel-header>
															{{ item.name }} -
															<span class="font-weight-bold ms-2 body-2">
																{{ item.part_number }}
															</span>
														</v-expansion-panel-header>
														<v-expansion-panel-content>
															<v-card
																v-for="(activity, k) in item.activities"
																:key="k + 'activities_x2'"
																flat
																class="my-4"
															>
																<v-card-title class="d-flex">
																	<h6>
																		Edit By:
																		<span class="font-weight-bold ms-1">
																			{{ activity.user?.name }}
																		</span>
																	</h6>
																	<v-spacer></v-spacer>
																	<h6 class="me-2">{{ activity.updated_at }}</h6>
																</v-card-title>
																<v-card-text class="d-flex">
																	<div
																		v-for="(old, j) in activity.properties.old"
																		:key="j"
																		style="width: 50%"
																		class="me-2"
																	>
																		<template v-if="typeof old.value === 'object'">
																			<div
																				v-for="(value, key) in old.value"
																				:key="key"
																				class="d-flex mb-4"
																			>
																				<field-text-field
																					background-color="#ffe2e2"
																					class="me-1"
																					outlined
																					hide-details
																					:value="key"
																					:label="key"
																				></field-text-field>

																				<field-text-field
																					background-color="#ffe2e2"
																					class="me-1"
																					outlined
																					hide-details
																					:value="value ? value : 'Null'"
																					:label="value"
																				></field-text-field>
																			</div>
																		</template>

																		<template v-else>
																			<field-text-field
																				background-color="#ffe2e2"
																				class="me-1"
																				outlined
																				hide-details
																				:value="old.value ? old.value : 'Null'"
																				:label="old.key"
																			></field-text-field>
																		</template>
																	</div>
																	<div
																		v-for="(newVal, n) in activity.properties.attributes"
																		:key="n"
																		style="width: 50%"
																	>
																		<template v-if="typeof newVal.value === 'object'">
																			<div
																				v-for="(value, key) in newVal.value"
																				:key="key"
																				class="d-flex mb-4"
																			>
																				<field-text-field
																					background-color="#4caf5036"
																					class="me-1"
																					outlined
																					hide-details
																					:value="key"
																					:label="key"
																				></field-text-field>

																				<field-text-field
																					background-color="#4caf5036"
																					class="me-1"
																					outlined
																					hide-details
																					:value="value ? value : 'Null'"
																					:label="value"
																				></field-text-field>
																			</div>
																		</template>

																		<template v-else>
																			<field-text-field
																				background-color="#4caf5036"
																				class="me-1"
																				outlined
																				hide-details
																				:value="newVal.value ? newVal.value : 'Null'"
																				:label="newVal.key"
																			></field-text-field>
																		</template>
																	</div>
																</v-card-text>
															</v-card>
														</v-expansion-panel-content>
													</v-expansion-panel>
												</template>
												<div v-else>There is no activities</div>
											</v-expansion-panels>
										</v-card-text>
										<v-card-actions>
											<v-spacer></v-spacer>
											<v-btn text @click="factoryActivitiesModal = false">Cancel</v-btn>
										</v-card-actions>
									</v-card>
								</v-dialog>

								<v-dialog v-model="factoryCurrencyModel" absolute width="700px">
									<v-card>
										<v-card-title>
											<div class="mb-3">Factory Currency</div>
											<v-spacer></v-spacer>
											<v-btn icon @click="factoryCurrencyModel = false">
												<v-icon>mdi-close</v-icon>
											</v-btn>
										</v-card-title>
										<v-card-text>
											<v-row>
												<v-col md="4" cols="12">
													<vc-select
														key="factory_currency"
														v-model="quoteData.factory_currency_id"
														hide-details
														outlined
														dense
														name="factory_currency"
														label="Factory Currency"
														api="/v1/lookups/currencies"
													></vc-select>
												</v-col>
												<v-col md="4" cols="12">
													<v-text-field
														hide-details
														outlined
														value="USD"
														label="To Currency"
														disabled
														dense
													></v-text-field>
												</v-col>
												<v-col md="4" cols="12">
													<v-text-field
														key="exchange_rate"
														v-model="quoteData.factory_exchange_rate"
														hide-details
														outlined
														dense
														name="exchange_rate"
														label="Exchange Rate"
													></v-text-field>
												</v-col>
											</v-row>
										</v-card-text>
										<v-card-actions>
											<v-spacer></v-spacer>
											<v-btn color="success" @click="validationStep('data_entry', 3)">Save</v-btn>
										</v-card-actions>
									</v-card>
								</v-dialog>

								<v-card outlined class="mb-12">
									<v-card-title>
										Products
										<!------- Factory Price with search ------->
										<v-spacer />
										<v-text-field
											v-model="search"
											append-icon="mdi-magnify"
											label="Search"
											single-line
											hide-details
											clearable
											class="hidden-on-copy"
											style="max-width: 300px"
										></v-text-field>
									</v-card-title>
									<v-data-table
										id="factoryPrice"
										ref="factoryPrice"
										class="custom-table-header"
										:headers="factoryPriceTable"
										:items="quoteData.products"
										:search="search"
									>
										<template #item.actions="{ item, index }">
											<div class="hidden-on-copy">
												<v-btn icon @click="takeOffHandler(item.id, 'edit', index)">
													<v-icon color="primary" small>mdi-pencil</v-icon>
												</v-btn>
												<v-btn icon @click="quoteData.products.splice(index, 1)">
													<v-icon color="primary" small>mdi-delete</v-icon>
												</v-btn>
											</div>
										</template>

										<template #item.id="{ index }">
											{{ ++index }}
										</template>

										<template #item.description="{ item }">
											<div class="cutting-paragraph">{{ item.description }}</div>
										</template>

										<template #item.factory_description="{ item }">
											<div class="cutting-paragraph">{{ item.factory_description }}</div>
										</template>

										<template #item.factory_price="{ item }">
											<span v-if="quoteData.factory_currency?.symbol" class="me-1">
												{{ quoteData.factory_currency?.symbol }}
											</span>
											<span v-else>
												{{ quoteData.factory?.currency?.symbol }}
											</span>
											{{ item.factory_price }}
										</template>

										<template #item.is_optional="{ item }">
											<v-icon :color="item.is_optional ? 'success' : 'red'">
												{{ item.is_optional ? "mdi-check" : "mdi-close" }}
											</v-icon>
										</template>
									</v-data-table>

									<v-row class="my-3 me-1 justify-end">
										<v-col md="3">
											<v-card outlined rounded>
												<v-card-text>
													<v-list-item dense>
														<v-list-item-content class="mb-0 pb-0">
															<v-list-item-title class="pb-2">Total</v-list-item-title>
														</v-list-item-content>
														<v-list-item-content class="mb-0 pb-0 text-end pe-4">
															<v-list-item-title class="font-weight-bold pb-2">
																<span v-if="quoteData.factory_currency?.symbol">
																	{{ quoteData.factory_currency?.symbol }}
																</span>
																<span v-else>
																	{{ quoteData.factory?.currency?.symbol }}
																</span>
																<span>{{ quoteData.factory_total }}</span>
															</v-list-item-title>
														</v-list-item-content>
													</v-list-item>
												</v-card-text>
											</v-card>
										</v-col>
									</v-row>
								</v-card>

								<div class="d-flex mt-6 steep-action">
									<v-btn text @click="step = 2"> Back </v-btn>
									<v-spacer></v-spacer>
									<v-btn color="primary" @click="validationStep('data_entry', 4)"> Next </v-btn>
								</div>
							</div>
							<div v-else>
								<v-alert text type="error" icon="mdi-alert-circle" class="mx-2 my-8">
									You do not have a permission
								</v-alert>
								<v-btn class="mt-8" text @click="step = 2"> Back </v-btn>
							</div>
						</v-stepper-content>

						<v-stepper-content step="4">
							<div v-if="checkPermission('quotes.process_pricing')">
								<div class="d-flex mb-3">
									<v-spacer />
									<v-btn small color="success" @click="quoteMarkupAllProductsModel = true">
										<v-icon left>mdi-currency-usd</v-icon>
										Markup / All
									</v-btn>
									<v-btn small color="success" @click="quoteMarkupBulkProductsModel = true">
										<v-icon left>mdi-currency-usd</v-icon>
										Markup / Item
									</v-btn>
									<v-btn small dark @click="quoteCurrencyModel = true">
										<v-icon left>mdi-cash-sync</v-icon>
										Quote Currency
									</v-btn>
								</div>

								<v-dialog v-model="quoteMarkupAllProductsModel" absolute width="600px">
									<v-card>
										<v-card-title>
											<div class="mb-3">Markup for all Products</div>
											<v-spacer></v-spacer>
											<v-btn icon @click="quoteMarkupAllProductsModel = false">
												<v-icon>mdi-close</v-icon>
											</v-btn>
										</v-card-title>
										<v-card-text class="mt-4">
											<v-row>
												<v-col md="8" cols="12">
													<v-text-field
														v-model="quoteMarkupProducts.markup_price"
														hide-details
														outlined
														dense
														name="markup_price"
														label="Markup Price"
													></v-text-field>
												</v-col>

												<v-col md="4" cols="12">
													<vc-select
														v-model="quoteMarkupProducts.markup_type"
														outlined
														dense
														hide-details
														:items="feesTypes"
														label="Markup Type"
													></vc-select>
												</v-col>

												<v-col v-if="quoteData.products && quoteData.products.length" cols="12">
													<vc-autocomplete
														key="tax_label"
														v-model="quoteMarkupProducts.except"
														outlined
														multiple
														dense
														name="tax_label"
														item-text="part_number"
														item-value="id"
														:items="quoteData.products"
														label="Excepted Products"
														hide-details
													>
														<template #item="{ item }">
															<v-list-item-content>
																<v-list-item-title class="d-flex">
																	<span class="body-2">
																		{{ item.part_number }}
																	</span>
																	<span class="mx-1">/</span>
																	<span class="body-2 cutting-paragraph"> {{ item.description }} </span>
																</v-list-item-title>
															</v-list-item-content>
														</template>
													</vc-autocomplete>
												</v-col>
											</v-row>
										</v-card-text>
										<v-card-actions>
											<v-btn text @click="quoteMarkupAllProductsModel = false">Cancel</v-btn>
											<v-spacer></v-spacer>
											<v-btn color="success" @click="quoteMarkupAllProductsMethod()">Save</v-btn>
										</v-card-actions>
									</v-card>
								</v-dialog>

								<v-dialog v-model="quoteMarkupBulkProductsModel" absolute width="600px">
									<v-card>
										<v-card-title>
											<div class="mb-3">Markup for Bulk Products</div>
											<v-spacer></v-spacer>
											<v-btn icon @click="quoteMarkupBulkProductsModel = false">
												<v-icon>mdi-close</v-icon>
											</v-btn>
										</v-card-title>
										<v-card-text class="mt-4">
											<div>
												<v-card
													v-for="(field, i) in quoteMarkupBulkProducts"
													:key="i + 'quoteMarkupBulkx'"
													outlined
													class="mt-6"
												>
													<v-card-text>
														<v-row>
															<v-col md="12" cols="12" class="pb-0">
																<div class="text-end">
																	<v-btn
																		small
																		color="primary"
																		@click="quoteMarkupBulkProducts.splice(i, 1)"
																	>
																		Remove
																	</v-btn>
																</div>
															</v-col>
															<v-col md="12" cols="12">
																<vc-autocomplete
																	key="tax_label"
																	v-model="field.products"
																	outlined
																	dense
																	multiple
																	name="tax_label"
																	item-text="part_number"
																	item-value="id"
																	:items="quoteData.products"
																	label="Products"
																	hide-details
																>
																	<template #item="{ item }">
																		<v-list-item-content>
																			<v-list-item-title class="d-flex">
																				<span class="body-2">
																					{{ item.part_number }}
																				</span>
																				<span class="mx-1">/</span>
																				<span class="body-2 cutting-paragraph">
																					{{ item.description }}
																				</span>
																			</v-list-item-title>
																		</v-list-item-content>
																	</template>
																</vc-autocomplete>
															</v-col>
															<v-col md="6" cols="12">
																<v-text-field
																	:key="i + 'markup_price'"
																	v-model="field.markup_price"
																	outlined
																	dense
																	:name="'markup_price-' + i"
																	label="Markup Price"
																	hide-details
																></v-text-field>
															</v-col>
															<v-col md="6" cols="12">
																<vc-autocomplete
																	:key="i + 'markup_type'"
																	v-model="field.markup_type"
																	outlined
																	dense
																	name="markup_type"
																	item-text="text"
																	item-value="value"
																	:items="feesTypes"
																	label="Markup Type"
																	hide-details
																></vc-autocomplete>
															</v-col>
														</v-row>
													</v-card-text>

													<v-divider />
												</v-card>
												<v-btn
													class="success mt-6"
													small
													dene
													@click="
														quoteMarkupBulkProducts.push({
															markup_price: null,
															markup_type: null,
															products: [],
														})
													"
												>
													<v-icon left>mdi-plus</v-icon>
													Add New
												</v-btn>
											</div>
										</v-card-text>
										<v-card-actions>
											<v-btn text @click="quoteMarkupBulkProductsModel = false">Cancel</v-btn>
											<v-spacer></v-spacer>
											<v-btn color="success" @click="quoteMarkupBulkProductsMethod()">Save</v-btn>
										</v-card-actions>
									</v-card>
								</v-dialog>

								<v-dialog v-model="quoteCurrencyModel" absolute width="700px">
									<v-card>
										<v-card-title>
											<div class="mb-3">Quote Currency</div>
											<v-spacer></v-spacer>
											<v-btn icon @click="quoteCurrencyModel = false">
												<v-icon>mdi-close</v-icon>
											</v-btn>
										</v-card-title>
										<v-card-text>
											<v-row>
												<v-col md="4" cols="12">
													<v-text-field
														hide-details
														outlined
														disabled
														dense
														value="USD"
														name="From Currency"
														label="From Currency"
													></v-text-field>
												</v-col>
												<v-col md="4" cols="12">
													<vc-select
														key="to_currency"
														v-model="quoteData.client_currency_id"
														hide-details
														outlined
														dense
														name="to_currency"
														label="To Currency"
														api="/v1/lookups/currencies"
													></vc-select>
												</v-col>

												<v-col md="4" cols="12">
													<v-text-field
														key="exchange_rate"
														v-model="quoteData.client_exchange_rate"
														hide-details
														outlined
														dense
														name="exchange_rate"
														label="Exchange Rate"
													></v-text-field>
												</v-col>
											</v-row>
										</v-card-text>
										<v-card-actions>
											<v-btn text @click="quoteCurrencyModel = false">Cancel</v-btn>
											<v-spacer></v-spacer>
											<v-btn color="success" @click="validationStep('pricing', 4)">Save</v-btn>
										</v-card-actions>
									</v-card>
								</v-dialog>

								<v-card outlined class="mb-12">
									<v-card-title>
										Products
										<!------- Factory Price with search ------->
										<v-spacer />
										<v-text-field
											v-model="search"
											append-icon="mdi-magnify"
											label="Search"
											single-line
											hide-details
											clearable
											class="hidden-on-copy"
											style="max-width: 300px"
										></v-text-field>
									</v-card-title>
									<v-data-table
										id="factoryPrice"
										ref="factoryPriceRef"
										class="custom-table-header"
										:headers="priceTable"
										:items="quoteData.products"
										:search="search"
									>
										<template #item.actions="{ index }">
											<div class="hidden-on-copy">
												<v-btn icon @click="priceProductHandler(index)">
													<v-icon color="primary" small>mdi-pencil</v-icon>
												</v-btn>

												<v-btn icon @click="quoteData.products.splice(index, 1)">
													<v-icon color="primary" small>mdi-delete</v-icon>
												</v-btn>
											</div>
										</template>

										<template #item.unit_price="{ item }">
											{{ item.unit_price }} <span class="ms-1">{{ quoteData.client_currency?.symbol }}</span>
										</template>

										<template #item.description="{ item }">
											<div class="cutting-paragraph">{{ item.description }}</div>
										</template>

										<template #item.id="{ index }">
											{{ ++index }}
										</template>
									</v-data-table>

									<v-row class="my-4 me-1 justify-end">
										<v-col md="3">
											<v-card outlined rounded="0">
												<v-card-text>
													<v-list-item dense>
														<v-list-item-content class="mb-0 pb-0">
															<v-list-item-title>Subtotal</v-list-item-title>
														</v-list-item-content>
														<v-list-item-content class="mb-0 pb-0 text-end pe-4">
															<v-list-item-title class="font-weight-bold">
																USD {{ quoteData.subtotal }}
															</v-list-item-title>
														</v-list-item-content>
													</v-list-item>
													<v-divider />

													<v-list-item v-if="quoteData.custom_fees" dense>
														<v-list-item-content class="mb-0 pb-0">
															<v-list-item-title>Custom Fees</v-list-item-title>
														</v-list-item-content>
														<v-list-item-content class="mb-0 pb-0 text-end pe-4">
															<v-list-item-title class="font-weight-bold">
																USD {{ quoteData.custom_fees }}
															</v-list-item-title>
														</v-list-item-content>
													</v-list-item>
													<v-divider v-if="quoteData.custom_fees" />

													<v-list-item v-if="quoteData.clearance_freight" dense>
														<v-list-item-content class="mb-0 pb-0">
															<v-list-item-title class="caption">Clearance Freight</v-list-item-title>
														</v-list-item-content>
														<v-list-item-content class="mb-0 pb-0 text-end pe-4">
															<v-list-item-title class="font-weight-bold">
																{{ quoteData.clearance_freight }}
															</v-list-item-title>
														</v-list-item-content>
													</v-list-item>
													<v-divider v-if="quoteData.clearance_freight" />

													<v-list-item dense>
														<v-list-item-content class="mb-0 pb-0">
															<v-list-item-title>Total</v-list-item-title>
														</v-list-item-content>
														<v-list-item-content class="mb-0 pb-0 text-end pe-4">
															<v-list-item-title class="font-weight-bold">
																USD {{ quoteData.total }}
															</v-list-item-title>
														</v-list-item-content>
													</v-list-item>
												</v-card-text>

												<!-- <v-list-item v-if="quoteData.tax_label && quoteData.tax" dense>
													<v-list-item-content class="mb-0 pb-0">
														<v-list-item-title class="text-capitalize">{{
															quoteData.tax_label
														}}</v-list-item-title>
													</v-list-item-content>
													<v-list-item-content class="mb-0 pb-0 text-end pe-4">
														<v-list-item-title class="font-weight-bold">
															%{{ quoteData.tax }}
														</v-list-item-title>
													</v-list-item-content>
												</v-list-item>
												<v-divider v-if="quoteData.tax_label && quoteData.tax" />

												<v-list-item v-if="quoteData.discount" dense>
													<v-list-item-content class="mb-0 pb-0">
														<v-list-item-title>Special Discount</v-list-item-title>
													</v-list-item-content>
													<v-list-item-content class="mb-0 pb-0 text-end pe-4">
														<v-list-item-title class="font-weight-bold">
															%{{ quoteData.discount }}
														</v-list-item-title>
													</v-list-item-content>
												</v-list-item>
												<v-divider v-if="quoteData.discount" /> -->

												<v-card-actions>
													<v-btn small @click="priceDiscountsModel = true"> Edit </v-btn>
												</v-card-actions>
											</v-card>
										</v-col>
									</v-row>
								</v-card>

								<div class="d-flex">
									<v-btn text @click="step = 3"> Back </v-btn>
									<v-spacer></v-spacer>
									<v-btn color="primary" @click="validationStep('pricing', 5)"> Next </v-btn>
								</div>

								<v-dialog v-model="priceProductModel" persistent absolute width="900px">
									<v-card>
										<v-card-title>
											<div class="mb-3">
												Pricing -
												<span class="body-1">{{ selectedProduct?.part_number }} - {{ selectedProduct?.name }}</span>
											</div>
											<v-spacer></v-spacer>
											<v-btn icon @click="priceProductModel = false">
												<v-icon>mdi-close</v-icon>
											</v-btn>
										</v-card-title>
										<v-card-text>
											<v-card flat>
												<v-card-text>
													<div class="d-flex mb-2">
														<v-spacer />
														<v-btn
															v-tooltip="'Client Price History'"
															dark
															small
															class="text-caption"
															@click="priceHistoryFunction(indexField)"
														>
															Price History
														</v-btn>
													</div>

													<v-card outlined class="mb-5">
														<v-card-text>
															<v-row>
																<v-col v-if="quoteData.products[indexField]" md="4">
																	<!-- :suffix="
																			quoteData.factory_currency?.symbol
																				? quoteData.factory_currency?.symbol
																				: quoteData.factory?.currency?.symbol
																		" -->
																	<div v-if="quoteData.products[indexField].factory_price">
																		<v-text-field
																			:key="indexField + 'factory_price'"
																			v-model="quoteData.products[indexField].factory_price"
																			dense
																			outlined
																			hide-details
																			type="number"
																			min="0"
																			disabled
																			suffix="USD"
																			name="factory_price"
																			label="Factory Price"
																		></v-text-field>
																	</div>
																	<div v-else>
																		<v-text-field
																			:key="indexField + 'factory_price'"
																			v-model="quoteData.products[indexField].assumed_price"
																			dense
																			outlined
																			hide-details
																			type="number"
																			min="0"
																			disabled
																			suffix="USD"
																			name="assumed_price"
																			label="Assumed Price"
																		></v-text-field>
																	</div>
																</v-col>
																<v-col md="4">
																	<v-text-field
																		v-if="quoteData.products[indexField]"
																		:key="indexField + 'unit_price'"
																		v-model="quoteData.products[indexField].unit_price"
																		dense
																		outlined
																		disabled
																		hide-details
																		type="number"
																		min="0"
																		name="unit_price"
																		label="Unit Price"
																	></v-text-field>
																</v-col>
																<v-col md="4">
																	<div class="d-flex">
																		<v-text-field
																			v-if="quoteData.products[indexField]"
																			:key="indexField + 'markup_price'"
																			v-model="quoteData.products[indexField].markup_price"
																			dense
																			outlined
																			hide-details
																			name="markup_price"
																			label="Markup Price"
																			class="me-1"
																			suffix="%"
																		></v-text-field>
																		<vc-select
																			v-if="quoteData.products[indexField]"
																			v-model="quoteData.products[indexField].markup_type"
																			dense
																			outlined
																			hide-details
																			:items="feesTypes"
																			label="Type"
																			style="width: 80px"
																		></vc-select>
																	</div>
																</v-col>
															</v-row>
														</v-card-text>
													</v-card>

													<v-card outlined>
														<v-card-text>
															<v-row v-if="quoteData.products[indexField]">
																<v-col md="4">
																	<v-text-field
																		:key="indexField + 'custom_fees'"
																		v-model="quoteData.products[indexField].custom_fees"
																		hide-details
																		dense
																		outlined
																		type="number"
																		min="0"
																		name="custom_fees"
																		label="Custom Fees"
																		class="me-1 mb-4"
																		suffix="%"
																	></v-text-field>
																</v-col>
																<!-- <v-col md="4">
																	<field-text-field
																		:key="indexField + 'discount'"
																		v-model="quoteData.products[indexField].discount"
																		hide-details
																		dense
																		outlined
																		type="number"
																		min="0"
																		name="discount"
																		label="Discount"
																		class="me-1 mb-4"
																	></field-text-field>
																</v-col> -->
																<v-col md="4">
																	<field-text-field
																		:key="indexField + 'clearance_freight'"
																		v-model="quoteData.products[indexField].clearance_freight"
																		hide-details
																		dense
																		outlined
																		type="number"
																		min="0"
																		label="Clearance charges & Inland freight"
																		class="mb-4 me-1"
																	></field-text-field>
																</v-col>
																<v-col md="4">
																	<vc-select
																		v-model="quoteData.products[indexField].clearance_freight_type"
																		outlined
																		hide-details
																		dense
																		:items="feesTypes"
																		label="Clearance Type"
																	></vc-select>
																</v-col>
															</v-row>
														</v-card-text>
													</v-card>
												</v-card-text>
											</v-card>
										</v-card-text>
										<v-card-actions>
											<v-spacer></v-spacer>
											<v-btn text @click="validationStep('pricing', 4)">Save</v-btn>
										</v-card-actions>
									</v-card>
								</v-dialog>

								<v-dialog v-model="priceDiscountsModel" persistent absolute width="700px">
									<v-card>
										<v-card-title>
											<div class="mb-3">Clearance Fees & Clearance</div>
											<v-spacer></v-spacer>
											<v-btn icon @click="priceDiscountsModel = false">
												<v-icon>mdi-close</v-icon>
											</v-btn>
										</v-card-title>
										<v-card-text>
											<v-card flat>
												<v-card-text>
													<v-card outlined class="mb-5">
														<v-card-text>
															<v-row v-if="step == 4">
																<v-col md="6">
																	<vc-text-field
																		v-model="quoteData.custom_fees"
																		name="custom_fees"
																		outlined
																		dense
																		label="Custom Fees"
																		hide-details
																		suffix="%"
																	></vc-text-field>
																</v-col>

																<v-col md="6">
																	<div class="d-flex">
																		<vc-text-field
																			v-model="quoteData.clearance_freight"
																			name="clearance_freight"
																			outlined
																			hide-details
																			dense
																			class="me-1"
																			label="Clearance Freight"
																		></vc-text-field>
																		<vc-select
																			v-model="quoteData.clearance_freight_type"
																			outlined
																			dense
																			hide-details
																			:items="feesTypes"
																			label="Type"
																			style="width: 100px"
																		></vc-select>
																	</div>
																</v-col>
															</v-row>

															<div v-else>
																<vc-text-field
																	v-model="quoteData.discount"
																	name="discount"
																	outlined
																	class="mb-4"
																	label="Discount"
																	hide-details
																	dense
																	prepend-inner-icon="mdi-percent"
																></vc-text-field>

																<v-row>
																	<v-col md="4">
																		<vc-select
																			key="tax_label"
																			v-model="quoteData.tax_label"
																			outlined
																			name="tax_label"
																			item-text="text"
																			item-value="value"
																			:items="['tax', 'vat']"
																			label="Type"
																			hide-details
																			dense
																		></vc-select>
																	</v-col>
																	<v-col md="8">
																		<vc-text-field
																			key="tax"
																			v-model="quoteData.tax"
																			name="tax"
																			outlined
																			label="Value"
																			dense
																			hide-details
																			prepend-inner-icon="mdi-percent"
																		></vc-text-field>
																	</v-col>
																</v-row>
															</div>
														</v-card-text>
													</v-card>
												</v-card-text>
											</v-card>
										</v-card-text>
										<v-card-actions>
											<v-spacer></v-spacer>
											<!-- <v-btn text @click="validationStep('data_entry', 4), (priceDiscountsModel = false)">Save</v-btn> -->
											<v-btn text @click="priceDiscountMethod('pricing')">Save</v-btn>
										</v-card-actions>
									</v-card>
								</v-dialog>
							</div>
							<div v-else>
								<v-alert text type="error" icon="mdi-alert-circle" class="mx-2 my-8">
									You do not have a permission
								</v-alert>
								<v-btn class="mt-8" text @click="step = 3"> Back </v-btn>
							</div>
						</v-stepper-content>

						<v-stepper-content step="5">
							<div v-if="checkPermission('quotes.process_shipping')">
								<v-card outlined class="mt-4">
									<v-card-title> Delivery </v-card-title>
									<v-card-text>
										<v-row class="mb-5">
											<v-col md="6" cols="12" class="mt-7">
												<vc-select
													key="factory_shipping_method"
													v-model="quoteData.factory_delivery_method"
													hide-details
													outlined
													disabled
													name="factory_shipping_method"
													label="Factory Shipping Method"
													api="/v1/lookups/delivery-methods"
													item-value="text"
													item-text="text"
												></vc-select>

												<field-text-field
													key="factory_shipping_estimated_price"
													v-model="quoteData.factory_delivery_price"
													outlined
													hide-details
													type="number"
													disabled
													min="0"
													:suffix="
														quoteData.factory?.currency?.symbol
															? quoteData.factory?.currency?.symbol
															: quoteData.factory_currency?.symbol
													"
													name="factory_shipping_estimated_price"
													label="Factory Shipping Estimated Price"
													class="mt-5"
												></field-text-field>

												<vc-select
													:key="quoteData.branch_id"
													v-model="quoteData.delivery_method_id"
													outlined
													name="delivery_method"
													item-text="text"
													item-value="value"
													:api="`/v1/lookups/delivery-methods?branch_id=${quoteData.branch_id}`"
													label="Method of delivery"
													class="mt-5"
													hide-details
												></vc-select>

												<div v-if="quoteData.delivery_method_id !== 5" class="mt-5">
													<vc-select
														:key="quoteData.delivery_method_id"
														v-model="quoteData.from_delivery_port_id"
														outlined
														name="from_delivery_port_id"
														item-text="text"
														item-value="value"
														:api="`/v1/lookups/delivery-ports?country_id=${quoteData.factory?.country_id}&delivery_method_id=${quoteData.delivery_method_id}`"
														label="From delivery port"
													></vc-select>

													<vc-select
														:key="quoteData.delivery_method_id"
														v-model="quoteData.delivery_port_id"
														outlined
														name="delivery_port_id"
														item-text="text"
														item-value="value"
														:api="`/v1/lookups/delivery-ports?country_id=${quoteData.country_id}&delivery_method_id=${quoteData.delivery_method_id}`"
														label="Delivery Port"
													></vc-select>

													<v-radio-group v-model="deliveryPriceType" row>
														<v-radio label="Delivery Cost" value="cost"></v-radio>
														<v-radio label="Delivery Estimated" value="estimated"></v-radio>
													</v-radio-group>

													<vc-text-field
														v-if="deliveryPriceType === 'estimated'"
														v-model="quoteData.estimated_delivery_price"
														name="estimated_delivery_price"
														outlined
														label="Delivery Estimated"
													></vc-text-field>

													<vc-text-field
														v-if="deliveryPriceType === 'cost'"
														v-model="quoteData.actual_delivery_price"
														name="actual_delivery_price"
														outlined
														label="Delivery Cost"
														@input="calculateDeliveryPrice()"
													></vc-text-field>

													<vc-text-field
														v-model="quoteData.delivery_multiplier"
														name="delivery_price"
														outlined
														label="Delivery Multiplier"
														prepend-inner-icon="mdi-percent-outline"
														@input="calculateDeliveryPrice()"
													></vc-text-field>

													<vc-text-field
														v-model="quoteData.delivery_price"
														name="delivery_price"
														outlined
														label="Delivery Price"
														type="number"
														disabled
													>
													</vc-text-field>
												</div>
												<div v-else>
													<vc-text-field
														v-model="quoteData.documentation_cost"
														name="delivery_price"
														class="mt-5"
														outlined
														label="Packaging and Documentation Cost"
														type="number"
													></vc-text-field>
												</div>
											</v-col>
											<v-col md="6" cols="12">
												<field-wysiwyg
													v-model="quoteData.delivery_method_terms"
													name="notes"
													label="Delivery terms"
												></field-wysiwyg>
											</v-col>
											<v-col cols="12">
												<field-wysiwyg
													v-model="quoteData.packaging_details"
													name="notes"
													label="Packaging Details"
												></field-wysiwyg>
											</v-col>
										</v-row>
									</v-card-text>
								</v-card>

								<div class="d-flex mt-6">
									<v-btn text @click="step = 4"> Back </v-btn>
									<v-spacer></v-spacer>
									<v-btn color="primary" @click="validationStep('shipping', 6)"> Next </v-btn>
								</div>
							</div>
							<div v-else>
								<v-alert text type="error" icon="mdi-alert-circle" class="mx-2 my-8">
									You do not have a permission
								</v-alert>
								<v-btn class="mt-8" text @click="step = 4"> Back </v-btn>
							</div>
						</v-stepper-content>

						<v-stepper-content step="6">
							<div v-if="checkPermission('quotes.process_payment')">
								<v-card outlined class="mb-6">
									<v-card-text>
										<v-row>
											<v-col md="6">
												<div class="d-flex">
													<vc-text-field
														v-model="quoteData.valid_until"
														outlined
														class="me-1"
														name="valid_until"
														label="Valid Until"
														:min="1"
														type="number"
														hide-details
														:rules="[$rules.required('Valid Until')]"
														suffix="Days"
														@input="setDate(quoteData.valid_until, 'days')"
													></vc-text-field>

													<field-date
														v-model="quoteData.valid_until_date"
														outlined
														hide-details
														name="valid_until_date"
														label="Valid Until Date"
													></field-date>
												</div>
											</v-col>
											<v-col md="6">
												<vc-select
													v-model="quoteData.status"
													outlined
													hide-details
													name="status"
													item-text="text"
													item-value="value"
													:items="statusItems"
													label="Status"
												></vc-select>
											</v-col>

											<v-col cols="12">
												<field-wysiwyg
													v-model="quoteData.notes"
													name="notes"
													label="Quotation Note"
												></field-wysiwyg>
											</v-col>
										</v-row>
									</v-card-text>
								</v-card>

								<v-card outlined class="fill-height">
									<v-card-title class="mb-3"> Payment </v-card-title>
									<v-card-text>
										<v-row>
											<v-col md="12" cols="12">
												<vc-select
													v-model="quoteData.payment_method_id"
													outlined
													api="/v1/lookups/payment-methods"
													item-text="text"
													item-value="value"
													name="payment_method"
													label="Payment Method"
													@input="updatePaymentMethodTerms"
												>
												</vc-select>

												<field-wysiwyg
													v-model="quoteData.payment_method_terms"
													name="payment_method_comment"
													label="Payment Method Terms"
												></field-wysiwyg>
											</v-col>
										</v-row>
									</v-card-text>
								</v-card>

								<div class="d-flex mt-6">
									<v-btn text @click="step = 5"> Back </v-btn>
									<v-spacer></v-spacer>
									<v-btn color="primary" @click="validationStep('payment', 7)"> Next </v-btn>
								</div>
							</div>
							<div v-else>
								<v-alert text type="error" icon="mdi-alert-circle" class="mx-2 my-8">
									You do not have a permission
								</v-alert>
								<v-btn class="mt-8" text @click="step = 5"> Back </v-btn>
							</div>
						</v-stepper-content>

						<v-stepper-content step="7">
							<div v-if="checkPermission('quotes.generate_formal_quote')">
								<div class="d-flex">
									<v-btn class="me-3" dark small @click="step = 6"><v-icon left>mdi-arrow-left</v-icon> Back </v-btn>

									<v-checkbox
										v-model="disable_factory_logo"
										class="mt-0 pt-1"
										hide-details
										:disabled="!quoteData.factory?.logo"
										label="Display Factory Logo"
									></v-checkbox>

									<v-spacer></v-spacer>

									<v-menu rounded="rounded" offset-y>
										<template #activator="{ attrs, on }">
											<v-btn dark small class="white--text" v-bind="attrs" v-on="on">
												<v-icon left>mdi-download</v-icon> Download
											</v-btn>
										</template>

										<v-list>
											<v-list-item link @click="exportPdf('formal-quote-pdf')">
												<v-list-item-title>As PDF</v-list-item-title>
											</v-list-item>
											<v-list-item link @click="captureScreenshot('formal-quote-pdf')">
												<v-list-item-title>As Image</v-list-item-title>
											</v-list-item>
										</v-list>
									</v-menu>

									<v-btn small dark class="me-1" @click="(createRevision = true), saveData('formal_quote', 7)">
										<v-icon left>mdi-content-copy</v-icon> Create Revision
									</v-btn>

									<v-btn small color="primary" @click="confirmHandler('order', 8)">
										<template v-if="!quoteData.order">
											<v-icon left>mdi-order-bool-ascending-variant</v-icon>Convert To Order
										</template>
										<template v-else> <v-icon left>mdi-order-bool-ascending-variant</v-icon>Next </template>
									</v-btn>
								</div>

								<template v-if="formalQuoteType === 'standard'">
									<pdfs-formal-quotes-standard :disable-factory-logo="disable_factory_logo" :quote-data="quoteData" />
								</template>
								<template v-if="formalQuoteType === 'express'"> </template>
								<template v-if="formalQuoteType === 'sea-freight'"> </template>
								<template v-if="formalQuoteType === 'air-freight-standard'"> </template>
								<template v-if="formalQuoteType === 'air-freight-express'"> </template>
								<template v-if="formalQuoteType === 'ex-works'"> </template>
								<template v-if="formalQuoteType === 'dap'"> </template>
								<template v-if="formalQuoteType === 'land-freight'"> </template>
							</div>
							<div v-else>
								<v-alert text type="error" icon="mdi-alert-circle" class="mx-2 my-8">
									You do not have a permission
								</v-alert>
								<v-btn class="mt-8" text @click="step = 6"> Back </v-btn>
							</div>
						</v-stepper-content>

						<v-stepper-content step="8">
							<div v-if="checkPermission('quotes.convert_to_order')">
								<div class="d-flex">
									<v-spacer />
									<v-btn outlined small @click="step = 7">
										<v-icon left>mdi-arrow-left</v-icon> Back To Formal Quote
									</v-btn>
								</div>

								<v-card flat>
									<v-card-text>
										<v-stepper v-model="orderStep" class="stepper-auto-height" flat vertical>
											<v-stepper-step :complete="orderStep > 1" step="1"> Waiting For Customer PO </v-stepper-step>

											<v-stepper-content step="1">
												<v-card flat class="mb-12">
													<v-card-text>
														<v-row>
															<v-col md="6">
																<field-text-field
																	v-model="quoteOrderData.client_po_number"
																	outlined
																	name="client_po_number"
																	label="Client PO Number"
																	:rules="[$rules.required('Client PO Number')]"
																></field-text-field>
															</v-col>
															<v-col md="6">
																<field-date
																	v-model="quoteOrderData.client_po_date"
																	outlined
																	hide-details
																	name="client_po_date"
																	label="Client PO Date"
																></field-date>
															</v-col>
															<v-col cols="12">
																<v-checkbox
																	v-model="quoteOrderData.bill_same_ship"
																	label="The same contact person is used for bill to and ship to"
																	hide-details
																>
																</v-checkbox>
															</v-col>
															<v-col
																v-if="quoteOrderData.quote && quoteOrderData.quote.contact_people.length"
																md="6"
															>
																<vc-autocomplete
																	key="bill_to"
																	v-model="quoteOrderData.bill_to"
																	outlined
																	name="bill_to"
																	item-text="name"
																	item-value="id"
																	:items="quoteOrderData.quote.contact_people"
																	:label="quoteOrderData.bill_same_ship ? 'Bill & Ship To' : 'Bill To'"
																	hide-details
																></vc-autocomplete>
															</v-col>

															<v-col
																v-if="
																	quoteOrderData.quote &&
																	quoteOrderData.quote.contact_people.length &&
																	!quoteOrderData.bill_same_ship
																"
																md="6"
															>
																<vc-autocomplete
																	key="ship_to"
																	v-model="quoteOrderData.ship_to"
																	outlined
																	name="ship_to"
																	item-text="name"
																	item-value="id"
																	:items="quoteOrderData.quote.contact_people"
																	label="Ship To"
																	hide-details
																></vc-autocomplete>
															</v-col>

															<v-col md="12">
																<field-wysiwyg
																	v-model="quoteOrderData.client_payment_details"
																	name="client_payment_details"
																	label="Client Payment Details"
																></field-wysiwyg>
															</v-col>

															<v-col md="12">
																<v-checkbox
																	v-model="quoteOrderData.client_confirm_drawings"
																	label="Confirm a signed drawing from customer"
																	:rules="[$rules.required('Confirm a signed drawing from customer')]"
																></v-checkbox>
															</v-col>
														</v-row>
													</v-card-text>
												</v-card>

												<div class="d-flex mb-5">
													<v-btn color="success" @click="updateQuoteOrder('waiting_for_client_po')">
														Generate PI
													</v-btn>
													<v-btn
														:disabled="!quoteOrderData.can_generate_waiting_for_client_po_documents"
														dark
														@click="piDialog = true"
													>
														<v-icon left>mdi-eye</v-icon> Proforma Invoice
													</v-btn>
													<v-spacer />
													<v-btn color="primary" @click="updateQuoteOrder('waiting_for_client_po', 'next')">
														Next
													</v-btn>
												</div>
											</v-stepper-content>

											<v-stepper-step :complete="orderStep > 2" step="2"> Waiting For Payment </v-stepper-step>
											<v-stepper-content step="2">
												<v-card flat class="mb-12">
													<v-card-text>
														<v-row>
															<v-col md="12">
																<field-date
																	v-model="quoteOrderData.factory_lead_date"
																	outlined
																	hide-details
																	name="factory_lead_time"
																	label="Factory Lead Time"
																></field-date>
															</v-col>
															<v-col md="12">
																<field-wysiwyg
																	v-model="quoteOrderData.factory_payment_details"
																	name="factory_payment_details"
																	label="Factory Payment Details"
																></field-wysiwyg>
															</v-col>
															<v-col md="12">
																<v-checkbox
																	v-model="quoteOrderData.received_first_payment"
																	label="Received First down payment"
																	:rules="[$rules.required('Received First down payment')]"
																></v-checkbox>
															</v-col>
														</v-row>

														<v-row>
															<v-col class="d-flex">
																<v-btn
																	class="me-2"
																	color="success"
																	@click="updateQuoteOrder('waiting_for_client_payment')"
																>
																	Generate PDFs
																</v-btn>

																<div
																	v-if="quoteOrderData.can_generate_waiting_for_client_payment_documents"
																>
																	<v-btn dark @click="poDialog = true">
																		<v-icon left>mdi-eye</v-icon> PO
																	</v-btn>

																	<v-btn dark @click="piDialog = true">
																		<v-icon left>mdi-eye</v-icon> PI
																	</v-btn>

																	<v-btn dark @click="cinDialog = true">
																		<v-icon left>mdi-eye</v-icon> CI
																	</v-btn>

																	<v-btn dark @click="ptDialog = true">
																		<v-icon left>mdi-eye</v-icon> PT
																	</v-btn>
																</div>
															</v-col>
														</v-row>
													</v-card-text>
												</v-card>

												<div class="d-flex mb-5">
													<v-btn text @click="orderStep = 1"> Back </v-btn>

													<v-spacer />
													<v-btn color="primary" @click="updateQuoteOrder('waiting_for_client_payment', 'next')">
														Next
													</v-btn>
												</div>
											</v-stepper-content>

											<v-stepper-step :complete="orderStep > 3" step="3">
												Waiting For Factory Confirmation
											</v-stepper-step>
											<v-stepper-content step="3">
												<v-card flat class="mb-12">
													<v-card-text>
														<v-row>
															<v-col>
																<div class="d-flex mb-3">
																	<v-spacer></v-spacer>
																	<v-btn
																		outlined
																		color="black"
																		small
																		@click="factoryConfirmationsModal('create')"
																	>
																		<v-icon left>mdi-plus</v-icon>
																		Add New
																	</v-btn>
																</div>
																<v-simple-table class="mb-16">
																	<thead style="background: #232323">
																		<tr>
																			<th class="text-left" style="color: #fff">#</th>
																			<th class="text-left" style="color: #fff">
																				Received From Factory
																			</th>
																			<th class="text-left" style="color: #fff">Readiness Date</th>
																			<th class="text-left" style="color: #fff">Additional Weeks</th>
																			<th class="text-left" style="color: #fff">
																				Delivery With Additional Weeks
																			</th>
																			<th class="text-left" style="color: #fff">Attachment</th>
																			<th class="text-left" style="color: #fff"></th>
																		</tr>
																	</thead>
																	<tbody>
																		<template
																			v-if="
																				factoryConfirmationsArrayData &&
																				factoryConfirmationsArrayData.length
																			"
																		>
																			<tr
																				v-for="(item, i) in factoryConfirmationsArrayData"
																				:key="i + 'factoryConfirmation_x'"
																			>
																				<td>{{ 1 + i }}</td>
																				<td>
																					<v-icon
																						class="hidden-on-copy"
																						:color="
																							item.received_from_factory ? 'success' : 'red'
																						"
																					>
																						{{
																							item.received_from_factory
																								? "mdi-check"
																								: "mdi-close"
																						}}
																					</v-icon>
																				</td>
																				<td>{{ item.delivery_to_client_at | date }}</td>
																				<td>{{ item.additional_weeks }}</td>
																				<td>
																					{{
																						item.delivery_to_client_with_additional_weeks | date
																					}}
																				</td>
																				<td>
																					<v-btn
																						text
																						small
																						color="primary"
																						class="text-capitalize"
																						style="text-decoration: underline"
																						@click="
																							orderPdfLoop('factory-confirmations', item.id)
																						"
																					>
																						AKN PDF
																					</v-btn>
																				</td>

																				<td style="width: 20px">
																					<div class="d-flex">
																						<v-btn
																							icon
																							@click="
																								factoryConfirmationsModal('edit', item.id)
																							"
																						>
																							<v-icon color="primary" small
																								>mdi-pencil</v-icon
																							>
																						</v-btn>

																						<v-btn
																							icon
																							@click="factoryConfirmationsDelete(item.id)"
																						>
																							<v-icon color="primary" small
																								>mdi-delete</v-icon
																							>
																						</v-btn>
																					</div>
																				</td>
																			</tr>
																		</template>
																		<template v-else>
																			<tr>
																				<td colspan="11">No data Available</td>
																			</tr>
																		</template>
																	</tbody>
																</v-simple-table>
															</v-col>
														</v-row>

														<v-dialog v-model="factoryConfirmationsDialog" absolute width="800px">
															<v-card>
																<v-card-title>
																	Factory Confirmations
																	<v-spacer></v-spacer>
																	<v-btn icon @click="factoryConfirmationsDialog = false">
																		<v-icon>mdi-close</v-icon>
																	</v-btn>
																</v-card-title>
																<v-card-text>
																	<v-row class="mt-3">
																		<v-col md="12">
																			<v-checkbox
																				v-model="factoryConfirmations.received_from_factory"
																				label="Received From Factory"
																				:rules="[$rules.required('Received from factory')]"
																			></v-checkbox>
																		</v-col>
																		<v-col md="6">
																			<field-date
																				v-model="factoryConfirmations.delivery_to_client_at"
																				outlined
																				hide-details
																				name="delivery_to_client_at"
																				label="Readiness Date"
																			></field-date>
																		</v-col>
																		<v-col md="6">
																			<field-text-field
																				v-model="factoryConfirmations.additional_weeks"
																				outlined
																				name="additional_weeks"
																				label="Additional Weeks"
																				hide-details
																			></field-text-field>
																		</v-col>

																		<v-col
																			v-if="quoteOrderData.quote && quoteOrderData.quote.products"
																			md="12"
																			cols="12"
																			class="mt-4"
																		>
																			<!-- :key="randomKey()" -->
																			<vc-autocomplete
																				:key="generateRandomKey"
																				v-model="factoryConfirmations.confirmation_products"
																				outlined
																				multiple
																				name="products"
																				item-text="name"
																				item-value="id"
																				return-object
																				:api="`/v1/lookups/order-products/${quoteOrderData.id}?confirmation_id=${confirmation_id}`"
																				label="Products"
																				hide-details
																			>
																				<template #item="{ item }">
																					<v-list-item-content>
																						<v-list-item-title>
																							<span> {{ item.name }} / </span>
																							<span class="mx-1 body-2">
																								<small>Quantity:</small>
																								{{ item.quantity }}
																								<small class="ms-2">Remaining:</small>
																								{{ item.confirmation_remaining }}
																							</span>
																						</v-list-item-title>
																					</v-list-item-content>
																				</template>
																				<template #selection="{ item }">
																					<div class="d-flex d-block" style="width: 100%">
																						<v-list-item-content>
																							<v-list-item-title>
																								<span class="me-3">{{ item.name }}</span>
																								<small class="font-weight-bold ms-2">
																									Original: {{ item.quantity }}
																								</small>
																								<small class="font-weight-bold ms-2">
																									Remaining:
																									{{ item.confirmation_remaining }}
																								</small>
																							</v-list-item-title>
																						</v-list-item-content>
																						<v-list-item-action>
																							<v-btn
																								text
																								small
																								color="primary"
																								@click="
																									addQuantity(
																										item.id,
																										'confirmation',
																										factoryConfirmations.id
																									)
																								"
																							>
																								Confirmation Quantity:
																								<span class="font-weight-bold">
																									{{ item.confirmation_quantity }}
																								</span>
																							</v-btn>
																						</v-list-item-action>
																					</div>
																				</template>
																			</vc-autocomplete>
																		</v-col>
																	</v-row>
																</v-card-text>
																<v-card-actions class="d-flex">
																	<v-spacer></v-spacer>
																	<v-btn color="success" @click="factoryConfirmationsApi()"> Save </v-btn>
																</v-card-actions>
															</v-card>
														</v-dialog>
													</v-card-text>
												</v-card>

												<div class="d-flex mb-5">
													<v-btn text @click="orderStep = 2"> Back </v-btn>

													<!-- <v-btn color="success" @click="updateQuoteOrder('waiting_for_factory_confirmation')">
														Confirmation for customer
													</v-btn>

													<v-btn
														dark
														:disabled="!quoteOrderData.can_generate_waiting_for_factory_confirmation_documents"
														@click="confirmationForCustomerDialog = true"
													>
														<v-icon left>mdi-eye</v-icon> Order Confirmation
													</v-btn> -->

													<v-spacer />
													<v-btn
														color="primary"
														@click="updateQuoteOrder('waiting_for_factory_confirmation', 'next')"
													>
														Next
													</v-btn>
												</div>
											</v-stepper-content>

											<v-stepper-step :complete="orderStep > 4" step="4">
												Waiting For Factory Invoice
											</v-stepper-step>
											<v-stepper-content step="4">
												<v-card flat class="mb-12">
													<v-card-text>
														<v-row>
															<v-col>
																<div class="d-flex mb-3">
																	<v-spacer></v-spacer>
																	<v-btn
																		outlined
																		color="black"
																		small
																		@click="waitingFactoryInvoiceModal('create')"
																	>
																		<v-icon left>mdi-plus</v-icon>
																		Add New
																	</v-btn>
																</div>
																<v-simple-table class="mb-16">
																	<thead style="background: #232323">
																		<tr>
																			<th class="text-left" style="color: #fff">#</th>
																			<th class="text-left" style="color: #fff">Invoice Number</th>
																			<th class="text-left" style="color: #fff">Invoice Amount</th>
																			<th class="text-left" style="color: #fff">Invoice Date</th>
																			<th class="text-left" style="color: #fff">Attachment</th>
																			<th class="text-left" style="color: #fff"></th>
																		</tr>
																	</thead>
																	<tbody>
																		<template
																			v-if="
																				factoryInvoicesArrayData && factoryInvoicesArrayData.length
																			"
																		>
																			<tr
																				v-for="(item, i) in factoryInvoicesArrayData"
																				:key="'invoice_' + i"
																			>
																				<td>{{ 1 + i }}</td>
																				<td>{{ item.invoice_number }}</td>
																				<td>{{ item.invoice_amount }}</td>
																				<td>{{ item.invoice_date | date }}</td>
																				<td>
																					<v-btn
																						text
																						small
																						color="primary"
																						class="text-capitalize"
																						style="text-decoration: underline"
																						@click="orderPdfLoop('factory-invoices', item.id)"
																					>
																						CI PDF
																					</v-btn>
																				</td>
																				<!-- <td>
																					<v-btn
																						text
																						small
																						color="primary"
																						class="text-capitalize"
																						style="text-decoration: underline"
																						@click="confirmationForCustomerDialog = true"
																					>
																						Attachment
																					</v-btn>
																				</td> -->

																				<td style="width: 20px">
																					<div class="d-flex">
																						<v-btn
																							icon
																							@click="
																								waitingFactoryInvoiceModal('edit', item.id)
																							"
																						>
																							<v-icon color="primary" small
																								>mdi-pencil</v-icon
																							>
																						</v-btn>

																						<v-btn
																							icon
																							@click="waitingFactoryInvoiceDelete(item.id)"
																						>
																							<v-icon color="primary" small
																								>mdi-delete</v-icon
																							>
																						</v-btn>
																					</div>
																				</td>
																			</tr>
																		</template>
																		<template v-else>
																			<tr>
																				<td colspan="11">No data Available</td>
																			</tr>
																		</template>
																	</tbody>
																</v-simple-table>
															</v-col>
														</v-row>
													</v-card-text>

													<v-dialog v-model="waitingFactoryInvoiceDialog" absolute width="800px">
														<v-card>
															<v-card-title>
																Factory Invoice
																<v-spacer></v-spacer>
																<v-btn icon @click="waitingFactoryInvoiceDialog = false">
																	<v-icon>mdi-close</v-icon>
																</v-btn>
															</v-card-title>
															<v-card-text>
																<v-row class="mt-3">
																	<v-col md="4">
																		<field-text-field
																			v-model="factoryInvoices.invoice_number"
																			outlined
																			name="invoice_number"
																			label="Invoice Number"
																			hide-details
																		></field-text-field>
																	</v-col>
																	<v-col md="4">
																		<field-text-field
																			v-model="factoryInvoices.invoice_amount"
																			outlined
																			name="invoice_amount"
																			label="Invoice Amount"
																			hide-details
																		></field-text-field>
																	</v-col>

																	<v-col md="4">
																		<field-date
																			v-model="factoryInvoices.invoice_date"
																			outlined
																			hide-details
																			name="invoice_date"
																			label="Invoice Date"
																		></field-date>
																	</v-col>

																	<v-col
																		v-if="quoteOrderData.quote && quoteOrderData.quote.products"
																		md="12"
																		cols="12"
																		class="mt-4"
																	>
																		<!-- :key="randomKey()" -->
																		<vc-autocomplete
																			:key="generateRandomKey"
																			v-model="factoryInvoices.invoice_products"
																			outlined
																			multiple
																			name="products"
																			item-text="name"
																			item-value="id"
																			return-object
																			:api="`/v1/lookups/order-products/${quoteOrderData.id}?invoice_id=${invoice_id}`"
																			label="Products"
																			hide-details
																		>
																			<template #item="{ item }">
																				<v-list-item-content>
																					<v-list-item-title>
																						<span> {{ item.name }} / </span>
																						<span class="mx-1 body-2">
																							<small>Quantity:</small> {{ item.quantity }}
																							<small class="ms-2">Remaining:</small>
																							{{ item.invoice_remaining }}
																						</span>
																					</v-list-item-title>
																				</v-list-item-content>
																			</template>
																			<template #selection="{ item }">
																				<div class="d-flex d-block" style="width: 100%">
																					<v-list-item-content>
																						<v-list-item-title>
																							<span class="me-3">{{ item.name }}</span>
																							<small class="font-weight-bold ms-2">
																								Original: {{ item.quantity }}
																							</small>
																							<small class="font-weight-bold ms-2">
																								Remaining: {{ item.invoice_remaining }}
																							</small>
																						</v-list-item-title>
																					</v-list-item-content>
																					<v-list-item-action>
																						<v-btn
																							text
																							small
																							color="primary"
																							@click="
																								addQuantity(
																									item.id,
																									'invoice',
																									factoryInvoices.id
																								)
																							"
																						>
																							Invoice Quantity:
																							<span class="font-weight-bold">
																								{{ item.invoice_quantity }}
																							</span>
																						</v-btn>
																					</v-list-item-action>
																				</div>
																			</template>
																		</vc-autocomplete>
																	</v-col>
																</v-row>
															</v-card-text>
															<v-card-actions class="d-flex">
																<v-spacer></v-spacer>
																<v-btn color="success" @click="waitingFactoryInvoiceApi()"> Save </v-btn>
															</v-card-actions>
														</v-card>
													</v-dialog>

													<v-dialog
														v-model="waitingFactoryInvoiceQuantityDialog"
														absolute
														width="600px"
														style="z-index: 9999; min-height: 300px"
													>
														<v-card>
															<v-card-title>
																Quantity
																<v-spacer></v-spacer>
																<v-btn icon @click="waitingFactoryInvoiceQuantityDialog = false">
																	<v-icon>mdi-close</v-icon>
																</v-btn>
															</v-card-title>
															<v-card-text>
																<v-row class="mt-3">
																	<v-col md="12">
																		<field-text-field
																			v-model="quantityXfield"
																			outlined
																			name="quantity"
																			label="Quantity"
																			hide-details
																		></field-text-field>
																	</v-col>
																</v-row>
															</v-card-text>
															<v-card-actions class="d-flex">
																<v-spacer></v-spacer>
																<v-btn color="success" @click="addQuantityMethod()"> Save </v-btn>
															</v-card-actions>
														</v-card>
													</v-dialog>

													<div class="d-flex mb-5">
														<v-btn text @click="orderStep = 3"> Back </v-btn>
														<v-spacer />
														<v-btn
															color="primary"
															@click="updateQuoteOrder('waiting_for_factory_invoice', 'next')"
														>
															Next
														</v-btn>
													</div>
												</v-card>
											</v-stepper-content>

											<v-stepper-step :complete="orderStep > 5" step="5"> Under Fabrication </v-stepper-step>
											<v-stepper-content step="5">
												<v-card flat class="mb-12">
													<v-card-text>
														<v-row>
															<v-col>
																<div class="d-flex mb-3">
																	<v-spacer></v-spacer>
																	<v-btn
																		outlined
																		color="black"
																		small
																		@click="factoryFabricationsModal('create')"
																	>
																		<v-icon left>mdi-plus</v-icon>
																		Add New
																	</v-btn>
																</div>
																<v-simple-table class="mb-16">
																	<thead style="background: #232323">
																		<tr>
																			<th class="text-left" style="color: #fff">#</th>
																			<th class="text-left" style="color: #fff">Invoice Number</th>
																			<th class="text-left" style="color: #fff">Invoice Date</th>
																			<th class="text-left" style="color: #fff">Weight</th>
																			<th class="text-left" style="color: #fff">Dimensions</th>
																			<th class="text-left" style="color: #fff">Hs Code</th>
																			<th class="text-left" style="color: #fff">Signature</th>
																			<th class="text-left" style="color: #fff">LC Number</th>
																			<th class="text-left" style="color: #fff">Attachment</th>
																			<th class="text-left" style="color: #fff"></th>
																		</tr>
																	</thead>
																	<tbody>
																		<template
																			v-if="
																				factoryFabricationsArrayData &&
																				factoryFabricationsArrayData.length
																			"
																		>
																			<tr
																				v-for="(item, i) in factoryFabricationsArrayData"
																				:key="'fabrications' + i"
																			>
																				<td>{{ 1 + i }}</td>

																				<td>{{ item.invoice_number }}</td>
																				<td>{{ item.invoice_date | date }}</td>
																				<td>{{ item.weight }}</td>
																				<td>{{ item.dimensions }}</td>
																				<td>{{ item.hs_code }}</td>
																				<td>
																					<v-icon
																						class="hidden-on-copy"
																						:color="
																							item.signature_and_stamp ? 'success' : 'red'
																						"
																					>
																						{{
																							item.signature_and_stamp
																								? "mdi-check"
																								: "mdi-close"
																						}}
																					</v-icon>
																				</td>
																				<td>{{ item.lc_number }}</td>
																				<td>
																					<v-btn
																						text
																						small
																						color="primary"
																						class="text-capitalize"
																						style="text-decoration: underline"
																						@click="
																							orderPdfLoop('factory-fabrications', item.id)
																						"
																					>
																						AKN PDF
																					</v-btn>
																				</td>

																				<td style="width: 20px">
																					<div class="d-flex">
																						<v-btn
																							icon
																							@click="
																								factoryFabricationsModal(
																									'edit',
																									item.id,
																									'fabrication'
																								)
																							"
																						>
																							<v-icon color="primary" small
																								>mdi-pencil</v-icon
																							>
																						</v-btn>

																						<v-btn
																							icon
																							@click="factoryFabricationsDelete(item.id)"
																						>
																							<v-icon color="primary" small
																								>mdi-delete</v-icon
																							>
																						</v-btn>
																					</div>
																				</td>
																			</tr>
																		</template>
																		<template v-else>
																			<tr>
																				<td colspan="11">No data Available</td>
																			</tr>
																		</template>
																	</tbody>
																</v-simple-table>
															</v-col>
														</v-row>
													</v-card-text>
												</v-card>

												<v-dialog v-model="factoryFabricationsDialog" absolute width="800px">
													<v-card>
														<v-card-title>
															Factory Fabrications
															<v-spacer></v-spacer>
															<v-btn icon @click="factoryFabricationsDialog = false">
																<v-icon>mdi-close</v-icon>
															</v-btn>
														</v-card-title>
														<v-card-text>
															<v-radio-group v-model="factoryFabrications.shipment_type" mandatory row>
																<v-radio label="Regular Shipment" :value="1"></v-radio>
																<v-radio label="LC Shipment" :value="2"></v-radio>
															</v-radio-group>

															<template v-if="factoryFabrications.shipment_type == '1'">
																<v-row class="mb-10 mt-3">
																	<v-col md="6">
																		<field-text-field
																			v-model="factoryFabrications.invoice_number"
																			outlined
																			name="invoice_number"
																			label="Invoice Number"
																			hide-details
																		></field-text-field>
																	</v-col>

																	<v-col md="6">
																		<field-date
																			v-model="factoryFabrications.invoice_date"
																			outlined
																			hide-details
																			name="invoice_date"
																			label="Invoice Date"
																		></field-date>
																	</v-col>

																	<v-col md="12">
																		<vc-select
																			v-model="factoryFabrications.delivery_method_id"
																			api="/v1/lookups/delivery-methods"
																			name="delivery_method_id"
																			label="Shipment Mode"
																			outlined
																			hide-details
																		></vc-select>
																	</v-col>

																	<v-col md="12">
																		<field-textarea
																			v-model="factoryFabrications.shipment_terms"
																			outlined
																			name="shipment_terms"
																			label="Shipment Terms"
																			hide-details
																		></field-textarea>
																	</v-col>

																	<v-col md="6">
																		<field-text-field
																			v-model="factoryFabrications.weight"
																			outlined
																			name="weight"
																			label="weight"
																			hide-details
																		></field-text-field>
																	</v-col>

																	<v-col md="6">
																		<field-text-field
																			v-model="factoryFabrications.gross_weight"
																			outlined
																			name="gross_weight"
																			label="Gross Weight"
																			hide-details
																		></field-text-field>
																	</v-col>

																	<v-col md="6">
																		<field-text-field
																			v-model="factoryFabrications.dimensions"
																			outlined
																			name="dimensions"
																			label="Dimensions"
																			hide-details
																		></field-text-field>
																	</v-col>

																	<v-col md="6">
																		<field-text-field
																			v-model="factoryFabrications.hs_code"
																			outlined
																			name="hs_code"
																			label="HScode"
																			hide-details
																		></field-text-field>
																	</v-col>

																	<v-col md="12">
																		<vc-select
																			v-model="factoryFabrications.made_in_country_id"
																			name="made_in"
																			api="/v1/lookups/countries"
																			label="Made in"
																			outlined
																			hide-details
																		></vc-select>
																	</v-col>

																	<v-col md="12">
																		<v-checkbox
																			v-model="factoryFabrications.signature_and_stamp"
																			label="Signature and Stamp"
																			:rules="[$rules.required('Signature')]"
																			hide-details
																		></v-checkbox>
																	</v-col>
																</v-row>
															</template>

															<template v-if="factoryFabrications.shipment_type == '2'">
																<v-row class="mt-3">
																	<v-col md="6">
																		<field-text-field
																			v-model="factoryFabrications.lc_number"
																			outlined
																			name="lc_number"
																			label="LC Number"
																			hide-details
																		></field-text-field>
																	</v-col>

																	<v-col md="6">
																		<field-date
																			v-model="factoryFabrications.lc_date_of_insurance"
																			outlined
																			hide-details
																			name="lc_date_of_insurance"
																			label="LC Date of insurance"
																		></field-date>
																	</v-col>

																	<v-col md="6">
																		<field-text-field
																			v-model="factoryFabrications.commodity_as_stated_by_the_bank"
																			outlined
																			name="commodity_as_stated_by_the_bank"
																			label="Commodity as stated by the bank"
																			hide-details
																		></field-text-field>
																	</v-col>

																	<v-col md="6">
																		<field-text-field
																			v-model="factoryFabrications.invoice_number"
																			outlined
																			name="invoice_number"
																			label="Invoice Number"
																			hide-details
																		></field-text-field>
																	</v-col>

																	<v-col md="6">
																		<field-date
																			v-model="factoryFabrications.invoice_date"
																			outlined
																			hide-details
																			name="invoice_date"
																			label="Invoice Date"
																		></field-date>
																	</v-col>

																	<v-col md="6">
																		<field-text-field
																			v-model="factoryFabrications.beneficiary_through"
																			outlined
																			name="beneficiary_through"
																			label="Beneficiary through"
																			hide-details
																		></field-text-field>
																	</v-col>

																	<v-col md="6">
																		<field-text-field
																			v-model="factoryFabrications.manufacturer_name"
																			outlined
																			name="manufacturer_name"
																			label="Manufacturer name"
																			hide-details
																		></field-text-field>
																	</v-col>

																	<v-col md="6">
																		<field-text-field
																			v-model="manufacturer_address"
																			outlined
																			name="manufacturer_address"
																			label="Manufacturer address"
																			hide-details
																		></field-text-field>
																	</v-col>

																	<v-col md="12">
																		<field-wysiwyg
																			v-model="factoryFabrications.additional_details"
																			name="notes"
																			hide-details
																			label="Additional details"
																		></field-wysiwyg>
																	</v-col>

																	<v-col md="6">
																		<field-text-field
																			v-model="factoryFabrications.dimensions"
																			outlined
																			name="dimensions"
																			label="Dimensions"
																			hide-details
																		></field-text-field>
																	</v-col>

																	<v-col md="6">
																		<vc-select
																			v-model="factoryFabrications.delivery_method_id"
																			api="/v1/lookups/delivery-methods"
																			name="delivery_method_id"
																			label="Shipment Mode"
																			outlined
																			hide-details
																		></vc-select>
																	</v-col>

																	<v-col md="12">
																		<field-textarea
																			v-model="factoryFabrications.shipment_terms"
																			outlined
																			name="shipment_terms"
																			label="Shipment Terms"
																			hide-details
																		></field-textarea>
																	</v-col>

																	<v-col md="6">
																		<field-text-field
																			v-model="factoryFabrications.weight"
																			outlined
																			name="net_weight"
																			label="Net weight"
																			hide-details
																		></field-text-field>
																	</v-col>

																	<v-col md="6">
																		<field-text-field
																			v-model="factoryFabrications.gross_weight"
																			outlined
																			name="gross_weight"
																			label="Gross weight"
																			hide-details
																		></field-text-field>
																	</v-col>

																	<v-col md="6">
																		<field-text-field
																			v-model="factoryFabrications.hs_code"
																			outlined
																			name="hs_code"
																			label="HScode"
																			hide-details
																		></field-text-field>
																	</v-col>

																	<v-col md="6">
																		<vc-select
																			v-model="factoryFabrications.made_in_country_id"
																			name="made_in_country_id"
																			api="/v1/lookups/countries"
																			label="Made in"
																			outlined
																			hide-details
																			:rules="[$rules.required('Made in')]"
																		></vc-select>
																	</v-col>

																	<v-col md="12">
																		<v-checkbox
																			v-model="factoryFabrications.signature_and_stamp"
																			label="Undetachable Stamp"
																			hide-details
																			:rules="[$rules.required('undetachable_stamp')]"
																		></v-checkbox>
																	</v-col>
																</v-row>
															</template>

															<v-col
																v-if="quoteOrderData.quote && quoteOrderData.quote.products"
																md="12"
																cols="12"
																class="mt-4"
															>
																<!-- :key="randomKey()" -->
																<vc-autocomplete
																	:key="generateRandomKey"
																	v-model="factoryFabrications.fabrication_products"
																	outlined
																	multiple
																	name="products"
																	item-text="name"
																	item-value="id"
																	return-object
																	:api="`/v1/lookups/order-products/${quoteOrderData.id}?fabrication_id=${fabrication_id}`"
																	label="Products"
																	hide-details
																>
																	<template #item="{ item }">
																		<v-list-item-content>
																			<v-list-item-title>
																				<span> {{ item.name }} / </span>
																				<span class="mx-1 body-2">
																					<small>Quantity:</small> {{ item.quantity }}
																					<small class="ms-2">Remaining:</small>
																					{{ item.fabrication_remaining }}
																				</span>
																			</v-list-item-title>
																		</v-list-item-content>
																	</template>
																	<template #selection="{ item }">
																		<div class="d-flex d-block" style="width: 100%">
																			<v-list-item-content>
																				<v-list-item-title>
																					<span class="me-3">{{ item.name }}</span>
																					<small class="font-weight-bold ms-2">
																						Original: {{ item.quantity }}
																					</small>
																					<small class="font-weight-bold ms-2">
																						Remaining: {{ item.fabrication_remaining }}
																					</small>
																				</v-list-item-title>
																			</v-list-item-content>
																			<v-list-item-action>
																				<v-btn
																					text
																					small
																					color="primary"
																					@click="
																						addQuantity(
																							item.id,
																							'fabrication',
																							factoryFabrications.id
																						)
																					"
																				>
																					Fabrication Quantity:
																					<span class="font-weight-bold">
																						{{ item.fabrication_quantity }}
																					</span>
																				</v-btn>
																			</v-list-item-action>
																		</div>
																	</template>
																</vc-autocomplete>
															</v-col>
														</v-card-text>
														<v-card-actions class="d-flex">
															<v-spacer></v-spacer>
															<v-btn color="success" @click="factoryFabricationsApi()"> Save </v-btn>
														</v-card-actions>
													</v-card>
												</v-dialog>

												<div class="d-flex mb-5">
													<v-btn text @click="orderStep = 4"> Back </v-btn>

													<!-- <v-btn color="success" @click="updateQuoteOrder('under_fabrication')">
														Generate Invoice & PT
													</v-btn>

													<v-btn
														dark
														:disabled="!quoteOrderData.can_generate_under_fabrication_documents"
														@click="poDialog = true"
													>
														<v-icon left>mdi-eye</v-icon> Invoice
													</v-btn>
													<v-btn
														dark
														:disabled="!quoteOrderData.can_generate_under_fabrication_documents"
														@click="poDialog = true"
													>
														<v-icon left>mdi-eye</v-icon> PT
													</v-btn> -->

													<v-spacer />
													<v-btn color="primary" @click="updateQuoteOrder('under_fabrication', 'next')">
														Next
													</v-btn>
												</div>
											</v-stepper-content>

											<v-stepper-step :complete="orderStep > 6" step="6"> Waiting For Shipping </v-stepper-step>
											<v-stepper-content step="6">
												<v-row>
													<v-col>
														<div class="d-flex mb-3">
															<v-spacer></v-spacer>
															<v-btn outlined color="black" small @click="orderShipmentsGetByIdApi('create')">
																<v-icon left>mdi-plus</v-icon>
																Add New Data
															</v-btn>
														</div>
														<v-simple-table class="mb-16">
															<thead style="background: #232323">
																<tr>
																	<th class="text-left" style="color: #fff">#</th>
																	<th class="text-left" style="color: #fff">Freight Number</th>
																	<th class="text-left" style="color: #fff">Freight Amount</th>

																	<th class="text-left" style="color: #fff">Insurance Number</th>
																	<th class="text-left" style="color: #fff">Insurance Amount</th>

																	<th class="text-left" style="color: #fff">Legalization Number</th>
																	<th class="text-left" style="color: #fff">Legalization Amount</th>

																	<th class="text-left" style="color: #fff">Attachment</th>
																	<th></th>
																</tr>
															</thead>
															<tbody>
																<template v-if="orderShipmentsData && orderShipmentsData.length">
																	<tr v-for="(item, i) in orderShipmentsData" :key="'Shipment' + i">
																		<td>{{ 1 + i }}</td>
																		<td>{{ item.freight_invoice_number }}</td>
																		<td>{{ item.freight_amount }}</td>

																		<td>{{ item.insurance_invoice_number }}</td>
																		<td>{{ item.insurance_amount }}</td>

																		<td>{{ item.document_legalization_invoice_number }}</td>
																		<td>{{ item.document_legalization_amount }}</td>

																		<td>
																			<v-btn
																				text
																				small
																				color="primary"
																				class="text-capitalize"
																				style="text-decoration: underline"
																				@click="orderPdfLoop('shipments', item.id)"
																			>
																				AKN PDF
																			</v-btn>
																		</td>

																		<td style="width: 20px">
																			<div class="d-flex">
																				<v-btn
																					icon
																					@click="orderShipmentsGetByIdApi('edit', item.id)"
																				>
																					<v-icon color="primary" small>mdi-pencil</v-icon>
																				</v-btn>
																				<v-btn icon @click="deleteOrderShipments(item.id)">
																					<v-icon color="primary" small>mdi-delete</v-icon>
																				</v-btn>
																			</div>
																		</td>
																	</tr>
																</template>
																<template v-else>
																	<tr>
																		<td colspan="11">No data Available</td>
																	</tr>
																</template>
															</tbody>
														</v-simple-table>
													</v-col>
												</v-row>

												<div class="d-flex mb-5">
													<v-btn text @click="orderStep = 5"> Back </v-btn>
													<v-spacer />
													<v-btn color="dark" @click="orderStep = 3">
														<v-icon left>mdi-reload</v-icon> Back To Loop
													</v-btn>
													<v-btn color="primary" @click="updateQuoteOrder('waiting_for_shipping', 'next')">
														Next
													</v-btn>
												</div>
											</v-stepper-content>

											<v-stepper-step :complete="orderStep > 7" step="7"> Waiting For Delivery Note </v-stepper-step>
											<v-stepper-content step="7">
												<v-row>
													<v-col md="12">
														<v-checkbox
															v-model="quoteOrderData.confirm_client_signed"
															label="Confirm Customer Signed"
															:rules="[$rules.required('Confirm Customer Signed')]"
														></v-checkbox>
													</v-col>
												</v-row>

												<div class="d-flex mb-5">
													<v-btn text @click="orderStep = 6"> Back </v-btn>

													<!-- <v-btn color="success" @click="updateQuoteOrder('waiting_for_delivery_note')">
														Generate PT
													</v-btn>

													<v-btn
														dark
														:disabled="!quoteOrderData.can_generate_waiting_for_delivery_note_documents"
														@click="poDialog = true"
													>
														<v-icon left>mdi-eye</v-icon> PT
													</v-btn> -->

													<v-spacer />
													<v-btn color="primary" @click="updateQuoteOrder('waiting_for_delivery_note')">
														Save
													</v-btn>
												</div>
											</v-stepper-content>
										</v-stepper>
									</v-card-text>
								</v-card>
							</div>
							<div v-else>
								<v-alert text type="error" icon="mdi-alert-circle" class="mx-2 my-8">
									You do not have a permission
								</v-alert>
								<v-btn class="mt-8" text @click="step = 7"> Back </v-btn>
							</div>
						</v-stepper-content>
					</v-stepper-items>
				</v-stepper>
			</v-col>
			<v-col md="3" class="my-0 py-0">
				<v-card>
					<v-card-title> Comments Section </v-card-title>
					<v-card-text style="height: 100%">
						<div v-if="commentsData.length">
							<div v-for="(item, i) in commentsData" :key="'comment' + i" class="mb-2">
								<h4 class="font-weight-bold">{{ item.user?.name }}</h4>
								<v-sheet elevation="0" tile color="#ededed" class="pa-2 my-1 rounded">
									<p class="caption mb-0">
										{{ item.body }}
									</p>
								</v-sheet>
								<div class="d-flex justify-space-between py-0 mt-1">
									<div v-if="item.user?.id === $auth.user.id">
										<v-btn class="mx-0 px-0" small text @click="editComment(item.id)">Edit</v-btn>
										<v-btn class="mx-0 px-0" small text @click="deleteComment(item.id)">delete</v-btn>
									</div>
									<v-spacer />
									<span class="caption">{{ item.updated_at | date }}</span>
								</div>
							</div>
						</div>
						<v-divider class="my-4" />
						<v-textarea
							v-model="comment.body"
							:counter="500"
							:rules="[$rules.maxLength('Comments', 500)]"
							label="Do you have any comments?"
							required
							outlined
						></v-textarea>
					</v-card-text>
					<v-card-actions>
						<v-spacer></v-spacer>
						<v-btn
							v-if="isEditableComment"
							color="success"
							:disabled="!$route.params.quote_id"
							small
							@click="updateComment(commentInstantId)"
						>
							Edit
						</v-btn>
						<v-btn v-else color="success" :disabled="!$route.params.quote_id" small @click="addComment()"> Add </v-btn>
					</v-card-actions>
				</v-card>

				<v-card v-if="step === 8" class="mt-6">
					<v-card-title> Payments </v-card-title>
					<v-card-text>
						<v-tabs v-model="paymentTabs">
							<v-tab>Client</v-tab>
							<v-tab>Factory</v-tab>
						</v-tabs>

						<v-tabs-items v-model="paymentTabs">
							<v-list-item-group>
								<v-tab-item>
									<v-list v-if="clientPayments.client_payments">
										<v-list-item
											v-for="item in clientPayments.client_payments"
											:key="item.id"
											@click="swiftEditPaymentMethod(item.id, 'client')"
										>
											<v-list-item-avatar>{{ item.amount }}</v-list-item-avatar>
											<v-list-item-title class="body-2">{{ item.due_date | date }}</v-list-item-title>
											<div>
												<v-chip
													v-if="item.status == 'paid'"
													small
													color="success"
													class="white--text text-capitalize"
												>
													{{ item.status }}
												</v-chip>
												<v-chip v-else small color="orange" class="white--text text-capitalize">
													{{ item.status }}
												</v-chip>
											</div>
										</v-list-item>
									</v-list>
									<div class="d-flex mt-4">
										<v-btn text color="primary" small @click="paymenManagementMethod('client')"> More </v-btn>
										<v-spacer></v-spacer>
										<v-btn color="success" small @click="swiftAddPaymentMethods('client')"> Add </v-btn>
									</div>
								</v-tab-item>
							</v-list-item-group>

							<v-list-item-group>
								<v-tab-item>
									<v-list v-if="factoryPayments.factory_payments">
										<v-list-item
											v-for="item in factoryPayments.factory_payments"
											:key="item.id"
											@click="swiftEditPaymentMethod(item.id, 'factory')"
										>
											<v-list-item-avatar>{{ item.amount }}</v-list-item-avatar>
											<v-list-item-title class="body-2">{{ item.due_date | date }}</v-list-item-title>
											<div>
												<v-chip
													v-if="item.status == 'paid'"
													small
													color="success"
													class="white--text text-capitalize"
												>
													{{ item.status }}
												</v-chip>
												<v-chip v-else small color="orange" class="white--text text-capitalize">
													{{ item.status }}
												</v-chip>
											</div>
										</v-list-item>
									</v-list>
									<div class="d-flex mt-4">
										<v-btn text color="primary" small @click="paymenManagementMethod('factory')"> More </v-btn>
										<v-spacer></v-spacer>
										<v-btn color="success" small @click="swiftAddPaymentMethods('factory')"> Add </v-btn>
									</div>
								</v-tab-item>
							</v-list-item-group>
						</v-tabs-items>
					</v-card-text>
				</v-card>

				<v-card class="mt-6">
					<v-card-title>
						<span>Attachments</span>
						<v-spacer />
						<v-btn v-if="quoteData.quote_folder" x-small depressed class="me-1" @click="openQuoteFolder()">
							<v-icon left small>mdi-folder</v-icon>Quote Folder
						</v-btn>
						<v-btn v-if="quoteData.quote_folder" x-small icon @click="getQuoteAttachments()">
							<v-icon small>mdi-refresh</v-icon>
						</v-btn>
					</v-card-title>
					<v-card-text style="height: 100%">
						<v-card v-if="AttNewObject" flat>
							<v-list v-for="(att, key, index) in AttNewObject" :key="'parent' + index">
								<v-list-group prepend-icon="mdi-attachment">
									<template #activator>
										<v-list-item-title class="text-uppercase"> {{ key }}</v-list-item-title>
									</template>

									<v-list-item v-for="(item, i) in att" :key="i + 'child'">
										<v-list-item-content>
											<v-list-item-title class="d-flex justify-space-between align-center">
												<v-icon v-if="item.comment" v-tooltip="item.comment" size="20">
													mdi-information-variant-circle-outline
												</v-icon>
												<a class="ms-1" target="__blank" :href="item.path">
													{{ item.name.slice(0, 6) }}
												</a>
												<span class="text-caption"> {{ item.updated_at | date }}</span>
												<img
													v-if="item.attached_by"
													:src="`https://ui-avatars.com/api/?name=${item.attached_by}&background=daa301&size=23&rounded=true`"
													alt=""
												/>
												<span v-else class="text-caption"> - </span>
												<v-btn color="error" icon @click="deleteQuoteAttachment(item.id)">
													<v-icon small>mdi-delete</v-icon>
												</v-btn>
											</v-list-item-title>
										</v-list-item-content>
									</v-list-item>
								</v-list-group>
							</v-list>
						</v-card>
					</v-card-text>
					<v-card-actions>
						<v-spacer></v-spacer>
						<v-btn color="success" :disabled="!$route.params.quote_id" small @click="attachmentsDialogFunction()"> Add </v-btn>
					</v-card-actions>
				</v-card>
			</v-col>
		</v-row>

		<v-row justify="center">
			<v-dialog v-model="paymenManagementDialog" fullscreen hide-overlay transition="dialog-bottom-transition">
				<v-card>
					<v-toolbar rounded="0" dark color="dark">
						<h3 class="text-capitalize">{{ paymenManagementDialogType }} Payments</h3>
						<v-spacer></v-spacer>
						<v-btn icon dark @click="paymenManagementDialog = false">
							<v-icon>mdi-close</v-icon>
						</v-btn>
					</v-toolbar>
					<v-container class="mt-16">
						<v-row>
							<v-col cols="12" md="3">
								<v-card class="mx-auto" max-width="600">
									<v-card-title>
										<v-icon color="green lighten-3" class="mr-12" size="50">mdi-note-check </v-icon>
										<v-row align="start">
											<div class="mt-2">
												<span
													v-if="paymenManagementDialogType === 'client'"
													class="text-h4 font-weight-black"
													v-text="clientPayments.paid_client_payments_count"
												>
												</span>

												<span
													v-if="paymenManagementDialogType === 'factory'"
													class="text-h4 font-weight-black"
													v-text="factoryPayments.paid_factory_payments_count"
												>
												</span>
												<div class="text-caption grey--text">Paid payment count</div>
											</div>
										</v-row>
										<v-spacer></v-spacer>
									</v-card-title>
								</v-card>
							</v-col>

							<v-col cols="12" md="3">
								<v-card class="mx-auto" max-width="600">
									<v-card-title>
										<v-icon color="green lighten-3" class="mr-12" size="50"> mdi-currency-usd </v-icon>
										<v-row align="start">
											<div class="mt-2">
												<span
													v-if="paymenManagementDialogType === 'client'"
													class="text-h4 font-weight-black"
													v-text="
														clientPayments.paid_client_payments_amount
															? clientPayments.paid_client_payments_amount
															: '-'
													"
												></span>

												<span
													v-if="paymenManagementDialogType === 'factory'"
													class="text-h4 font-weight-black"
													v-text="
														factoryPayments.paid_factory_payments_amount
															? factoryPayments.paid_factory_payments_amount
															: '-'
													"
												></span>
												<div class="text-caption grey--text">Paid payment amount</div>
											</div>
										</v-row>

										<v-spacer></v-spacer>
									</v-card-title>
								</v-card>
							</v-col>

							<v-col cols="12" md="3">
								<v-card class="mx-auto" max-width="600">
									<v-card-title>
										<v-icon color="error lighten-3" class="mr-12" size="50"> mdi-archive-clock </v-icon>
										<v-row align="start">
											<div class="mt-2">
												<span
													v-if="paymenManagementDialogType === 'client'"
													class="text-h4 font-weight-black"
													v-text="clientPayments.remaining_client_payments_count"
												></span>

												<span
													v-if="paymenManagementDialogType === 'factory'"
													class="text-h4 font-weight-black"
													v-text="factoryPayments.remaining_factory_payments_count"
												></span>
												<div class="text-caption grey--text">Remaining payments count</div>
											</div>
										</v-row>

										<v-spacer></v-spacer>
									</v-card-title>
								</v-card>
							</v-col>

							<v-col cols="12" md="3">
								<v-card class="mx-auto" max-width="600">
									<v-card-title>
										<v-icon color="error lighten-3" class="mr-6" size="50"> mdi-currency-usd-off </v-icon>
										<v-row align="start">
											<div class="mt-2">
												<span
													v-if="paymenManagementDialogType === 'client'"
													class="text-h4 font-weight-black"
													v-text="
														clientPayments.remaining_client_payments_amount
															? clientPayments.remaining_client_payments_amount
															: '-'
													"
												></span>

												<span
													v-if="paymenManagementDialogType === 'factory'"
													class="text-h4 font-weight-black"
													v-text="
														factoryPayments.remaining_factory_payments_amount
															? factoryPayments.remaining_factory_payments_amount
															: '-'
													"
												></span>
												<div class="text-caption grey--text">Remaining payments amount</div>
											</div>
										</v-row>

										<v-spacer></v-spacer>
									</v-card-title>
								</v-card>
							</v-col>
						</v-row>

						<v-row class="mt-12">
							<v-col cols="12">
								<field-wysiwyg
									v-if="paymenManagementDialogType === 'client'"
									v-model="quoteOrderData.client_payment_details"
									name="client_payment_details"
									label="Client Payment Details"
								></field-wysiwyg>

								<field-wysiwyg
									v-if="paymenManagementDialogType === 'factory'"
									v-model="quoteOrderData.factory_payment_details"
									name="factory_payment_details"
									label="Factory Payment Details"
								></field-wysiwyg>
							</v-col>
							<v-col md="12">
								<div class="d-flex mb-3">
									<v-spacer />
									<v-btn small dark @click="PaymentsAddNewDialogOpenReset()">
										<v-icon left>mdi-cash-plus</v-icon>
										Add New Payment
									</v-btn>
								</div>
								<v-card outlined>
									<v-card-text>
										<v-simple-table class="mb-8">
											<thead style="background: #232323">
												<tr>
													<th class="text-left" style="color: #fff">#</th>
													<th class="text-left" style="color: #fff">Amount</th>
													<th class="text-left" style="color: #fff">Date</th>
													<th class="text-left" style="color: #fff">Currency</th>
													<th class="text-left" style="color: #fff">Exchange Rate</th>
													<th class="text-left" style="color: #fff">Statue</th>
													<th></th>
												</tr>
											</thead>
											<tbody v-if="paymenManagementDialogType === 'client'">
												<template v-if="clientPayments.client_payments">
													<tr v-for="(field, i) in clientPayments.client_payments" :key="i + 'client_payments'">
														<td>{{ 1 + i }}</td>
														<td>{{ field.amount }}</td>
														<td>{{ field.due_date | date }}</td>
														<td>{{ field.currency?.symbol }}</td>
														<td>{{ field.exchange_rate }}</td>
														<td>{{ field.status }}</td>

														<td style="width: 20px">
															<div class="d-flex">
																<template v-if="paymenManagementDialogType === 'client'">
																	<v-btn icon @click="PaymentsAddNewDialogHandler(field.id, 'client')">
																		<v-icon color="primary" small>mdi-pencil</v-icon>
																	</v-btn>
																	<v-btn icon @click="paymentsDeleteHandler(field.id, 'client')">
																		<v-icon color="primary" small>mdi-delete</v-icon>
																	</v-btn>
																</template>

																<template v-if="paymenManagementDialogType === 'factory'">
																	<v-btn icon @click="PaymentsAddNewDialogHandler(field.id, 'factory')">
																		<v-icon color="primary" small>mdi-pencil</v-icon>
																	</v-btn>
																	<v-btn icon @click="paymentsDeleteHandler(field.id, 'factory')">
																		<v-icon color="primary" small>mdi-delete</v-icon>
																	</v-btn>
																</template>
															</div>
														</td>
													</tr>
												</template>
												<template v-else>
													<tr>
														<td colspan="7">No data Available</td>
													</tr>
												</template>
											</tbody>

											<tbody v-if="paymenManagementDialogType === 'factory'">
												<template v-if="factoryPayments.factory_payments">
													<tr
														v-for="(field, i) in factoryPayments.factory_payments"
														:key="i + '_factoryPayments_x'"
													>
														<td>{{ 1 + i }}</td>
														<td>{{ field.amount }}</td>
														<td>{{ field.due_date | date }}</td>
														<td>{{ field.currency?.symbol }}</td>
														<td>{{ field.exchange_rate }}</td>
														<td>{{ field.status }}</td>

														<td style="width: 20px">
															<div class="d-flex">
																<v-btn icon @click="PaymentsAddNewDialogHandler(field.id, 'factory')">
																	<v-icon color="primary" small>mdi-pencil</v-icon>
																</v-btn>
																<v-btn icon @click="clientPayments.splice(i, 1)">
																	<v-icon color="primary" small>mdi-delete</v-icon>
																</v-btn>
															</div>
														</td>
													</tr>
												</template>
												<template v-else>
													<tr>
														<td colspan="7">No data Available</td>
													</tr>
												</template>
											</tbody>
										</v-simple-table>
									</v-card-text>
								</v-card>
							</v-col>
						</v-row>
					</v-container>
				</v-card>
			</v-dialog>

			<v-dialog v-model="PaymentsAddNewDialog" absolute width="800px">
				<v-card>
					<v-card-title>
						<div class="mb-3">Add New Payment</div>
						<v-spacer></v-spacer>
						<v-btn icon @click="PaymentsAddNewDialog = false">
							<v-icon>mdi-close</v-icon>
						</v-btn>
					</v-card-title>
					<v-card-text>
						<div>
							<v-row>
								<v-col md="4" cols="12">
									<field-text-field
										v-model="cfNewAddPayment.reference"
										outlined
										hide-details
										name="reference"
										label="Reference"
									></field-text-field>
								</v-col>
								<v-col md="4" cols="12">
									<field-text-field
										v-model="cfNewAddPayment.payment_method"
										outlined
										hide-details
										name="payment_method"
										label="Payment Method"
									></field-text-field>
								</v-col>
								<v-col md="4" cols="12">
									<field-text-field
										v-model="cfNewAddPayment.amount"
										outlined
										hide-details
										name="amount"
										label="Amount"
									></field-text-field>
								</v-col>

								<v-col md="4" cols="12">
									<vc-select
										v-model="cfNewAddPayment.status"
										outlined
										name="status"
										:items="['pending', 'paid']"
										label="Status"
										hide-details
										:rules="[$rules.required('Status')]"
									></vc-select>
								</v-col>
								<v-col md="4" cols="12">
									<vc-select
										v-model="cfNewAddPayment.currency_id"
										hide-details
										outlined
										name="currency_id"
										label="Currency"
										api="/v1/lookups/currencies"
									></vc-select>
								</v-col>
								<v-col md="4" cols="12">
									<field-text-field
										v-model="cfNewAddPayment.exchange_rate"
										outlined
										hide-details
										name="exchange_rate"
										label="Exchange Rate"
									></field-text-field>
								</v-col>
								<v-col md="12" cols="12">
									<!-- <field-date-time
										v-model="cfNewAddPayment.due_date"
										outlined
										hide-details
										name="date"
										label="Due Date"
									></field-date-time> -->
									<field-date
										v-model="cfNewAddPayment.due_date"
										outlined
										hide-details
										name="date"
										label="Due Date"
									></field-date>
								</v-col>
							</v-row>
						</div>
					</v-card-text>
					<v-card-actions>
						<v-spacer></v-spacer>
						<v-btn text @click="addClientPaymentFunction(cfNewAddPayment.id, paymenManagementDialogType)">Save</v-btn>
					</v-card-actions>
				</v-card>
			</v-dialog>
		</v-row>

		<v-dialog v-model="priceHistoryModel" absolute width="1200px">
			<v-card style="min-height: 400px">
				<v-card-title>
					<div class="mb-3">
						Client Price History -
						<span class="body-1">{{ selectedProductHistory?.part_number }} - {{ selectedProductHistory?.name }}</span>
					</div>
					<v-spacer></v-spacer>
					<v-btn icon @click="priceHistoryModel = false">
						<v-icon>mdi-close</v-icon>
					</v-btn>
				</v-card-title>
				<v-card-text>
					<v-skeleton-loader :loading="!clientHistoryLoader" type="paragraph@3">
						<div class="d-flex mb-3">
							<v-spacer />
							<v-text-field
								v-model="searchPriceHistoryTable"
								append-icon="mdi-magnify"
								label="Search"
								single-line
								hide-details
								clearable
								class="hidden-on-copy"
								style="max-width: 300px"
							></v-text-field>
						</div>
						<v-data-table
							id="priceHistory"
							ref="priceHistory"
							class="custom-table-header"
							:headers="clientPriceHistory"
							:items="priceHistoryData"
							:search="searchPriceHistoryTable"
						>
							<template #item.client_contact_person="{ item }">
								{{ item.client?.name }}
							</template>

							<template #item.id="{ index }">
								{{ ++index }}
							</template>

							<template #item.quote_reference_number="{ item }">
								<v-btn text target="__blank" @click="goToQuote(item)">{{ item.quote_reference_number }}</v-btn>
							</template>

							<template #item.factory_price="{ item }">
								<a v-if="item.factory_quote_attachment" target="__blank" :href="item.factory_quote_attachment">
									{{ item.factory_price }} <span class="ms-1">{{ quoteData.factory?.currency?.symbol }}</span>
								</a>
								<span v-else> {{ item.factory_price }}</span>
							</template>

							<template #item.markup_price="{ item }">
								<span v-if="item.markup_type === 'percentage'">%</span>
								<span v-else-if="markup_type === 'fixed'">$</span>

								<span v-if="item.markup_price">{{ item.markup_price }}</span>
								<span v-else>-</span>
							</template>

							<template #item.status="{ item }">
								<v-chip v-if="item.status === 0" small> Draft </v-chip>
								<v-chip v-else-if="item.status === 1" small outlined color="orange"> Under Review </v-chip>
								<v-chip v-else-if="item.status === 2" small outlined color="orange"> Under Approval </v-chip>
								<v-chip v-else-if="item.status === 3" small outlined color="green">Approved </v-chip>
								<v-chip v-else-if="item.status === 4" small outlined color="purple">Tender </v-chip>
								<v-chip v-else-if="item.status === 5" small outlined color="blue">Achieved </v-chip>
								<v-chip v-else-if="item.status === 6" small outlined color="red">Lost </v-chip>
								<v-chip v-else-if="item.status === 7" small outlined color="red">Dead </v-chip>
								<v-chip v-else-if="item.status === 8" small outlined color="orange"> Pre Qualification </v-chip>
								<v-chip v-else-if="item.status === 9" small outlined color="orange"> Material Submittal </v-chip>
								<v-chip v-else-if="item.status === 10" small outlined color="orange"> Sample Request </v-chip>
								<v-chip v-else-if="item.status === 11" small outlined color="green"> Expected Order </v-chip>
							</template>

							<template #item.quote_created_at="{ item }">
								{{ item.quote_created_at | date }}
							</template>
						</v-data-table>
					</v-skeleton-loader>
				</v-card-text>
			</v-card>
		</v-dialog>

		<v-dialog v-model="attachmentsDialog" absolute width="900px">
			<v-card style="min-height: 400px">
				<v-card-title>
					<div class="mb-3">Add New Attachment</div>
					<v-spacer></v-spacer>
					<v-btn icon @click="attachmentsDialog = false">
						<v-icon>mdi-close</v-icon>
					</v-btn>
				</v-card-title>
				<v-card-text>
					<v-form ref="attachmentForm">
						<v-row v-for="(field, i) in attachmentData.attachments" :key="i + '_2attachments'" class="align-center">
							<v-col md="2" cols="12">
								<field-upload
									:key="i + 'path'"
									v-model="field.path"
									multiple
									label="Attachment"
									:rules="[$rules.required('path')]"
									outlined
									hide-details
								></field-upload>
							</v-col>
							<v-col md="4" cols="12">
								<field-text-field
									:key="i + 'name'"
									v-model="field.name"
									outlined
									:name="'name-' + i"
									label="Attachment Name"
									class="pt-1"
									:rules="[$rules.required('name')]"
									hide-details
								></field-text-field>
							</v-col>
							<v-col md="4" cols="12">
								<vc-autocomplete
									:key="i + 'type'"
									v-model="field.type"
									outlined
									name="type"
									item-text="text"
									item-value="value"
									:items="attachmentTypes"
									label="Attachment Type"
									:rules="[$rules.required('Attachment Type')]"
									hide-details
								></vc-autocomplete>
							</v-col>
							<v-col md="1" cols="12">
								<v-btn icon class="float-end ms-1" color="primary" @click="attachmentData.attachments.splice(i, 1)">
									<v-icon>mdi-trash-can</v-icon>
								</v-btn>
							</v-col>
							<v-col md="12" cols="12">
								<v-textarea
									:key="i"
									v-model="field.comment"
									outlined
									hide-details
									name="comment"
									label="Comment"
								></v-textarea>
							</v-col>
							<v-col md="12" cols="12" class="my-6">
								<v-divider />
							</v-col>
						</v-row>
					</v-form>

					<v-btn
						v-tooltip="'add new'"
						class="success mt-3"
						small
						dence
						@click="
							attachmentData.attachments.push({
								path: null,
								name: null,
								type: null,
								comment: null,
							})
						"
					>
						<v-icon left>mdi-plus</v-icon>
						Add New
					</v-btn>
				</v-card-text>
				<v-card-actions>
					<v-spacer></v-spacer>
					<v-btn :loading="isLoading" outlined @click="addAttachmentsMethod()">Save</v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>

		<v-dialog v-model="productActivitiesModal" absolute width="900px">
			<v-card style="min-height: 400px">
				<v-card-title>
					Product Activities
					<v-spacer></v-spacer>
					<v-btn icon @click="productActivitiesModal = false">
						<v-icon>mdi-close</v-icon>
					</v-btn>
				</v-card-title>
				<v-card-text>
					<v-card flat>
						<v-card-text>
							<template v-if="productActivitesData && productActivitesData.length">
								<v-card v-for="(item, i) in productActivitesData" :key="i + '__activitesData'" class="mb-4">
									<v-card-title class="d-flex">
										<h6>
											Edit By:<span class="font-weight-bold ms-1">{{ item.user?.name }}</span>
										</h6>
										<v-spacer></v-spacer>
										<h6 class="me-2">{{ item.updated_at }}</h6>
									</v-card-title>
									<v-card-text class="d-flex">
										<div v-for="(old, j) in item.properties.old" :key="j + '__old'" style="width: 50%" class="me-2">
											<field-text-field
												background-color="#ffe2e2"
												class="me-1"
												outlined
												hide-details
												:value="old.value ? old.value : 'Null'"
												:label="old.key"
											></field-text-field>
										</div>
										<div v-for="(newVal, n) in item.properties.attributes" :key="n + '__new-2'" style="width: 50%">
											<field-text-field
												class="me-1"
												background-color="#4caf5036"
												outlined
												hide-details
												:value="newVal.value ? newVal.value : 'Null'"
												:label="newVal.key"
											></field-text-field>
										</div>
									</v-card-text>
								</v-card>
							</template>
							<template v-else>
								<p class="text-center mt-6">There is no data available!</p>
							</template>
						</v-card-text>
					</v-card>
				</v-card-text>
			</v-card>
		</v-dialog>

		<v-dialog v-model="waitingForShippingDialog" absolute width="1100px">
			<v-card style="min-height: 400px">
				<v-card-title>
					Waiting Shipping Data
					<v-spacer></v-spacer>
					<v-btn icon @click="waitingForShippingDialog = false">
						<v-icon>mdi-close</v-icon>
					</v-btn>
				</v-card-title>
				<v-card-text>
					<v-row class="mb-4 mt-6">
						<v-col md="3">
							<vc-select
								v-model="orderShipmentsDataById.freight_forwarder_id"
								name="freight_forwarder"
								api="/v1/lookups/freight-forwarders"
								label="Freight Forwarder"
								hide-details
								outlined
							></vc-select>
						</v-col>
						<v-col md="3">
							<field-text-field
								v-model="orderShipmentsDataById.freight_invoice_number"
								outlined
								name="freight_invoice_number"
								label="Freight Invoice Number"
								hide-details
							></field-text-field>
						</v-col>
						<v-col md="2">
							<field-text-field
								v-model="orderShipmentsDataById.freight_amount"
								outlined
								name="freight_amount"
								label="Freight Amount"
								hide-details
							></field-text-field>
						</v-col>
						<v-col md="2">
							<field-date
								v-model="orderShipmentsDataById.freight_date"
								outlined
								hide-details
								name="freight_date"
								label="Freight Date"
							></field-date>
						</v-col>
						<v-col md="2">
							<vc-autocomplete
								key="freight_currency_id"
								v-model="quoteData.freight_currency_id"
								hide-details
								outlined
								name="freight_currency_id"
								label="Freight Currency"
								api="/v1/lookups/currencies"
							></vc-autocomplete>
						</v-col>
					</v-row>

					<v-row class="mb-4">
						<v-col md="3">
							<vc-autocomplete
								v-model="orderShipmentsDataById.insurance_company_id"
								name="insurance_company_id"
								api="/v1/lookups/insurance-companies"
								label="Insurance Company"
								hide-details
								outlined
							></vc-autocomplete>
						</v-col>
						<v-col md="3">
							<field-text-field
								v-model="orderShipmentsDataById.insurance_invoice_number"
								outlined
								name="insurance_invoice_number"
								label="Insurance Invoice Number"
								hide-details
							></field-text-field>
						</v-col>
						<v-col md="2">
							<field-text-field
								v-model="orderShipmentsDataById.insurance_amount"
								outlined
								name="insurance_amount"
								label="Insurance Amount"
								hide-details
							></field-text-field>
						</v-col>
						<v-col md="2">
							<field-date
								v-model="orderShipmentsDataById.insurance_date"
								outlined
								hide-details
								name="insurance_date"
								label="Insurance Date"
							></field-date>
						</v-col>
						<v-col md="2">
							<vc-autocomplete
								key="freight_currency_id"
								v-model="quoteData.insurance_currency_id"
								hide-details
								outlined
								name="insurance_currency_id"
								label="Insurance Currency"
								api="/v1/lookups/currencies"
							></vc-autocomplete>
						</v-col>
					</v-row>

					<v-row class="mb-4">
						<v-col md="3">
							<vc-autocomplete
								v-model="orderShipmentsDataById.legalization_company_id"
								name="legalization_company_id"
								api="/v1/lookups/legalization-companies"
								label="Legalization Company"
								hide-details
								outlined
							></vc-autocomplete>
						</v-col>
						<v-col md="3">
							<field-text-field
								v-model="orderShipmentsDataById.document_legalization_invoice_number"
								outlined
								name="document_legalization_invoice_number"
								label="Legalization Invoice Number"
								hide-details
							></field-text-field>
						</v-col>
						<v-col md="2">
							<field-text-field
								v-model="orderShipmentsDataById.document_legalization_amount"
								outlined
								name="document_legalization_amount"
								label="Legalization Amount"
								hide-details
							></field-text-field>
						</v-col>
						<v-col md="2">
							<field-date
								v-model="orderShipmentsDataById.document_legalization_date"
								outlined
								hide-details
								name="document_legalization_date"
								label="Legalization Date"
							></field-date>
						</v-col>
						<v-col md="2">
							<vc-autocomplete
								key="freight_currency_id"
								v-model="quoteData.document_legalization_currency_id"
								hide-details
								outlined
								name="document_legalization_currency_id"
								label="Legalization Currency"
								api="/v1/lookups/currencies"
							></vc-autocomplete>
						</v-col>
					</v-row>

					<v-row class="mb-4">
						<v-col md="3">
							<field-text-field
								v-model="orderShipmentsDataById.commissions"
								outlined
								name="commissions"
								label="Commissions"
								hide-details
							></field-text-field>
						</v-col>
						<v-col md="3">
							<field-date
								v-model="orderShipmentsDataById.pickup_date"
								outlined
								hide-details
								name="pickup_date"
								label="Pickup Date"
							></field-date>
						</v-col>
						<v-col md="3">
							<field-date
								v-model="orderShipmentsDataById.estimated_arrival_date"
								outlined
								hide-details
								name="estimated_arrival_date"
								label="Estimated Arrival Date"
							></field-date>
						</v-col>
						<v-col md="3">
							<field-date
								v-model="orderShipmentsDataById.actual_shipment_date"
								outlined
								hide-details
								name="actual_shipment_date"
								label="Actual Shipment Date"
							></field-date>
						</v-col>
					</v-row>
				</v-card-text>
				<v-card-actions class="d-flex">
					<v-spacer></v-spacer>
					<v-btn color="success" @click="orderShipmentsUpdateApi(shipmentId)"> Save </v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>

		<div class="general-pdfs">
			<pdfs-dialog :pdf-data="quoteOrderData" pdf-name="pi" :general-dialog.sync="piDialog" />
			<pdfs-dialog :pdf-data="quoteOrderData" pdf-name="po" :general-dialog.sync="poDialog" />
			<pdfs-dialog :pdf-data="pdfData" :extra-data="quoteOrderData" pdf-name="ci" :general-dialog.sync="ciDialog" />
			<pdfs-dialog :pdf-data="pdfData" :extra-data="quoteOrderData" pdf-name="oa" :general-dialog.sync="oaDialog" />
			<pdfs-dialog :pdf-data="quoteOrderData" pdf-name="pt" :general-dialog.sync="ptDialog" />
			<pdfs-dialog :pdf-data="quoteOrderData" pdf-name="cin" :general-dialog.sync="cinDialog" />
		</div>
	</page>
</template>

<script>
import html2pdf from "html2pdf.js";
import html2canvas from "html2canvas";
import JsPDF from "jspdf";

export default {
	kye: "steeper-page",

	data() {
		return {
			piDialog: false,
			poDialog: false,
			ciDialog: false,
			ptDialog: false,
			oaDialog: false,
			cinDialog: false,
			formalQuoteType: "standard",
			pdfData: {},
			genericPdfLoopData: {},
			attachmentData: {
				attachments: [],
			},
			productCategory: null,
			selectorFactoryId: null,
			instant_qoute_id: null,
			clientHistoryLoader: false, // using in table loader
			indexField: 0,
			quoteDialog: false,
			step: 1,
			selector: false,
			selectorType: 1,
			description: null,
			discountsType: "Tax",
			orderType: "op",
			search: "",
			searchPriceHistoryTable: "",
			deliveryPriceType: "cost",
			selectedProductHistory: null,
			selectedProduct: null,
			AttNewObject: {},
			orderStep: 1,
			paymentTabs: 0,
			selectorPanel: 0,
			createRevision: false,

			productActivitesData: [],

			commentsData: [
				{
					body: null,
					user: {
						name: null,
					},
				},
			],

			comment: {
				body: null,
			},

			calculationsBtn: true,
			priceHistoryModel: false,
			priceProductModel: false,
			priceDiscountsModel: false,
			ShowClientDialog: false,
			attachmentsDialog: false,
			steep_1_validation: true,
			steep_2_validation: false,
			factoryCurrencyModel: false,
			disable_factory_logo: false,
			quoteCurrencyModel: false,
			productActivitiesModal: false,
			paymenManagementDialog: false,
			PaymentsAddNewDialog: false,
			paymenManagementDialogType: null,
			waitingForShippingDialog: false,
			waitingFactoryInvoiceQuantityDialog: false,

			quoteData: {
				current_step: "define_project",
				attachments: [],
				products: [],
				specifications: {
					//	options: {},
				},
				from_currency: "USD",
			},

			statusItems: [
				{
					text: "Draft",
					value: 0,
				},
				{
					text: "Under Review",
					value: 1,
				},
				{
					text: "Under Approval",
					value: 2,
				},
				{
					text: "Approved",
					value: 3,
				},
				{
					text: "Tender",
					value: 4,
				},
				{
					text: "Achieved",
					value: 5,
				},
				{
					text: "On Going",
					value: 6,
				},
				{
					text: "Lost",
					value: 7,
				},
				{
					text: "Dead",
					value: 8,
				},
				{
					text: "Pre Qualification",
					value: 9,
				},
				{
					text: "Material submittal",
					value: 10,
				},
				{
					text: "Sample request",
					value: 11,
				},
				{
					text: "Expected order",
					value: 12,
				},
				{
					text: "Ordered",
					value: 13,
				},
			],

			paymentItems: [
				{
					text: "None",
					value: 0,
				},
				{
					text: "Cash",
					value: 1,
				},
				{
					text: "Down",
					value: 2,
				},
			],

			// feesTypes: ["amount", "percentage"],
			feesTypes: [
				{
					text: "$",
					value: "fixed",
				},
				{
					text: "%",
					value: "percentage",
				},
			],

			horizontalVerticalItems: [
				{
					text: "Horizontal",
					value: "horizontal",
				},
				{
					text: "Vertical",
					value: "vertical",
				},
			],

			fixedOrMovable: [
				{
					text: "Fixed",
					value: "fixed",
				},
				{
					text: "Movable",
					value: "movable",
				},
			],

			specificationsOptionsItems: [
				{
					text: "option 1",
					value: "1",
				},
				{
					text: "option 2",
					value: "2",
				},
				{
					text: "option 3",
					value: "3",
				},
			],

			paymentMethods: [],

			skinItems: [
				{
					text: "Single",
					value: "Single",
				},
				{
					text: "Double",
					value: "Double",
				},
			],

			fireFatingItems: [
				{
					text: "Fire Rated",
					value: "Fire Rated",
				},
				{
					text: "Non Fire Rated",
					value: "Non Fire Rated",
				},
			],

			instantSelectorValues: {
				specifications: {
					// options: {},
				},
				options: {},
			},

			attachmentTypes: [
				{
					text: "Drawings",
					value: "drawings",
				},
				{
					text: "Emails",
					value: "emails",
				},
				{
					text: "Factory Quote",
					value: "factory_quote",
				},
				{
					text: "Others",
					value: "others",
				},
			],

			priceHistoryData: [],

			newClientData: {
				name: null,
				number: null,
				email: null,
				country_id: null,
				contact_people: [
					{
						client_name: null,
						client_number: null,
						email_one: null,
						phone: null,
						address_one: null,
					},
				],
			},

			takeOffTable: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{ text: "Part Number", value: "part_number" },
				{ text: "Description", value: "description" },
				{ text: "Quantity", value: "quantity" },
				{ text: "Is Optional", value: "is_optional", class: "hidden-on-copy" },
				{ text: "Is Hidden", value: "is_hidden", class: "hidden-on-copy" },
				{ text: "Actions", value: "actions", class: "hidden-on-copy" },
			],

			factoryPriceTable: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{ text: "Part Number", value: "part_number" },
				{ text: "Description", value: "description" },
				{ text: "Quantity", value: "quantity" },
				{ text: "Factory Description", value: "factory_description" },
				{ text: "Factory Price", value: "factory_price" },
				{ text: "Is Optional", value: "is_optional" },
				{ text: "Actions", value: "actions" },
			],

			priceTable: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{ text: "Part Number", value: "part_number" },
				{ text: "Description", value: "description" },
				{ text: "Quantity", value: "quantity" },
				{ text: "Factory Price", value: "factory_price" },
				{ text: "Unit Price", value: "unit_price" },
				{ text: "Total Price", value: "total" },
				{ text: "Actions", value: "actions" },
			],

			clientPriceHistory: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "quote_id",
				},
				{ text: "Client Name", value: "client_contact_person" },
				{ text: "Q Reference Number", value: "quote_reference_number" },
				{ text: "Factory Price", value: "factory_price" },
				{ text: "Markup Price", value: "markup_price" },
				{ text: "Total", value: "total" },
				{ text: "Status", value: "status" },
				{ text: "Quote Date", value: "quote_created_at" },
			],

			paymentsArray: [],

			quoteOrderData: {
				shipment_type: 1,
			},
			clientPayments: {},
			factoryPayments: {},

			cfNewAddPayment: {
				due_date: null,
			},
			factoryNewAddPayment: {},

			orderShipmentsData: [],
			orderShipmentsDataById: {},
			shipmentId: null,
			shipmentApiType: "create",

			confirmationForCustomerDialog: false,
			factoryActivitiesModal: false,
			selectedProductActivities: [],
			quoteMarkupAllProductsModel: false,
			quoteMarkupBulkProductsModel: false,
			quoteMarkupProducts: {
				except: [],
			},
			theSameBillAndShip: false,

			quoteMarkupBulkProducts: [
				{
					markup_price: null,
					markup_type: null,
					products: [],
				},
			],

			// Factory Invoices CRUD
			factoryInvoicesArrayData: [],
			waitingFactoryInvoiceDialog: false,
			invoice_id: null,
			factoryInvoices: {
				invoice_number: null,
				invoice_amount: null,
				invoice_products: [
					{
						id: null,
						name: null,
						product_id: null,
						invoice_quantity: null,
					},
				],
			},
			waitingFactoryType: null,
			waitingFactoryId: null,

			// Factory Confirmations CRUD
			factoryConfirmationsArrayData: [],
			factoryConfirmationsDialog: false,
			factoryConfirmations: {
				received_from_factory: false,
				delivery_to_client_at: null,
				additional_weeks: null,
				confirmation_products: [],
			},
			factoryConfirmationType: null,
			factoryConfirmationId: null,

			// Factory Fabrications CRUD
			factoryFabricationsArrayData: [],
			factoryFabricationsDialog: false,
			factoryFabrications: {
				shipment_type: false,
				invoice_number: null,
				invoice_date: null,
				delivery_method_id: null,
				shipment_terms: null,
				weight: null,
				gross_weight: null,
				dimensions: null,
				hs_code: null,
				made_in_country_id: null,
				signature_and_stamp: null,
				lc_number: null,
				lc_date_of_insurance: null,
				commodity_as_stated_by_the_bank: null,
				beneficiary_through: null,
				manufacturer_name: null,
				manufacturer_address: null,
			},
			factoryFabricationsType: null,
			factoryFabricationsId: null,

			selectionProductId: null,
			selectionItemId: null,
			quantityXfield: null,
			quantityType: null,
			fabrication_id: null,
			confirmation_id: null,
			generateRandomKey: null,
			selectorRandomKey: null,

			isLoading: false,
			isDefaultSelector: false,
			isEditableComment: false,
			commentInstantId: null,
		};
	},

	async fetch() {
		if (this.$route.params.quote_id) {
			this.getComments();
			await this.$axios.$get(`/v1/portal/quotes/${this.$route.params.quote_id}?ids=contactPeople`).then((resp) => {
				this.quoteData = resp;
				if (resp.current_step === 8) {
					this.step = resp.current_step;
					this.getQuoteOrders();
				} else {
					this.step = resp.current_step + 1;
				}

				if (resp.current_step === 4) {
					this.setDate(resp.valid_until);
				}

				// Set Product Selector
				this.selectorComponent = "selector-cat-" + resp.product_category_id + "-fact-" + resp.factory_id; // selector-cat-1-fact-1
				this.productCategory = resp.product_category_id;
				this.selectorFactoryId = resp.factory_id;

				// selector-cat-4-fact-1

				if (
					this.selectorComponent !== "selector-cat-1-fact-1" &&
					this.selectorComponent !== "selector-cat-1-fact-4" &&
					this.selectorComponent !== "selector-cat-5-fact-9" &&
					this.selectorComponent !== "selector-cat-4-fact-1" &&
					this.selectorComponent !== "selector-cat-6-fact-3"
				) {
					this.isDefaultSelector = true;
				} else {
					this.isDefaultSelector = false;
				}

				//	console.log("this.selectorComponent=", this.selectorComponent);

				// get factory currency from quoteData obj
				if (!this.quoteData.factory_currency_id) {
					this.quoteData.factory_currency_id = this.quoteData.factory.currency.id;
				}

				// Attachments
				this.getQuoteAttachments();
			});
		}
	},

	computed: {
		selectorRandomKeyComputed() {
			return new Date().getTime();
		},
	},

	// mounted() {
	// 	console.log("This is the mounted function---------------------------------------------step", this.step);
	// 	this.fetchPaymentMethods();
	// },

	methods: {
		validationStep(stepName, NextStep) {
			//	console.log("stepName=", stepName, "	NextStep=", NextStep);
			//	console.log("validation function", stepName);
			this.$toast.clear();

			if (stepName === "define_project") {
				this.steep_1_validation = true;
			} else this.steep_1_validation = false;

			if (stepName === "take_off") {
				this.steep_2_validation = true;
			} else this.steep_2_validation = false;

			if (this.steep_1_validation && this.$refs.stepOneValidation) {
				if (!this.$refs.stepOneValidation.validate()) return;
			}

			if (this.steep_2_validation && this.$refs.stepTwoValidation) {
				if (!this.$refs.stepTwoValidation.validate()) return;
			}

			this.$store.dispatch("cache/clearKey", "/v1/lookups/countriese");

			if (stepName !== "take_off" && stepName !== "data_entry") {
				this.$axios
					.$post(`/v1/portal/quote-steps/${stepName}`, this.quoteData)
					.then(() => {
						// Post API
						this.quoteData.current_step = stepName;
						if (stepName === "pricing") {
							this.priceProductModel = false;
							this.quoteCurrencyModel = false;
						}
						this.saveData(stepName, NextStep);
						// this.$toast.success("validation has been successfully");
					})
					.catch(this.genericErrorHandler);
			} else {
				if (stepName === "take_off") {
					this.selector = false;
				} else if (stepName === "data_entry") {
					this.factoryCurrencyModel = false;
				}
				this.saveData(stepName, NextStep);
			}
		},

		backFunction(step) {
			this.$axios.$get(`/v1/portal/quotes/${this.$route.params.quote_id}?ids=contactPeople`).then((resp) => {
				this.quoteData = resp;
				this.step = step;
				if (step === 8) {
					this.getQuoteOrders();
				}
			});
		},

		saveData(stepName, NextStep) {
			this.quoteData.current_step = stepName;
			if (stepName === "define_project" && !this.$route.params.quote_id) {
				//	console.log("This is POST Api");
				this.$axios
					.$post(`/v1/portal/quotes`, this.quoteData)
					.then((resp) => {
						this.$toast.success("successfully saving data");
						this.instant_qoute_id = resp.id;
						// this.$router.push({ params: { quote_id: this.instant_qoute_id } });
						window.location.href = `quotes/${this.instant_qoute_id}`;
					})
					.catch(this.genericErrorHandler);
			} else {
				console.log("This is PUT Api");

				if (this.createRevision) {
					this.$confirm("", { title: "Are you sure to create a new revision?" }).then((isAccepted) => {
						if (isAccepted) {
							this.quoteData.should_create_revision = true;

							this.$axios
								.$put(`/v1/portal/quotes/${this.$route.params.quote_id}`, this.quoteData)
								.then((resp) => {
									this.$toast.success("successfully saving data");
									this.productCategory = resp.product_category_id;
									this.selectorFactoryId = resp.factory_id;
									this.setSelector();

									this.step = NextStep;

									// Get quote data
									this.$axios.$get(`/v1/portal/quotes/${this.$route.params.quote_id}?ids=contactPeople`).then((resp) => {
										this.quoteData = resp;
									});
								})
								.catch(this.genericErrorHandler);

							this.createRevision = false;
							this.quoteData.should_create_revision = false;
						}
					});
				} else {
					//	console.log("-------------------this.quoteData.product_category_id", this.quoteData.product_category_id);

					this.$axios
						.$put(`/v1/portal/quotes/${this.$route.params.quote_id}`, this.quoteData)
						.then((resp) => {
							this.$toast.success("successfully saving data");
							this.selector = false;
							this.productCategory = resp.product_category_id;
							this.selectorFactoryId = resp.factory_id;
							this.setSelector();

							this.step = NextStep;

							// Get quote data
							this.$axios.$get(`/v1/portal/quotes/${this.$route.params.quote_id}?ids=contactPeople`).then((resp) => {
								this.quoteData = resp;
							});
						})
						.catch(this.genericErrorHandler);
				}
			}

			if (NextStep === 8) {
				// ("update order steps");
				this.paymenManagementMethod("all");
				this.getQuoteOrders();
			}

			this.getQuoteAttachments();
		},

		getProductId(i) {
			this.$axios
				.$get(`/v1/lookups/products?sub_category_id=${this.quoteData.products[i].sub_category_id}`)
				.then((resp) => {
					if (resp && resp.length > 0) {
						this.quoteData.products[i].product_id = resp[0].value;
					} else {
						console.error("No data found in the response or data array is empty");
					}
				})
				.catch((error) => {
					console.error(error);
				});
		},

		getSelectorValues(i) {
			const refKey = "selectorsSpecifications" + i;
			const formRefs = this.$refs[refKey];
			this.$toast.clear();

			if (Array.isArray(formRefs) && formRefs.length > 0) {
				const formRef = formRefs[0];

				if (this.selectorComponent === "selector-cat-1-fact-1") {
					// Nystrom
					this.$nextTick(() => {
						this.quoteData.products.forEach((product, i) => {
							// Set non-dynamic values
							this.instantSelectorValues.quote_id = this.quoteData.id;
							this.instantSelectorValues.product_id = product.product_id;
							// this.instantSelectorValues.group = this.quoteData.products[i].group;
							// this.instantSelectorValues.part_number = this.quoteData.products[i].part_number;

							// List of general keys
							const allGeneralKeys = ["group", "part_number", "options"];
							const requiredGeneralKeys = ["group"];

							// Dynamically assign general values
							allGeneralKeys.forEach((key) => {
								this.instantSelectorValues[key] = product[key];
							});

							// List of specification all keys
							const allSpecificationsKeys = ["height", "width", "model_group", "model", "horizontal_or_vertical"];

							// List of specification required keys
							const requiredSpecificationsKeys = ["height", "width", "model_group", "model"];

							// Dynamically assign all specification values'
							allSpecificationsKeys.forEach((key) => {
								this.instantSelectorValues.specifications[key] = product.specifications[key];
							});

							// check if all required fields are filled
							const allGeneralFilled = requiredGeneralKeys.every((key) => this.instantSelectorValues[key]);

							const allSpecificationsFilled = requiredSpecificationsKeys.every(
								(key) => this.instantSelectorValues.specifications[key]
							);

							// Make the POST request if all required fields are filled
							if (allGeneralFilled && allSpecificationsFilled) {
								this.$axios
									.$post("/v1/portal/selector-values", this.instantSelectorValues)
									.then((resp) => {
										product.part_number = resp.areel_part_number;
										product.description = resp.areel_description;
										product.factory_description = resp.factory_description;
										product.factory_part_number = resp.factory_part_number;
										// this.selectorRandomKey = new Date().getTime();
										this.$toast.clear();
									})
									.catch((err) => {
										this.genericErrorHandler(err, formRef);
									});
							}
						});

						// this.instantSelectorValues.quote_id = this.quoteData.id;
						// this.instantSelectorValues.product_id = this.quoteData.products[i].product_id;

						// this.instantSelectorValues.specifications.height = this.quoteData.products[i].specifications.height;
						// this.instantSelectorValues.specifications.width = this.quoteData.products[i].specifications.width;

						// this.instantSelectorValues.specifications.horizontal_or_vertical =
						// 	this.quoteData.products[i].specifications.horizontal_or_vertical;

						// this.instantSelectorValues.group = this.quoteData.products[i].group;
						// this.instantSelectorValues.specifications.model_group = this.quoteData.products[i].specifications.model_group;
						// this.instantSelectorValues.specifications.model = this.quoteData.products[i].specifications.model;

						// if (
						// 	this.instantSelectorValues.product_id &&
						// 	this.instantSelectorValues.group &&
						// 	this.instantSelectorValues.specifications.model_group &&
						// 	this.instantSelectorValues.specifications.model &&
						// 	this.instantSelectorValues.specifications.height &&
						// 	this.instantSelectorValues.specifications.width
						// 	// this.instantSelectorValues.specifications.horizontal_or_vertical
						// ) {
						// 	this.$axios
						// 		.$post("/v1/portal/selector-values", this.instantSelectorValues)
						// 		.then((resp) => {
						// 			this.quoteData.products[i].part_number = resp.areel_part_number;
						// 			this.quoteData.products[i].description = resp.areel_description;
						// 			this.selectorRandomKey = new Date().getTime();
						// 		})
						// 		.catch((err) => {
						// 			this.genericErrorHandler(err, formRef);
						// 		});
						// }
					});
				} else if (this.selectorComponent === "selector-cat-1-fact-4") {
					// Form
					this.$nextTick(() => {
						this.quoteData.products.forEach((product, i) => {
							// Set non-dynamic values
							this.instantSelectorValues.quote_id = this.quoteData.id;
							this.instantSelectorValues.product_id = product.product_id;
							this.instantSelectorValues.part_number = this.quoteData.products[i].part_number;

							// List of specification keys to be copied
							const specificationsKeys = [
								"door_material",
								"door_material_thickness",
								"rough_opening_width",
								"rough_opening_height",
								"door_material_type",
								"door_insulation_value",
								"door_insulation_type",
								"door_surface",
								"curb_material",
								"curb_material_thickness",
								"curb_material_type",
								"curb_insulation_value",
								"curb_insulation_type",
								"curb_height",
								"flange_width",
								"gas_strut",
								"hardware",
								"finish",
								"ral_number",
							];

							// Dynamically assign specification values
							specificationsKeys.forEach((key) => {
								this.instantSelectorValues.specifications[key] = product.specifications[key];
							});

							// Check if all required fields are filled
							const allSpecificationsFilled = specificationsKeys.every(
								(key) => this.instantSelectorValues.specifications[key]
							);

							// Make the POST request if all fields are filled
							if (allSpecificationsFilled) {
								this.$axios
									.$post("/v1/portal/selector-values", this.instantSelectorValues)
									.then((resp) => {
										product.part_number = resp.areel_part_number;
										product.description = resp.areel_description;
										product.factory_description = resp.factory_description;
										product.factory_part_number = resp.factory_part_number;
										// this.selectorRandomKey = new Date().getTime();
										this.$toast.clear();
									})
									.catch((err) => {
										this.genericErrorHandler(err, formRef);
									});
							}
						});
					});

					// this.instantSelectorValues.quote_id = this.quoteData.id;
					// this.instantSelectorValues.product_id = this.quoteData.products[i].product_id;

					// this.instantSelectorValues.specifications.door_material = this.quoteData.products[i].specifications.door_material;
					// this.instantSelectorValues.specifications.door_material_thickness =
					// 	this.quoteData.products[i].specifications.door_material_thickness;
					// this.instantSelectorValues.specifications.rough_opening_width =
					// 	this.quoteData.products[i].specifications.rough_opening_width;
					// this.instantSelectorValues.specifications.rough_opening_height =
					// 	this.quoteData.products[i].specifications.rough_opening_height;
					// this.instantSelectorValues.specifications.door_material_type =
					// 	this.quoteData.products[i].specifications.door_material_type;
					// this.instantSelectorValues.specifications.door_insulation_value =
					// 	this.quoteData.products[i].specifications.door_insulation_value;
					// this.instantSelectorValues.specifications.door_insulation_type =
					// 	this.quoteData.products[i].specifications.door_insulation_type;
					// this.instantSelectorValues.specifications.curb_material = this.quoteData.products[i].specifications.curb_material;
					// this.instantSelectorValues.specifications.curb_material_thickness =
					// 	this.quoteData.products[i].specifications.curb_material_thickness;
					// this.instantSelectorValues.specifications.curb_material_type =
					// 	this.quoteData.products[i].specifications.curb_material_type;
					// this.instantSelectorValues.specifications.curb_insulation_value =
					// 	this.quoteData.products[i].specifications.curb_insulation_value;
					// this.instantSelectorValues.specifications.curb_insulation_type =
					// 	this.quoteData.products[i].specifications.curb_insulation_type;
					// this.instantSelectorValues.specifications.curb_height = this.quoteData.products[i].specifications.curb_height;
					// this.instantSelectorValues.specifications.flange_width = this.quoteData.products[i].specifications.flange_width;
					// this.instantSelectorValues.specifications.gas_strut = this.quoteData.products[i].specifications.gas_strut;
					// this.instantSelectorValues.specifications.hardware = this.quoteData.products[i].specifications.hardware;
					// this.instantSelectorValues.specifications.finish = this.quoteData.products[i].specifications.finish;

					// if (
					// 	this.instantSelectorValues.specifications.door_material &&
					// 	this.instantSelectorValues.specifications.door_material_thickness &&
					// 	this.instantSelectorValues.specifications.rough_opening_width &&
					// 	this.instantSelectorValues.specifications.rough_opening_height &&
					// 	this.instantSelectorValues.specifications.door_material_type &&
					// 	this.instantSelectorValues.specifications.door_insulation_value &&
					// 	this.instantSelectorValues.specifications.door_insulation_type &&
					// 	this.instantSelectorValues.specifications.curb_material &&
					// 	this.instantSelectorValues.specifications.curb_material_thickness &&
					// 	this.instantSelectorValues.specifications.curb_material_type &&
					// 	this.instantSelectorValues.specifications.curb_insulation_value &&
					// 	this.instantSelectorValues.specifications.curb_insulation_type &&
					// 	this.instantSelectorValues.specifications.curb_height &&
					// 	this.instantSelectorValues.specifications.flange_width &&
					// 	this.instantSelectorValues.specifications.gas_strut &&
					// 	this.instantSelectorValues.specifications.hardware &&
					// 	this.instantSelectorValues.specifications.finish
					// ) {
					// 	this.$axios
					// 		.$post("/v1/portal/selector-values", this.instantSelectorValues)
					// 		.then((resp) => {
					// 			this.quoteData.products[i].part_number = resp.areel_part_number;
					// 			this.quoteData.products[i].description = resp.areel_description;
					// 			this.quoteData.products[i].factory_description = resp.factory_description;
					// 			this.quoteData.products[i].factory_part_number = resp.factory_part_number;
					// 			this.selectorRandomKey = new Date().getTime();
					// 		})
					// 		.catch(this.genericErrorHandler);
					// }
				} else if (this.selectorComponent === "selector-cat-5-fact-9") {
					// Bach
					this.instantSelectorValues.quote_id = this.quoteData.id;
					this.instantSelectorValues.product_id = this.quoteData.products[i].product_id;
					this.instantSelectorValues.part_number = this.quoteData.products[i].part_number;
					// console.log("***instantSelectorValues.product_id***", this.instantSelectorValues.product_id);

					this.instantSelectorValues.specifications.horizontal_or_vertical =
						this.quoteData.products[i].specifications.horizontal_or_vertical;
					this.instantSelectorValues.specifications.fixed_or_movable = this.quoteData.products[i].specifications.fixed_or_movable;
					this.instantSelectorValues.specifications.classification = this.quoteData.products[i].specifications.classification;
					this.instantSelectorValues.specifications.width = this.quoteData.products[i].specifications.width;
					this.instantSelectorValues.specifications.height = this.quoteData.products[i].specifications.height;

					if (
						this.instantSelectorValues.quote_id &&
						this.instantSelectorValues.product_id &&
						this.instantSelectorValues.specifications.horizontal_or_vertical &&
						this.instantSelectorValues.specifications.fixed_or_movable &&
						this.instantSelectorValues.specifications.classification &&
						this.instantSelectorValues.specifications.width &&
						this.instantSelectorValues.specifications.height
					) {
						this.$axios
							.$post("/v1/portal/selector-values", this.instantSelectorValues)
							.then((resp) => {
								this.quoteData.products[i].result = resp.result;
								if (resp.result === false) {
									this.quoteData.products[i].alertSelector = true;
								} else {
									this.quoteData.products[i].alertSelector = false;
								}

								// this.selectorRandomKey = new Date().getTime();

								// Null values
								if (resp.result === false) {
									this.quoteData.products[i].specifications.electric_motor = null;
									this.quoteData.products[i].specifications.roller = null;
									this.quoteData.products[i].specifications.cbm_control_panel = null;
									this.quoteData.products[i].specifications.side_guides_installation = null;
									this.quoteData.products[i].specifications.head_box_installation = null;
									this.quoteData.products[i].specifications.crm_motor_box = null;
									this.quoteData.products[i].specifications.side_guides_material = null;
									this.quoteData.products[i].specifications.head_box_size = null;
									this.quoteData.products[i].specifications.bottom_bar = null;
									this.quoteData.products[i].specifications.head_box_material = null;
									this.quoteData.products[i].specifications.irrigation_system = null;
									this.quoteData.products[i].specifications.fabric = null;
									this.quoteData.products[i].specifications.tests_standards = null;

									this.quoteData.products[i].part_number = resp.areel_part_number;
									this.quoteData.products[i].description = resp.areel_description;
									this.quoteData.products[i].factory_description = resp.factory_description;
									this.quoteData.products[i].factory_part_number = resp.factory_part_number;
								}

								this.quoteData.products[i].part_number = resp.areel_part_number;
								this.quoteData.products[i].description = resp.areel_description;
								this.quoteData.products[i].factory_description = resp.factory_description;
								this.quoteData.products[i].factory_part_number = resp.factory_part_number;

								// Set values
								this.quoteData.products[i].specifications.electric_motor = resp.specifications.electric_motor;
								this.quoteData.products[i].specifications.roller = resp.specifications.roller;
								this.quoteData.products[i].specifications.cbm_control_panel = resp.specifications.cbm_control_panel;
								this.quoteData.products[i].specifications.side_guides_installation =
									resp.specifications.side_guides_installation;
								this.quoteData.products[i].specifications.head_box_installation = resp.specifications.head_box_installation;
								this.quoteData.products[i].specifications.crm_motor_box = resp.specifications.crm_motor_box;
								this.quoteData.products[i].specifications.side_guides_material = resp.specifications.side_guides_material;
								this.quoteData.products[i].specifications.head_box_size = resp.specifications.head_box_size;
								this.quoteData.products[i].specifications.bottom_bar = resp.specifications.bottom_bar;
								this.quoteData.products[i].specifications.head_box_material = resp.specifications.head_box_material;
								this.quoteData.products[i].specifications.irrigation_system = resp.specifications.irrigation_system;
								this.quoteData.products[i].specifications.fabric = resp.specifications.fabric;
								this.quoteData.products[i].specifications.tests_standards = resp.specifications.tests_standards;
								this.$toast.clear();
							})
							.catch((err) => {
								this.genericErrorHandler(err, formRef);
							});
					}
				} else if (this.selectorComponent === "selector-cat-6-fact-3") {
					// Rollsis

					this.instantSelectorValues.quote_id = this.quoteData.id;
					this.instantSelectorValues.product_id = this.quoteData.products[i].product_id;
					this.instantSelectorValues.part_number = this.quoteData.products[i].part_number;

					this.instantSelectorValues.specifications.fire_rating = this.quoteData.products[i].specifications.fire_rating;
					this.instantSelectorValues.specifications.materials_type = this.quoteData.products[i].specifications.materials_type;
					this.instantSelectorValues.specifications.skin = this.quoteData.products[i].specifications.skin;
					this.instantSelectorValues.specifications.formed_or_extruded =
						this.quoteData.products[i].specifications.formed_or_extruded;
					this.instantSelectorValues.specifications.perforated = this.quoteData.products[i].specifications.perforated;
					this.instantSelectorValues.specifications.insulation = this.quoteData.products[i].specifications.insulation;
					this.instantSelectorValues.specifications.panel_height = this.quoteData.products[i].specifications.panel_height;
					this.instantSelectorValues.specifications.width = this.quoteData.products[i].specifications.width;
					this.instantSelectorValues.specifications.height = this.quoteData.products[i].specifications.height;

					if (
						this.instantSelectorValues.quote_id &&
						this.instantSelectorValues.product_id &&
						this.instantSelectorValues.specifications.fire_rating &&
						this.instantSelectorValues.specifications.materials_type &&
						this.instantSelectorValues.specifications.skin &&
						this.instantSelectorValues.specifications.formed_or_extruded &&
						this.instantSelectorValues.specifications.perforated &&
						// this.instantSelectorValues.specifications.insulation &&
						this.instantSelectorValues.specifications.panel_height
						// this.instantSelectorValues.specifications.width &&
						// this.instantSelectorValues.specifications.height
					) {
						this.$axios
							.$post("/v1/portal/selector-values", this.instantSelectorValues)
							.then((resp) => {
								this.quoteData.products[i].result = resp.result;
								if (resp.result === false) {
									this.quoteData.products[i].alertSelector = true;
								} else {
									this.quoteData.products[i].alertSelector = false;
								}

								// this.selectorRandomKey = new Date().getTime();

								// Null values
								if (resp.result === false) {
									this.quoteData.products[i].specifications.options.ral = null;
									this.quoteData.products[i].specifications.options.hs_motor = null;
									this.quoteData.products[i].specifications.options.inner_ral = null;
									this.quoteData.products[i].specifications.options.motor_type = null;
									this.quoteData.products[i].specifications.options.vision_panel = null;
									this.quoteData.products[i].specifications.options.pedestrian_doors = null;
									this.quoteData.products[i].specifications.options.photocell_security = null;
									this.quoteData.products[i].specifications.options.pneumatic_security = null;
									this.quoteData.products[i].specifications.options.pedestrian_doors_size = null;

									this.quoteData.products[i].part_number = resp.areel_part_number;
									this.quoteData.products[i].description = resp.areel_description;
									this.quoteData.products[i].factory_description = resp.factory_description;
									this.quoteData.products[i].factory_part_number = resp.factory_part_number;
								} else {
									// Set values
									this.quoteData.products[i].part_number = resp.areel_part_number;
									this.quoteData.products[i].description = resp.areel_description;
									this.quoteData.products[i].factory_description = resp.factory_description;
									this.quoteData.products[i].factory_part_number = resp.factory_part_number;

									this.quoteData.products[i].specifications.options.ral = resp.options.ral;
									this.quoteData.products[i].specifications.options.hs_motor = resp.options.hs_motor;
									this.quoteData.products[i].specifications.options.inner_ral = resp.options.inner_ral;
									this.quoteData.products[i].specifications.options.motor_type = resp.options.motor_type;
									this.quoteData.products[i].specifications.options.vision_panel = resp.options.vision_panel;
									this.quoteData.products[i].specifications.options.pedestrian_doors = resp.options.pedestrian_doors;
									this.quoteData.products[i].specifications.options.photocell_security = resp.options.photocell_security;
									this.quoteData.products[i].specifications.options.pneumatic_security = resp.options.pneumatic_security;
									this.quoteData.products[i].specifications.options.pedestrian_doors_size =
										resp.options.pedestrian_doors_size;
									this.$toast.clear();
								}
							})
							.catch((err) => {
								this.genericErrorHandler(err, formRef);
							});
					}
				} else if (this.selectorComponent === "selector-cat-4-fact-1") {
					// Stair Nosing

					if (this.quoteData.products[i].specifications.width_in_inch) {
						this.quoteData.products[i].specifications.width_in_mm = this.quoteData.products[i].specifications.width_in_inch;
					}

					this.instantSelectorValues.quote_id = this.quoteData.id;
					this.instantSelectorValues.product_id = this.quoteData.products[i].product_id;

					this.instantSelectorValues.specifications.material = this.quoteData.products[i].specifications.material;
					this.instantSelectorValues.specifications.material_type = this.quoteData.products[i].specifications.material_type;
					this.instantSelectorValues.specifications.nosing = this.quoteData.products[i].specifications.nosing;
					this.instantSelectorValues.specifications.install = this.quoteData.products[i].specifications.install;
					this.instantSelectorValues.specifications.width_in_inch = this.quoteData.products[i].specifications.width_in_inch;
					this.instantSelectorValues.specifications.width_in_mm = this.quoteData.products[i].specifications.width_in_mm;
					this.instantSelectorValues.specifications.tread = this.quoteData.products[i].specifications.tread;
					this.instantSelectorValues.specifications.colors = this.quoteData.products[i].specifications.colors;
					this.instantSelectorValues.specifications.model = this.quoteData.products[i].specifications.model;

					this.instantSelectorValues.specifications.clear = this.quoteData.products[i].specifications.clear;
					this.instantSelectorValues.specifications.mitered_cuts = this.quoteData.products[i].specifications.mitered_cuts;
					this.instantSelectorValues.specifications.painted_undersides =
						this.quoteData.products[i].specifications.painted_undersides;
					this.instantSelectorValues.specifications.taped_tops = this.quoteData.products[i].specifications.taped_tops;

					if (
						this.instantSelectorValues.quote_id &&
						this.instantSelectorValues.product_id &&
						this.instantSelectorValues.specifications.material &&
						this.instantSelectorValues.specifications.material_type &&
						this.instantSelectorValues.specifications.nosing &&
						this.instantSelectorValues.specifications.install &&
						this.instantSelectorValues.specifications.width_in_inch &&
						this.instantSelectorValues.specifications.width_in_mm &&
						this.instantSelectorValues.specifications.tread &&
						this.instantSelectorValues.specifications.colors &&
						this.instantSelectorValues.specifications.model
					) {
						this.$axios
							.$post("/v1/portal/selector-values", this.instantSelectorValues)
							.then((resp) => {
								this.quoteData.products[i].part_number = resp.areel_part_number;
								this.quoteData.products[i].description = resp.areel_description;
								this.quoteData.products[i].factory_description = resp.factory_description;
								this.quoteData.products[i].factory_part_number = resp.factory_part_number;
								this.$toast.clear();
							})
							.catch((err) => {
								this.genericErrorHandler(err, formRef);
							});
					}
				} else {
					console.log("This is the default selector");
				}
			}
		},

		setDate(days, type) {
			if (type === "weeks") {
				days = days * 7;
			}
			if (days) {
				this.addDays(new Date(), parseInt(days), type);
			}
		},

		exportTemplate() {
			this.$axios.$get(`/v1/portal/selector-import-template?quote_id=${this.quoteData.id}`).then((resp) => {
				// window.open(resp.file);

				const a = document.createElement("a");
				a.href = resp.file;
				document.body.appendChild(a);
				a.click();
				document.body.removeChild(a);

				//	console.log(resp);
			});
		},

		openFilePicker() {
			this.$refs.filepicker.click();
			console.log("🚀 ~ file: NewQuote.vue:openFilePicker ~ this.$refs.filepicker:", this.$refs.filepicker);
		},

		uploadFile(e) {
			//	console.log("🚀 ~ file: NewQuote.vue:upload ~ e:", e);
			const files = e.target.files;

			if (!files.length) return;

			const file = files[0];
			//	console.log("file", file);

			if (!(file instanceof File)) return;

			const formData = new FormData();
			formData.append("file", file);
			formData.append("quote_id", this.quoteData.id);

			this.$axios
				.$post("/v1/portal/selector-import", formData)
				.then((resp) => {
					//		console.log("🚀 ~ .then ~ resp:", resp);
					// this.quoteData.products = resp;
					// this.quoteData.products = [...this.quoteData.products, ...resp];
					this.quoteData.products.push(...resp);

					this.$refs.filepicker.value = "";
					for (let i = 0; i < this.quoteData.products.length; i++) {
						setTimeout(() => {
							this.getSelectorValues(i);
						}, 200);
					}
					// this.selectorRandomKey = new Date().getTime();
				})
				.catch((e) => {
					this.genericErrorHandler(e);
				});
		},

		addDays(date, days, type) {
			const dateCopy = new Date(date);
			dateCopy.setDate(date.getDate() + days);
			const originalDate = dateCopy.toLocaleDateString();
			const convertedDate = this.convertDateFormat(originalDate);
			if (type === "weeks") {
				this.quoteOrderData.delivery_to_client_with_additional_weeks = convertedDate;
			} else {
				this.quoteData.valid_until_date = convertedDate;
			}
		},

		convertDateFormat(dateString) {
			// Split the date string by '/'
			const dateParts = dateString.split("/");
			// Rearrange the date parts in the desired format (YYYY-MM-DD)
			const convertedDate = dateParts[2] + "-" + dateParts[0].padStart(2, "0") + "-" + dateParts[1].padStart(2, "0");
			return convertedDate;
		},

		setAddress(address) {
			this.quoteData.delivery_method_terms = "<p>C&F " + address + " ( Airport / Port )</p>";
		},

		fetchPaymentMethods() {
			this.$axios
				.get("/v1/lookups/payment-methods")
				.then((response) => {
					this.paymentMethods = response.data;
					// console.log("This is the fetchPaymentMethods function---------------------------------------------paymentMethods", this.paymentMethods);
				})
				.catch((error) => {
					console.error("Error fetching payment methods:", error);
				});
		},

		updatePaymentMethodTerms() {
			const selectedMethod = this.paymentMethods.find((method) => method.value === this.quoteData.payment_method_id);
			if (selectedMethod && selectedMethod.meta && selectedMethod.meta.terms) {
				this.quoteData.payment_method_terms = selectedMethod.meta.terms;
			}
		},

		addComment() {
			this.$axios
				.$post(`/v1/portal/quotes/${this.$route.params.quote_id}/comments`, this.comment)
				.then((resp) => {
					this.$toast.success("Added successfully");
					this.getComments();
				})
				.catch(this.genericErrorHandler);
		},

		getComments() {
			this.$axios.$get(`/v1/portal/quotes/${this.$route.params.quote_id}/comments`).then((resp) => {
				this.commentsData = resp;
				this.comment.body = "";
			});
		},

		priceHistoryFunction(i) {
			this.priceHistoryModel = true;
			this.selectedProductHistory = this.quoteData.products[i];
			this.clientHistoryLoader = false;
			this.$axios
				.$get(`/v1/portal/products/${this.quoteData.products[i].product_id}/quotes?current_quote_id=${this.$route.params.quote_id}`)
				.then((resp) => {
					this.priceHistoryData = resp;
					this.clientHistoryLoader = true;
				});
		},

		// Old PDF Function -- Not Used
		exportToPDF(element) {
			const divsToHide = document.getElementsByClassName("hidden-on-copy"); // divsToHide is an array
			const dataFooter = document.getElementsByClassName("v-data-footer"); // divsToHide is an array

			for (let i = 0; i < divsToHide.length; i++) {
				divsToHide[i].style.display = "none"; // depending on what you're doing
			}
			for (let i = 0; i < dataFooter.length; i++) {
				dataFooter[i].style.display = "none"; // depending on what you're doing
			}

			if (element) {
				html2pdf(document.getElementById(element), {
					margin: 1,
					filename: "quote.pdf",
				});
			} else {
				html2pdf(document.getElementById("element-to-convert"), {
					margin: 1,
					filename: "quote.pdf",
					// orientation: "portrait",
					// quality: 0.98,
				});
			}

			setTimeout(() => {
				for (let i = 0; i < divsToHide.length; i++) {
					divsToHide[i].style.display = "table-cell"; // depending on what you're doing
				}
				for (let i = 0; i < dataFooter.length; i++) {
					dataFooter[i].style.display = "flex"; // depending on what you're doing
				}
			}, 300);
		},

		// New PDF Function
		exportPdf(element) {
			const divsToHide = document.getElementsByClassName("hidden-on-copy"); // divsToHide is an array
			const dataFooter = document.getElementsByClassName("v-data-footer"); // divsToHide is an array

			for (let i = 0; i < divsToHide.length; i++) {
				divsToHide[i].style.display = "none"; // depending on what you're doing
			}
			for (let i = 0; i < dataFooter.length; i++) {
				dataFooter[i].style.display = "none"; // depending on what you're doing
			}

			// just this export code
			// const doc = new JsPDF();
			const doc = new JsPDF("p", "mm", "a4");
			const section = document.getElementById(element);

			html2canvas(section, { scale: 3 }) // Increase the scale value for better quality
				.then((canvas) => {
					const imgData = canvas.toDataURL("image/png");

					doc.addImage(imgData, "PNG", 10, 10, 190, 0);

					// Save the PDF file
					doc.save(`${this.quoteData.reference_number}.pdf`);
				});
			// end code

			setTimeout(() => {
				for (let i = 0; i < divsToHide.length; i++) {
					divsToHide[i].style.display = "table-cell"; // depending on what you're doing
				}
				for (let i = 0; i < dataFooter.length; i++) {
					dataFooter[i].style.display = "flex"; // depending on what you're doing
				}
			}, 300);
		},

		copyTable(sectionId) {
			// -------- start remover tags -----------
			const divsToHide = document.getElementsByClassName("hidden-on-copy"); // divsToHide is an array
			const dataFooter = document.getElementsByClassName("v-data-footer"); // divsToHide is an array
			for (let i = 0; i < divsToHide.length; i++) {
				divsToHide[i].style.display = "none"; // depending on what you're doing
			}
			for (let i = 0; i < dataFooter.length; i++) {
				dataFooter[i].style.display = "none"; // depending on what you're doing
			}
			// -------- End remover tags -----------

			const table = document.getElementById(sectionId);
			const range = document.createRange();
			range.selectNode(table);
			const selection = window.getSelection();
			selection.removeAllRanges();
			selection.addRange(range);
			document.execCommand("copy");
			selection.removeAllRanges();
			this.$toast.success("Table copied successfully");

			// setTimeout(() => {
			for (let i = 0; i < divsToHide.length; i++) {
				divsToHide[i].style.display = "table-cell"; // depending on what you're doing
			}
			for (let i = 0; i < dataFooter.length; i++) {
				dataFooter[i].style.display = "flex"; // depending on what you're doing
			}
			// }, 300);
		},

		captureScreenshot(element) {
			html2canvas(document.querySelector("#" + element)).then((canvas) => {
				const dataURL = canvas.toDataURL("image/png");
				const a = document.createElement("a");
				a.href = dataURL;
				a.download = `${this.quoteData.reference_number}.png`;
				a.click();
			});
		},

		setSelector() {
			this.selectorComponent = "selector-cat-" + this.productCategory + "-fact-" + this.selectorFactoryId;

			if (
				this.selectorComponent !== "selector-cat-1-fact-1" &&
				this.selectorComponent !== "selector-cat-1-fact-4" &&
				this.selectorComponent !== "selector-cat-5-fact-9" &&
				this.selectorComponent !== "selector-cat-6-fact-3"
			) {
				this.isDefaultSelector = true;
			} else {
				this.isDefaultSelector = false;
			}
		},

		takeOffHandler(id, type, index) {
			if (type === "new") {
				if (this.selectorComponent === "selector-cat-6-fact-3") {
					this.quoteData.products.push({
						product_id: null,
						quantity: null,
						unit_price: null,
						total: null,
						uom: null,
						part_number: null,
						description: null,
						is_optional: false,
						is_hidden: false,
						client_tag: null,
						specifications: {
							options: {},
						},
					});
				} else {
					this.quoteData.products.push({
						product_id: null,
						quantity: null,
						unit_price: null,
						total: null,
						uom: null,
						part_number: null,
						description: null,
						is_optional: false,
						is_hidden: false,
						client_tag: null,
						specifications: {},
					});
				}
			}
			this.selector = true;
			this.selectorPanel = index;

			const key = `product-${index + 1}`;
			// console.log("This key ---------------------", key);

			setTimeout(() => {
				this.scrollTo(key);
			}, 200);
		},

		scrollTo(key) {
			// console.log("#" + key, document.getElementsByClassName("v-dialog v-dialog--active v-dialog--persistent"));
			this.$vuetify.goTo("#" + key, {
				//	offset: 30,
				container: "div.v-dialog.v-dialog--active.v-dialog--persistent",
			});
		},

		priceProductHandler(i) {
			this.priceProductModel = true;
			this.selectedProduct = this.quoteData.products[i];
			this.indexField = i;
		},

		addClientMethod() {
			if (!this.$refs.newClientValidate.validate()) return;
			this.$axios
				.$post(`/v1/portal/clients`, this.newClientData)
				.then((resp) => {
					this.ShowClientDialog = false;
					this.$toast.success("has been successfully");
					this.$store.dispatch("cache/clearKey", "/v1/lookups/client-contact-people");

					this.newClientData.name = "";
					this.newClientData.email = "";
					this.newClientData.number = "";
					this.newClientData.country_id = "";
					this.newClientData.contact_people = [];
				})
				.catch(this.genericErrorHandler);
		},

		attachmentsDialogFunction() {
			this.attachmentData.attachments = [];
			this.attachmentData.attachments.push({
				path: null,
				name: null,
				type: null,
				comment: null,
			});
			this.attachmentsDialog = true;
		},

		addAttachmentsMethod() {
			if (!this.$refs.attachmentForm.validate()) return;

			this.isLoading = true;
			this.$axios.$post(`/v1/portal/quotes/${this.$route.params.quote_id}/attachments`, this.attachmentData).then(() => {
				this.$toast.success("We are in a process of saving the attachments in the background , it will take a few seconds");
				this.getQuoteAttachments();
				this.attachmentsDialog = false;

				if (this.quoteData.attachments && this.quoteData.attachments.length < 1) {
					setTimeout(() => {
						this.$axios.$get(`/v1/portal/quotes/${this.$route.params.quote_id}?ids=contactPeople`).then((resp) => {
							this.quoteData = resp;
						});
					}, 1000);
				}

				this.isLoading = false;
			});
		},

		getQuoteAttachments() {
			this.attachmentData.attachments = [];
			this.$axios.$get(`/v1/portal/quotes/${this.$route.params.quote_id}/attachments`).then((resp) => {
				this.attachmentData.attachments = resp;

				this.AttNewObject = {};
				this.attachmentData.attachments.forEach((item) => {
					if (!this.AttNewObject[item.type]) this.AttNewObject[item.type] = [];

					this.AttNewObject[item.type].push({
						id: item.id,
						name: item.name,
						path: item.path,
						updated_at: item.updated_at,
						attached_by: item.attached_by?.name,
						comment: item.comment,
					});
				});
			});
		},

		deleteQuoteAttachment(id) {
			this.$confirm(null, { title: "Are you sure to delete the item?" }).then((isAccepted) => {
				if (isAccepted) {
					this.$axios.$delete(`/v1/portal/quotes/${this.$route.params.quote_id}/attachments/${id}`).then(() => {
						this.getQuoteAttachments();
					});
				}
			});
		},

		openQuoteFolder() {
			// window.open("https://drive.google.com/drive/my-drive", "_blank");
			window.open(this.quoteData.quote_folder, "_blank");
		},

		priceDiscountMethod(stepName) {
			this.quoteData.current_step = stepName;
			this.$axios
				.$put(`/v1/portal/quotes/${this.$route.params.quote_id}`, this.quoteData)
				.then((resp) => {
					this.quoteData = resp;

					this.$axios
						.$get(`/v1/portal/quotes/${this.$route.params.quote_id}?ids=contactPeople`)
						.then((resp) => {
							this.quoteData = resp;
						})
						.catch(this.genericErrorHandler);

					this.priceDiscountsModel = false;
					this.$toast.success("successfully saving data");
				})
				.catch(this.genericErrorHandler);
		},

		confirmHandler(stepName, NextStep) {
			if (!this.quoteData.order) {
				this.$confirm("", { title: "Are you sure convert to order" }).then((isAccepted) => {
					if (isAccepted) {
						this.validationStep(stepName, NextStep);
					}
				});
			} else {
				this.validationStep(stepName, NextStep);
			}
		},

		clientFilter(item, query) {
			return (
				item.meta.client_name.toLowerCase().includes(query.toLowerCase()) ||
				item.text.toLowerCase().includes(query.toLowerCase()) ||
				String(item.meta?.number).includes(query)
			);
		},

		productActivitesMethod(item) {
			this.productActivitesData = item.activities;
			this.productActivitiesModal = true;
		},

		// start order section
		paymenManagementMethod(type) {
			// Get quote data
			if (type === "factory") {
				this.$axios.$get(`/v1/portal/orders/${this.quoteOrderData.id}/factory-payments`).then((resp) => {
					this.factoryPayments = resp;

					this.paymenManagementDialogType = "factory";
					this.paymenManagementDialog = true;
				});
			}
			if (type === "client") {
				this.$axios.$get(`/v1/portal/orders/${this.quoteOrderData.id}/client-payments`).then((resp) => {
					this.clientPayments = resp;

					this.paymenManagementDialogType = "client";
					this.paymenManagementDialog = true;
				});
			}
			if (type === "all") {
				this.$axios.$get(`/v1/portal/orders/${this.quoteOrderData.id}/factory-payments`).then((resp) => {
					this.factoryPayments = resp;
				});

				this.$axios.$get(`/v1/portal/orders/${this.quoteOrderData.id}/client-payments`).then((resp) => {
					this.clientPayments = resp;
				});
			}
		},

		PaymentsAddNewDialogOpenReset() {
			this.cfNewAddPayment.id = null;
			this.cfNewAddPayment.reference = null;
			this.cfNewAddPayment.payment_method = null;
			this.cfNewAddPayment.amount = null;
			this.cfNewAddPayment.status = null;
			this.cfNewAddPayment.currency_id = null;
			this.cfNewAddPayment.exchange_rate = null;
			this.$set(this.cfNewAddPayment, "due_date", null);

			this.PaymentsAddNewDialog = true;
		},

		PaymentsAddNewDialogHandler(id, type) {
			if (type === "factory") {
				// /api/v1/portal/orders/4/factory-payments
				this.$axios.$get(`/v1/portal/orders/${this.quoteOrderData.id}/factory-payments/${id}`).then((resp) => {
					this.cfNewAddPayment = resp;
					this.paymenManagementDialogType = "factory";
					this.PaymentsAddNewDialog = true;
				});
			}
			if (type === "client") {
				this.$axios.$get(`/v1/portal/orders/${this.quoteOrderData.id}/client-payments/${id}`).then((resp) => {
					this.cfNewAddPayment = resp;
					this.paymenManagementDialogType = "client";
					this.PaymentsAddNewDialog = true;
				});
			}
		},

		paymentsDeleteHandler(id, type) {
			if (type === "factory") {
				this.$confirm(null, { title: "Are you sure to delete the item?" }).then((isAccepted) => {
					if (isAccepted) {
						this.$axios
							.$delete(`/v1/portal/orders/${this.quoteOrderData.id}/factory-payments/${id}`)
							.then((resp) => {
								this.$toast.success("The item has been deleted successfully");
								this.paymenManagementMethod(type);
							})
							.catch(this.genericErrorHandler);
					}
				});
			}

			if (type === "client") {
				this.$confirm(null, { title: "Are you sure to delete the item?" }).then((isAccepted) => {
					if (isAccepted) {
						this.$axios
							.$delete(`/v1/portal/orders/${this.quoteOrderData.id}/client-payments/${id}`)
							.then((resp) => {
								this.$toast.success("The item has been deleted successfully");
								this.paymenManagementMethod(type);
							})
							.catch(this.genericErrorHandler);
					}
				});
			}
		},

		addClientPaymentFunction(id, type) {
			//	console.log("id=", id, "type=", type);
			let apiType;
			if (type === "client") {
				apiType = "client-payments";
			} else {
				apiType = "factory-payments";
			}

			if (id) {
				this.$axios
					.$put(`/v1/portal/orders/${this.quoteOrderData.id}/${apiType}/${id}`, this.cfNewAddPayment)
					.then((resp) => {
						this.paymenManagementMethod();
						this.PaymentsAddNewDialog = false;

						this.cfNewAddPayment.reference = "";
						this.cfNewAddPayment.payment_method = "";
						this.cfNewAddPayment.amount = "";
						this.cfNewAddPayment.status = "";
						this.cfNewAddPayment.currency_id = "";
						this.cfNewAddPayment.exchange_rate = "";
						this.cfNewAddPayment.due_date = null;

						this.paymenManagementMethod(type);
					})
					.catch(this.genericErrorHandler);
			} else {
				this.$axios
					.$post(`/v1/portal/orders/${this.quoteOrderData.id}/${apiType}`, this.cfNewAddPayment)
					.then((resp) => {
						this.paymenManagementMethod();
						this.PaymentsAddNewDialog = false;

						this.cfNewAddPayment.reference = "";
						this.cfNewAddPayment.payment_method = "";
						this.cfNewAddPayment.amount = "";
						this.cfNewAddPayment.status = "";
						this.cfNewAddPayment.currency_id = "";
						this.cfNewAddPayment.exchange_rate = "";
						this.cfNewAddPayment.due_date = null;

						this.paymenManagementMethod(type);
					})
					.catch(this.genericErrorHandler);
			}
		},

		getQuoteOrders() {
			this.$axios
				.$get(`/v1/portal/quotes/${this.$route.params.quote_id}/order`)
				.then((resp) => {
					this.quoteOrderData = resp;
					this.orderStep = resp.order_current_step;

					this.fetchPaymentMethods();
					this.orderShipmentsGetAllApi();
					this.paymenManagementMethod("all");
					this.waitingFactoryInvoiceGetAllApi();
					this.factoryConfirmationsGetAllApi();
					this.factoryFabricationsGetAllApi();
				})
				.catch(this.genericErrorHandler);
		},

		updateQuoteOrder(stepName, type) {
			this.quoteOrderData.current_step = stepName;
			console.log("***current_step=***", stepName);
			this.$axios
				.$put(`/v1/portal/quotes/${this.$route.params.quote_id}/order`, this.quoteOrderData)
				.then((resp) => {
					this.quoteOrderData = resp;
					if (type === "next") {
						this.orderStep = resp.order_current_step + 1;
					} else {
						this.orderStep = resp.order_current_step;
					}
					this.$toast.success("updated successfully");

					if (this.orderStep === 3) {
						this.waitingFactoryInvoiceGetAllApi();
					}
					if (this.orderStep === 4) {
						this.factoryConfirmationsGetAllApi();
					}
					if (this.orderStep === 5) {
						this.factoryFabricationsGetAllApi();
					}
					if (this.orderStep === 6) {
						this.orderShipmentsGetAllApi();
					}
				})
				.catch(this.genericErrorHandler);
		},

		orderShipmentsGetByIdApi(type, id) {
			if (type === "create") {
				// -------------- Freight ------------//
				this.orderShipmentsDataById.freight_forwarder_id = "";
				this.orderShipmentsDataById.freight_invoice_number = "";
				this.orderShipmentsDataById.freight_amount = "";
				this.$set(this.orderShipmentsDataById, "freight_date", null);
				this.orderShipmentsDataById.freight_currency_id = "";

				// -------------- Insurance ------------//
				this.orderShipmentsDataById.insurance_company_id = "";
				this.orderShipmentsDataById.insurance_invoice_number = "";
				this.$set(this.orderShipmentsDataById, "insurance_date", null);
				this.orderShipmentsDataById.insurance_currency_id = "";
				this.orderShipmentsDataById.legalization_company_id = "";

				// -------------- Insurance ------------//
				this.orderShipmentsDataById.legalization_company_id = "";
				this.orderShipmentsDataById.document_legalization_invoice_number = "";
				this.orderShipmentsDataById.document_legalization_amount = "";
				this.$set(this.orderShipmentsDataById, "document_legalization_date", null);
				this.orderShipmentsDataById.document_legalization_currency_id = "";
				this.orderShipmentsDataById.commissions = "";
				this.$set(this.orderShipmentsDataById, "pickup_date", null);
				this.$set(this.orderShipmentsDataById, "actual_shipment_date", null);
				this.$set(this.orderShipmentsDataById, "estimated_arrival_date", null);

				this.shipmentApiType = "create";
				this.waitingForShippingDialog = true;
			} else {
				this.$axios // Api: portal/orders/1/shipments/1
					.$get(`/v1/portal/orders/${this.quoteOrderData.id}/shipments/${id}`)
					.then((resp) => {
						this.orderShipmentsDataById = resp;

						this.orderShipmentsDataById.freight_date = this.$dayjs(this.orderShipmentsDataById.freight_date).format(
							"YYYY-MM-DD"
						);

						this.orderShipmentsDataById.insurance_date = this.$dayjs(this.orderShipmentsDataById.insurance_date).format(
							"YYYY-MM-DD"
						);

						this.orderShipmentsDataById.document_legalization_date = this.$dayjs(
							this.orderShipmentsDataById.document_legalization_date
						).format("YYYY-MM-DD");

						this.orderShipmentsDataById.pickup_date = this.$dayjs(this.orderShipmentsDataById.pickup_date).format("YYYY-MM-DD");

						this.orderShipmentsDataById.actual_shipment_date = this.$dayjs(
							this.orderShipmentsDataById.actual_shipment_date
						).format("YYYY-MM-DD");

						this.orderShipmentsDataById.estimated_arrival_date = this.$dayjs(
							this.orderShipmentsDataById.estimated_arrival_date
						).format("YYYY-MM-DD");

						this.shipmentId = resp.id;
						this.shipmentApiType = type; // edit
						this.waitingForShippingDialog = true;
					})
					.catch(this.genericErrorHandler);
			}
		},
		orderShipmentsUpdateApi(id) {
			if (this.shipmentApiType === "edit" && id) {
				// console.log("edit case", "id=", id);
				this.$axios // Update Api: portal/orders/1/shipments/1
					.$put(`/v1/portal/orders/${this.quoteOrderData.id}/shipments/${id}`, this.orderShipmentsDataById)
					.then((resp) => {
						this.orderShipmentsGetAllApi();
						this.$toast.success("updated successfully");
						this.waitingForShippingDialog = false;
					})
					.catch(this.genericErrorHandler);
			} else {
				// console.log("create case", "id=", id);
				this.$axios // Create Api: portal/orders/1/shipments
					.$post(`/v1/portal/orders/${this.quoteOrderData.id}/shipments`, this.orderShipmentsDataById)
					.then((resp) => {
						this.orderShipmentsData = resp;
						this.orderShipmentsGetAllApi();
						this.$toast.success("updated successfully");
						this.waitingForShippingDialog = false;
					})
					.catch(this.genericErrorHandler);
			}
		},
		orderShipmentsGetAllApi() {
			this.$axios // Api: portal/orders/1/shipments
				.$get(`/v1/portal/orders/${this.quoteOrderData.id}/shipments`)
				.then((resp) => {
					this.orderShipmentsData = resp.items;
				})
				.catch(this.genericErrorHandler);
		},
		deleteOrderShipments(id) {
			this.$confirm(null, { title: "Are you sure to delete the item?" }).then((isAccepted) => {
				if (isAccepted) {
					this.$axios
						.$delete(`/v1/portal/orders/${this.quoteOrderData.id}/shipments/${id}`)
						.then((resp) => {
							this.orderShipmentsGetAllApi();
						})
						.catch(this.genericErrorHandler);
				}
			});
		},

		goToQuote(item) {
			const router = this.$router;
			const routeData = { name: "new-quotes", params: { quote_id: item.quote_id } };

			// Create a temporary anchor element
			const tempLink = document.createElement("a");

			// Set the href attribute based on the route path
			tempLink.href = router.resolve(routeData).href;

			// Open the link in a new tab/window
			tempLink.target = "_blank";

			// Programmatically click the anchor element to open in a new tab
			tempLink.click();
		},
		resetContactPeople() {
			this.quoteData.contact_people = [];
		},

		factoryActivitiesMethod() {
			this.factoryActivitiesModal = true;

			if (this.quoteData.products.length > 0) {
				for (let i = 0; i < this.quoteData.products.length; i++) {
					if (this.quoteData.products[i].activities.length > 0) {
						this.selectedProductActivities[i] = this.quoteData.products[i];
					}
				}
			}
		},

		quoteMarkupAllProductsMethod() {
			this.$axios.$put(`/v1/portal/quotes/${this.$route.params.quote_id}/products`, this.quoteMarkupProducts).then((resp) => {
				this.quoteMarkupAllProductsModel = false;
				this.$toast.success("updated successfully");

				this.$axios.$get(`/v1/portal/quotes/${this.$route.params.quote_id}?ids=contactPeople`).then((resp) => {
					this.quoteData = resp;
				});
			});
		},

		quoteMarkupBulkProductsMethod() {
			const data = { data: this.quoteMarkupBulkProducts };

			this.$axios.$put(`/v1/portal/quotes/${this.$route.params.quote_id}/products/bulk-update`, data).then((resp) => {
				this.quoteMarkupBulkProductsModel = false;
				this.$toast.success("updated successfully");

				this.$axios.$get(`/v1/portal/quotes/${this.$route.params.quote_id}?ids=contactPeople`).then((resp) => {
					this.quoteData = resp;
				});
			});
		},

		swiftEditPaymentMethod(id, type) {
			this.PaymentsAddNewDialogHandler(id, type);
		},

		swiftAddPaymentMethods(type) {
			this.paymenManagementDialogType = type;
			this.PaymentsAddNewDialogOpenReset();
		},

		clearCache() {
			this.$store.dispatch("cache/clearAllKeys");
		},

		orderPdfLoop(type, id) {
			this.$axios
				.$get(`/v1/portal/orders/${this.quoteOrderData.id}/${type}/${id}`)
				.then((resp) => {
					this.genericPdfLoopData = null;
					this.genericPdfLoopData = resp;

					// Mix confirmation_products with products
					// const mixedProducts = this.genericPdfLoopData.order.quote.products.map((product) => {
					const mixedProducts = this.quoteOrderData.quote.products.map((product) => {
						if (type === "factory-confirmations") {
							const specificProduct = this.genericPdfLoopData.confirmation_products.find(
								(cp) => cp.product_id === product.id
							);
							if (specificProduct) {
								return {
									...product,
									acknowledge_quantity: specificProduct.confirmation_quantity,
								};
							}
						} else if (type === "factory-fabrications") {
							const specificProduct = this.genericPdfLoopData.fabrication_products.find((cp) => cp.product_id === product.id);
							if (specificProduct) {
								return {
									...product,
									acknowledge_quantity: specificProduct.fabrication_quantity,
								};
							}
						} else if (type === "factory-invoices") {
							const specificProduct = this.genericPdfLoopData.invoice_products.find((cp) => cp.product_id === product.id);
							if (specificProduct) {
								return {
									...product,
									acknowledge_quantity: specificProduct.invoice_quantity,
								};
							}
						}

						return product;
					});

					// Assign the mixed products back to the quote
					this.genericPdfLoopData.order.quote.products = mixedProducts;

					if (type === "factory-invoices") {
						this.ciDialog = true;
					} else {
						this.oaDialog = true;
					}

					this.pdfData = this.genericPdfLoopData;
				})
				.catch(this.genericErrorHandler);
		},

		// Waiting Factory Invoice CRUD
		waitingFactoryInvoiceModal(type, id) {
			this.generateRandomKey = new Date().getTime();
			this.clearCache();

			if (type === "create") {
				this.factoryInvoices.invoice_number = "";
				this.factoryInvoices.invoice_amount = "";
				this.factoryInvoices.invoice_products = [];
				this.$set(this.factoryInvoices, "invoice_date", null);
				this.waitingFactoryType = "create";
				this.waitingFactoryInvoiceDialog = true;
				this.invoice_id = null;
			} else {
				this.invoice_id = id;
				//	this.$store.dispatch("cache/clearKey", `/v1/lookups/order-products/${this.quoteOrderData.id}?invoice_id=${id}`);

				this.$axios
					.$get(`/v1/portal/orders/${this.quoteOrderData.id}/factory-invoices/${id}`)
					.then((resp) => {
						this.factoryInvoices.invoice_number = "";
						this.factoryInvoices.invoice_amount = "";
						this.factoryInvoices.invoice_products = [];

						this.factoryInvoices = resp;
						this.factoryInvoices.invoice_date = this.$dayjs(this.factoryInvoices.invoice_date).format("YYYY-MM-DD");
						this.waitingFactoryType = "edit";
						this.waitingFactoryId = id;
						this.waitingFactoryInvoiceDialog = true;
					})
					.catch(this.genericErrorHandler);
			}
		},

		waitingFactoryInvoiceGetAllApi() {
			this.$axios // Api: portal/orders/1/factory-invoices
				.$get(`/v1/portal/orders/${this.quoteOrderData.id}/factory-invoices`)
				.then((resp) => {
					this.factoryInvoicesArrayData = resp.items;
				})
				.catch(this.genericErrorHandler);
		},

		waitingFactoryInvoiceApi() {
			if (this.waitingFactoryType === "edit" && this.waitingFactoryId) {
				this.$axios // Update Api: portal/orders/1/factory-invoices/1
					.$put(`/v1/portal/orders/${this.quoteOrderData.id}/factory-invoices/${this.waitingFactoryId}`, this.factoryInvoices)
					.then((resp) => {
						this.$toast.success("updated successfully");
						this.waitingFactoryInvoiceGetAllApi();
						this.waitingFactoryInvoiceDialog = false;
					})
					.catch(this.genericErrorHandler);
			} else {
				this.$axios
					.$post(`/v1/portal/orders/${this.quoteOrderData.id}/factory-invoices`, this.factoryInvoices)
					.then((resp) => {
						this.waitingFactoryInvoiceGetAllApi();
						this.$toast.success("updated successfully");
						this.waitingFactoryInvoiceDialog = false;
					})
					.catch(this.genericErrorHandler);
			}
		},

		addQuantity(id, type, itemId) {
			this.selectionProductId = id;
			this.selectionItemId = itemId;
			this.quantityXfield = 0;
			this.quantityType = type;
			this.waitingFactoryInvoiceQuantityDialog = true;
		},

		addQuantityMethod() {
			// Find the product with the matching ID
			if (this.quantityType === "invoice") {
				const productToUpdate = this.factoryInvoices.invoice_products.find((product) => product.id === this.selectionProductId);

				// If the product is found, update the invoice_quantity
				if (productToUpdate) {
					productToUpdate.invoice_quantity = this.quantityXfield;
					//	console.log("Product updated:", productToUpdate);
				}
			} else if (this.quantityType === "confirmation") {
				const productToUpdate = this.factoryConfirmations.confirmation_products.find(
					(product) => product.id === this.selectionProductId
				);

				// If the product is found, update the invoice_quantity
				if (productToUpdate) {
					productToUpdate.confirmation_quantity = this.quantityXfield;
					//	console.log("Product updated:", productToUpdate);
				}
			} else if (this.quantityType === "fabrication") {
				const productToUpdate = this.factoryFabrications.fabrication_products.find(
					(product) => product.id === this.selectionProductId
				);
				// If the product is found, update the invoice_quantity
				if (productToUpdate) {
					productToUpdate.fabrication_quantity = this.quantityXfield;
					//	console.log("Product updated:", productToUpdate);
				}
			}

			this.generateRandomKey = new Date().getTime();
			this.waitingFactoryInvoiceQuantityDialog = false;
		},

		waitingFactoryInvoiceDelete(id) {
			this.$confirm(null, { title: "Are you sure to delete the item?" }).then((isAccepted) => {
				if (isAccepted) {
					this.$axios
						.$delete(`/v1/portal/orders/${this.quoteOrderData.id}/factory-invoices/${id}`)
						.then((resp) => {
							this.waitingFactoryInvoiceGetAllApi();
						})
						.catch(this.genericErrorHandler);
				}
			});
		},

		// Factory Confirmations CRUD
		factoryConfirmationsModal(type, id) {
			this.generateRandomKey = new Date().getTime();
			this.clearCache();

			if (type === "create") {
				this.factoryConfirmations.received_from_factory = false;
				this.$set(this.factoryConfirmations, "delivery_to_client_at", null);
				this.factoryConfirmations.additional_weeks = "";
				this.factoryConfirmationType = "create";
				this.factoryConfirmationsDialog = true;
				this.factoryConfirmations.confirmation_products = [];
				this.confirmation_id = null;
			} else {
				this.confirmation_id = id;

				this.$axios
					.$get(`/v1/portal/orders/${this.quoteOrderData.id}/factory-confirmations/${id}`)
					.then((resp) => {
						this.factoryConfirmations = null;
						this.factoryConfirmations = resp;

						this.factoryConfirmations.delivery_to_client_at = this.$dayjs(
							this.factoryConfirmations.delivery_to_client_at
						).format("YYYY-MM-DD");
						this.factoryConfirmationType = "edit";
						this.factoryConfirmationId = id;
						this.factoryConfirmationsDialog = true;
					})
					.catch(this.genericErrorHandler);
			}
		},

		factoryConfirmationsGetAllApi() {
			this.$axios // Api: portal/orders/1/factory-confirmations
				.$get(`/v1/portal/orders/${this.quoteOrderData.id}/factory-confirmations`)
				.then((resp) => {
					this.factoryConfirmationsArrayData = resp.items;
				})
				.catch(this.genericErrorHandler);
		},

		factoryConfirmationsApi() {
			if (this.factoryConfirmationType === "edit" && this.factoryConfirmationId) {
				this.$axios // Update Api: portal/orders/1/factory-invoices/1
					.$put(
						`/v1/portal/orders/${this.quoteOrderData.id}/factory-confirmations/${this.factoryConfirmationId}`,
						this.factoryConfirmations
					)
					.then((resp) => {
						this.$toast.success("updated successfully");
						this.factoryConfirmationsGetAllApi();
						this.factoryConfirmationsDialog = false;
					})
					.catch(this.genericErrorHandler);
			} else {
				this.$axios
					.$post(`/v1/portal/orders/${this.quoteOrderData.id}/factory-confirmations`, this.factoryConfirmations)
					.then((resp) => {
						this.factoryConfirmationsGetAllApi();
						this.$toast.success("updated successfully");
						this.factoryConfirmationsDialog = false;
					})
					.catch(this.genericErrorHandler);
			}
		},

		factoryConfirmationsDelete(id) {
			this.$confirm(null, { title: "Are you sure to delete the item?" }).then((isAccepted) => {
				if (isAccepted) {
					this.$axios
						.$delete(`/v1/portal/orders/${this.quoteOrderData.id}/factory-confirmations/${id}`)
						.then((resp) => {
							this.factoryConfirmationsGetAllApi();
						})
						.catch(this.genericErrorHandler);
				}
			});
		},

		randomKey() {
			return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
		},

		// Factory Fabrications CRUD
		factoryFabricationsModal(type, id) {
			this.generateRandomKey = new Date().getTime();
			this.clearCache();

			if (type === "create") {
				this.factoryFabrications.shipment_type = null;
				this.factoryFabrications.lc_number = null;
				this.$set(this.factoryFabrications, "lc_date_of_insurance", null);
				this.factoryFabrications.commodity_as_stated_by_the_bank = null;
				this.factoryFabrications.invoice_number = null;
				this.$set(this.factoryFabrications, "invoice_date", null);
				this.factoryFabrications.beneficiary_through = null;
				this.factoryFabrications.manufacturer_name = null;
				this.factoryFabrications.manufacturer_address = null;
				this.factoryFabrications.additional_details = null;
				this.factoryFabrications.dimensions = null;
				this.factoryFabrications.delivery_method_id = null;
				this.factoryFabrications.shipment_terms = null;
				this.factoryFabrications.weight = null;
				this.factoryFabrications.gross_weight = null;
				this.factoryFabrications.hs_code = null;
				this.factoryFabrications.made_in_country_id = null;
				this.factoryFabrications.signature_and_stamp = null;
				this.factoryFabrications.fabrication_products = [];
				this.factoryConfirmationType = "create";
				this.factoryFabricationsDialog = true;
				this.fabrication_id = null;
			} else {
				this.factoryFabrications.shipment_type = null;
				this.factoryFabrications.lc_number = null;
				this.$set(this.factoryFabrications, "lc_date_of_insurance", null);
				this.factoryFabrications.commodity_as_stated_by_the_bank = null;
				this.factoryFabrications.invoice_number = null;
				this.$set(this.factoryFabrications, "invoice_date", null);
				this.factoryFabrications.beneficiary_through = null;
				this.factoryFabrications.manufacturer_name = null;
				this.factoryFabrications.manufacturer_address = null;
				this.factoryFabrications.additional_details = null;
				this.factoryFabrications.dimensions = null;
				this.factoryFabrications.delivery_method_id = null;
				this.factoryFabrications.shipment_terms = null;
				this.factoryFabrications.weight = null;
				this.factoryFabrications.gross_weight = null;
				this.factoryFabrications.hs_code = null;
				this.factoryFabrications.made_in_country_id = null;
				this.factoryFabrications.signature_and_stamp = null;
				this.factoryFabrications.fabrication_products = [];

				this.fabrication_id = id;
				this.$axios
					.$get(`/v1/portal/orders/${this.quoteOrderData.id}/factory-fabrications/${id}`)
					.then((resp) => {
						this.factoryFabrications = resp;
						this.factoryFabrications.invoice_date = this.$dayjs(this.factoryFabrications.invoice_date).format("YYYY-MM-DD");
						this.factoryFabrications.lc_date_of_insurance = this.$dayjs(this.factoryFabrications.lc_date_of_insurance).format(
							"YYYY-MM-DD"
						);
						this.factoryFabricationsType = "edit";
						this.factoryFabricationsId = id;
						this.factoryFabricationsDialog = true;
					})
					.catch(this.genericErrorHandler);
			}
		},

		factoryFabricationsGetAllApi() {
			this.$axios // Api: portal/orders/1/factory-fabrications
				.$get(`/v1/portal/orders/${this.quoteOrderData.id}/factory-fabrications`)
				.then((resp) => {
					this.factoryFabricationsArrayData = resp.items;
				})
				.catch(this.genericErrorHandler);
		},

		factoryFabricationsApi() {
			if (this.factoryFabricationsType === "edit" && this.factoryFabricationsId) {
				this.$axios // Update Api: portal/orders/1/factory-fabrications/1
					.$put(
						`/v1/portal/orders/${this.quoteOrderData.id}/factory-fabrications/${this.factoryFabricationsId}`,
						this.factoryFabrications
					)
					.then(() => {
						this.$toast.success("updated successfully");
						this.factoryFabricationsGetAllApi();
						this.factoryFabricationsDialog = false;
					})
					.catch(this.genericErrorHandler);
			} else {
				this.$axios
					.$post(`/v1/portal/orders/${this.quoteOrderData.id}/factory-fabrications`, this.factoryFabrications)
					.then(() => {
						this.factoryFabricationsGetAllApi();
						this.$toast.success("updated successfully");
						this.factoryFabricationsDialog = false;
					})
					.catch(this.genericErrorHandler);
			}
		},

		factoryFabricationsDelete(id) {
			this.$confirm(null, { title: "Are you sure to delete the item?" }).then((isAccepted) => {
				if (isAccepted) {
					this.$axios
						.$delete(`/v1/portal/orders/${this.quoteOrderData.id}/factory-fabrications/${id}`)
						.then((resp) => {
							this.factoryFabricationsGetAllApi();
						})
						.catch(this.genericErrorHandler);
				}
			});
		},

		calculateDeliveryPrice() {
			this.quoteData.delivery_price = 0;
			let actualOrEstimated = 0;

			if (this.quoteData.estimated_delivery_price) {
				actualOrEstimated = this.quoteData.estimated_delivery_price;
			}

			if (this.quoteData.actual_delivery_price) {
				actualOrEstimated = this.quoteData.actual_delivery_price;
			}

			if (this.quoteData.delivery_multiplier && actualOrEstimated) {
				this.quoteData.delivery_price =
					parseInt(actualOrEstimated * (this.quoteData.delivery_multiplier / 100)) + parseInt(actualOrEstimated);
			}
		},

		pickItem(id) {
			this.$confirm(null, { title: " Are you sure to pick the quote?" }).then((isAccepted) => {
				if (isAccepted) {
					this.$axios
						.$put(`/v1/portal/quotes/${id}/pick`)
						.then((resp) => {
							this.$toast.success("has been successfully");
						})
						.catch(this.genericErrorHandler);
				}
			});
		},

		deleteComment(id) {
			this.$confirm(null, { title: "Are you sure to delete the item?" }).then((isAccepted) => {
				if (isAccepted) {
					this.$axios
						.$delete(`/v1/portal/quotes/${this.quoteData.id}/comments/${id}`)
						.then((resp) => {
							this.getComments();
							this.$toast.success("has been successfully");
						})
						.catch(this.genericErrorHandler);
				}
			});
		},

		editComment(id) {
			const found = this.commentsData.find((comment) => comment.id === id);
			if (found) {
				this.comment = { ...found };
				this.isEditableComment = true;
				this.commentInstantId = id;
			}
		},
		updateComment(id) {
			this.$axios
				.$put(`/v1/portal/quotes/${this.quoteData.id}/comments/${id}`, { body: this.comment.body })
				.then((resp) => {
					this.getComments();
					this.$toast.success("has been successfully");
					this.isEditableComment = false;
					this.commentInstantId = null;
					this.comment.body = "";
				})
				.catch(this.genericErrorHandler);
		},
	},
};
</script>

<style lang="scss">
.steeper-view {
	background: transparent !important;
	box-shadow: none !important;

	.v-stepper {
		height: 100% !important;

		.ProseMirrorW {
			height: 150px;
		}

		.custom-table-header {
			.v-data-table-header {
				th {
					background: #232323 !important;
					color: #fff !important;
				}
			}
		}

		.stepper-auto-height {
			.v-stepper {
				&__content {
					min-height: auto !important;
				}
			}
		}

		.tiptap-vuetify-editor__content {
			.ProseMirror {
				min-height: 150px;
			}
		}
	}
}

.ProseMirror {
	min-height: 150px;
}

.cutting-paragraph {
	display: -webkit-box;
	line-clamp: 2;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
	height: 3em;
	/* This assumes the line height is 1.5em */
}

.v-snack__wrapper {
	bottom: 0 !important;
}
</style>
