<template>
	<page title="Data Migration" desc="">
		<v-stepper v-if="checkPermission('data_migration.create')" v-model="stepStatus">
			<v-stepper-header>
				<v-stepper-step step="1"> Template Types </v-stepper-step>

				<v-divider></v-divider>

				<v-stepper-step step="2"> Download Blank Template </v-stepper-step>

				<v-divider></v-divider>

				<v-stepper-step step="3"> Upload Template With Data </v-stepper-step>
			</v-stepper-header>

			<v-stepper-items>
				<v-stepper-content step="1">
					<v-card class="mb-12" height="200px" outlined>
						<v-card-title>Select Template Type</v-card-title>
						<v-card-text>
							<v-radio-group v-model="selectedType" mandatory>
								<v-radio label="Quotes Products" value="quotes_products"></v-radio>
								<v-radio label="Quotes" value="quotes"></v-radio>
							</v-radio-group>
						</v-card-text>
					</v-card>
					<div class="d-flex">
						<v-spacer />
						<v-btn color="primary" @click="stepStatus = 2"> Continue </v-btn>
					</div>
				</v-stepper-content>

				<v-stepper-content step="2">
					<v-card class="mb-12" outlined>
						<v-card-title>Download Template</v-card-title>
						<v-card-text class="mt-6">
							<v-btn color="success" @click="exportTemplate">
								<v-icon left>mdi-download</v-icon>
								Download
							</v-btn>
						</v-card-text>
					</v-card>

					<div class="d-flex">
						<v-btn text @click="stepStatus = 1"> Back </v-btn>
						<v-spacer />
						<v-btn color="primary" @click="stepStatus = 3"> Continue </v-btn>
					</div>
				</v-stepper-content>

				<v-stepper-content step="3">
					<v-card class="mb-12" outlined>
						<v-card-title>Upload Template</v-card-title>
						<v-card-text class="mt-6">
							<input ref="filepicker" type="file" class="d-none" @change="uploadFile" />
							<v-btn color="success" :loading="isLoading" @click="openFilePicker">
								<v-icon left>mdi-upload</v-icon>
								Upload
							</v-btn>
						</v-card-text>
					</v-card>

					<div class="d-flex">
						<v-btn text @click="stepStatus = 2"> Back </v-btn>
					</div>
				</v-stepper-content>
			</v-stepper-items>
		</v-stepper>

		<div v-else class="pa-5">
			<v-icon left>mdi-alert-circle</v-icon>
			<span class="pt-2">Sorry, you do not have permission!</span>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			selectedType: null,
			stepStatus: 1,
			isLoading: false,
		};
	},

	methods: {
		exportTemplate() {
			this.$axios.$get(`/v1/portal/data-migration-template?type=${this.selectedType}`).then((resp) => {
				const a = document.createElement("a");
				a.href = resp;
				document.body.appendChild(a);
				a.click();
				document.body.removeChild(a);
			});
		},

		openFilePicker() {
			this.$refs.filepicker.click();
			console.log("🚀 ~ file:vue:openFilePicker ~ this.$refs.filepicker:", this.$refs.filepicker);
		},

		uploadFile(e) {
			const files = e.target.files;

			if (!files.length) return;

			const file = files[0];

			if (!(file instanceof File)) return;

			const formData = new FormData();
			formData.append("file", file);
			formData.append("type", this.selectedType);
			this.isLoading = true;

			this.$axios
				.$post("/v1/portal/data-migration", formData)
				.then((resp) => {
					console.log("🚀 ~ .then ~ resp:", resp);
					this.$refs.filepicker.value = "";
					this.isLoading = false;
					this.$toast.success(
						"Your migration request is being processed in the background. We will notify you as soon as the process is complete."
					);
				})
				.catch((e) => {
					this.genericErrorHandler(e);
					this.isLoading = false;
				});
		},
	},
};
</script>
