<template>
	<v-card>
		<v-card-title>
			<span>Top Client</span>
			<v-spacer />
			<v-select
				dense
				hide-details
				flat
				solo
				label=""
				:items="topItems"
				:menu-props="{ offsetY: true }"
				style="max-width: 130px"
			></v-select>

			<!-- <v-icon right></v-icon> -->
		</v-card-title>

		<v-simple-table class="overflow-x-auto">
			<tbody>
				<tr v-for="item in items" :key="item.name">
					<td>
						<v-avatar class="me-5" size="36px">
							<img alt="Avatar" :src="$faker.image.avatar()" />
						</v-avatar>
						<strong>
							{{ item.fullName }}
						</strong>
					</td>

					<td class="text-center">
						<span v-if="item.rebate < 0" class="primary--text d-flex" style="font-variant-numeric: tabular-nums"
							>{{ item.rebate | money }}
						</span>
						<span v-else class="success--text d-flex">
							<money>{{ item.rebate }}</money>
						</span>
					</td>
				</tr>
			</tbody>
		</v-simple-table>
	</v-card>
</template>

<script>
const gradients = [];
export default {
	data() {
		return {
			item: {
				name: "Open Positions",
			},
			items: [
				{ accountNo: "*********", fullName: "Jasmine King", volume: 0.8, rebate: 156.17 },
				{ accountNo: "**********", fullName: "abdallah ahmad", volume: 0.1, rebate: 655.04 },
				{ accountNo: "**********", fullName: "Muhammed Bassm", volume: 0.5, rebate: 877.7 },
				{ accountNo: "**********", fullName: "Huda test", volume: 0.06, rebate: -1.3 },
				// { accountNo: "**********", fullName: "Saif Banihani", volume: 0.03, rebate: 8660.72 },
				// { accountNo: "**********", fullName: "Hamza Ayaad", volume: 0.1, rebate: -3.37 },
				// { accountNo: "**********", fullName: "Ahmad Od", volume: 0.01, rebate: 199.98 },
			],

			topItems: ["Last week ", "Last Month "],

			width: 4,
			radius: 0,
			padding: 8,
			lineCap: "round",
			gradient: ["#1feaea", "#ffd200", "#f72047"],
			value: [0, 2, 5, 9, 5, 10, 3],
			gradientDirection: "top",
			gradients,
			fill: false,
			type: "trend",
			autoLineWidth: false,
		};
	},

	mounted() {
		setInterval(() => {
			this.items.forEach((item, i) => {
				item.profit = (Math.random() * 100).toFixed(2) * (i % 2 ? -1 : 1);
			});
		}, 1000);
	},
};
</script>
