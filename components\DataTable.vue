<template>
	<Content
		v-bind="$attrs"
		ref="content"
		hide-loader
		hide-pagination
		:pagination-history="false"
		class="overflow-x-auto"
		:per-page="itemsPerPageOptions[0]"
		v-on="$listeners"
	>
		<template v-if="$scopedSlots.filter" #filter="bind">
			<slot name="filter" v-bind="bind" />
		</template>
		<template #prepend-action="bind">
			<slot name="prepend-action" v-bind="bind" />
			<v-menu :close-on-content-click="false">
				<template #activator="{ on }">
					<v-badge dot bordered color="primary" overlap :value="selectedColumns.length !== allowedColumns.length">
						<v-btn v-tooltip:top="'Columns Visibility'" class="me-2" icon width="32" height="32" v-on="on">
							<v-icon>mdi-view-column-outline</v-icon></v-btn
						>
					</v-badge>
				</template>
				<v-card>
					<v-list dense>
						<v-list-item-group v-model="selectedColumns" multiple @change="saveColumnsVisibility">
							<v-list-item
								v-for="col in visibilityColumns"
								:key="'col' + col.value"
								:disabled="
									!selectableColumnsValues.includes(col.value) ||
									(parsedColumnsValues.length === 1 && parsedColumnsValues.includes(col.value))
								"
								:value="col.value"
							>
								<template #default="{ active }">
									<v-list-item-action>
										<v-checkbox
											:input-value="active"
											:disabled="
												!selectableColumnsValues.includes(col.value) ||
												(parsedColumnsValues.length === 1 && parsedColumnsValues.includes(col.value))
											"
										></v-checkbox>
									</v-list-item-action>

									<v-list-item-content>
										<v-list-item-title>{{ col.text }}</v-list-item-title>
									</v-list-item-content>
								</template>
							</v-list-item>
						</v-list-item-group>
					</v-list>
				</v-card>
			</v-menu>
		</template>
		<template #default="{ loading, pagination, paginationHandler, items, isFullscreen }">
			<v-data-table
				:id="id"
				:expanded.sync="localExpanded"
				class="flex-table"
				:headers="parsedColumns"
				:loading="loading"
				:items="items"
				:class="{ 'fullscreen-active': isFullscreen }"
				:height="isFullscreen ? 'calc(100vh - 124px)' : undefined"
				:server-items-length="pagination.total"
				:items-per-page="pagination.per_page"
				:page="pagination.page"
				:group-by="groupBy"
				:item-class="itemClass"
				:footer-props="{ 'items-per-page-options': itemsPerPageOptions }"
				:show-expand="showExpand"
				:single-expand="singleExpand"
				:disable-sort="true"
				@pagination="paginationHandler"
				@page-count="pagination.count = $event"
				@current-items="loadedHandler"
			>
				<template v-for="(slot, name) in $scopedSlots" #[name]="item">
					<slot :name="name" v-bind="item"></slot>
				</template>
				<!-- <template #item.actions="{ item }">
					<div class="d-flex">
						<v-icon class="sort-handle">mdi-sort-alphabetical-variant</v-icon>
						<slot name="item.actions" v-bind="item"></slot>
					</div>
				</template> -->

				<template #item.index="props">
					{{ props.index + 1 }}
				</template>

				<template #footer.prepend>
					<div class="d-flex align-center justify-end flex-grow-1">
						<v-spacer />
						<v-progress-circular
							v-show="loading"
							size="24"
							:indeterminate="loading"
							color="primary"
							width="2"
							class="me-2"
						></v-progress-circular>
					</div>
				</template>
			</v-data-table>
		</template>
	</Content>
</template>

<script>
import Sortable from "sortablejs";
import responsive from "~/mixins/responsive";
export default {
	mixins: [responsive],
	props: {
		columns: {
			type: Array,
			default: () => [],
		},

		itemClass: {
			type: [Function, String],
			default: undefined,
		},
		groupBy: {
			type: String,
			default: undefined,
		},
		permissionModel: {
			type: String,
			default: null,
		},
		expanded: {
			type: Array,
			default: () => [],
		},
		showExpand: {
			type: Boolean,
			default: false,
		},
		sortable: {
			type: Boolean,
			default: false,
		},
		allow: {
			type: Array,
			default: () => ["id", "created_at", "updated_at"],
		},
	},
	data() {
		return {
			filterDrawerModel: false,
			parsedFilter: [],
			searchTimerId: null,
			singleExpand: true,

			// filterData: {
			// 	...deserialize(this.$route.query),
			// },
			filterOptions: {},

			selectedColumns: [],

			itemsPerPageOptions: [15, 30, 50, 100, 500],
			// filterValues: Object.fromEntries(Object.entries(this.$route.query).filter(([key]) => !paginationKeys.includes(key))),
			filterValues: { ...this.$route.query },
			filterObject: {},
			models: {},
			sortableObject: null,

			revision: [
				{
					id: 4,
					name: "Jordan-2 Inner",
					country_code: "Jo-Inner",
				},
				{
					id: 6,
					name: "Jordan-2 Inner",
					country_code: "Jo-Inner",
				},
				{
					id: 2,
					name: "Jordan-2 Inner",
					country_code: "Jo-Inner",
				},
			],
		};
	},
	async fetch() {
		if (!this.apiURL) return;

		const modelsQuery = this.$cloneDeep(this.trimmedModels);
		const paginationQuery = {
			per_page: this.$refs.content ? this.$refs.content.pagination.per_page : undefined,
			page: this.$refs.content ? this.$refs.content.pagination.page : 1,
		};

		const url = this.$router.resolve({
			params: { ...this.$route.params },
			query: modelsQuery,
		}).href;

		if (this.$route.fullPath !== url) {
			this.$router.push(url);
		}

		await this.$axios
			.$get(this.apiURL, {
				params: { ...modelsQuery, ...paginationQuery },
			})
			.then((resp) => {
				this.items = resp.items;
				console.log("***this.items****", this.items);
				if (resp.pagination) {
					this.$refs.content.pagination = resp.pagination;
				}
				if (resp.filters) {
					this.filterObject = resp.filters;
				}
			})
			.catch((error) => {
				this.genericErrorHandler(error);
			});
	},
	computed: {
		hasPermission() {
			return !!this.permissionModel;
		},
		visibilityColumns() {
			return this.columns.filter((column) => !!column.text);
		},
		allowedColumns() {
			if (!this.hasPermission) {
				return this.columns;
			}

			return this.columns.filter((column) => {
				console.log("$gates.hasPermission", this.$gates.hasPermission(`${this.permissionModel}.view.${column.value}`));
				return this.allow.includes(column.value) || this.$gates.hasPermission(`${this.permissionModel}.view.${column.value}`);
			});
		},
		parsedColumns() {
			return this.allowedColumns.filter((column, index) => this.selectedColumns.includes(column.value) || !column.text);
		},
		parsedColumnsValues() {
			return this.parsedColumns.map((column) => column.value);
		},
		searchableItems() {
			return this.allowedColumns.filter((column) => column.searchable);
		},
		selectableColumns() {
			return this.allowedColumns.filter((column, index) => !!column.text);
		},
		selectableColumnsValues() {
			return this.selectableColumns.map((column) => column.value);
		},
		content() {
			return this.$refs.content;
		},
		id() {
			return "table-" + this._uid;
		},

		localExpanded: {
			get() {
				return this.expanded;
			},

			set(expanded) {
				this.$emit("update:expanded", expanded);
			},
		},
		// preferencesColumns() {
		// 	return this.getPreferencesByRoute.dataTable?.columns || [];
		// },
		// preferencesColumnsValues() {
		// 	return this.preferencesColumns;
		// },
	},
	watch: {
		selectedColumns(cols) {
			if (Array.isArray(cols) && cols.length === 1) {
				this.$toast.info(`You can't hide all columns`, { title: "Info" });
			}
		},
	},

	mounted() {
		this.selectedColumns = this.allowedColumns.map((c) => c.value);
		this.loadedHandler();
	},

	methods: {
		refresh() {
			this.$refs.content.refresh();
		},
		initSortable() {
			const table = document.querySelector(`#${this.id} tbody`);
			const _self = this;
			if (!_self.$refs.content.items) return;
			// this way we avoid data binding
			_self.dragNdrop = JSON.parse(JSON.stringify(_self.$refs.content.items));

			this.sortableObject = Sortable.create(table, {
				animation: 700,
				handle: ".sort-handle",
				onEnd({ newIndex, oldIndex }) {
					_self.dragNdrop.splice(newIndex, 0, ..._self.dragNdrop.splice(oldIndex, 1));
					console.log(_self.dragNdrop);
				},
			});
		},
		loadedHandler() {
			if (this.sortable) {
				this.$nextTick(() => {
					this.initSortable();
				});
			}
		},
		saveColumnsVisibility(columns) {
			// this.setPreferencesByRoute([`dataTable.columns`, columns]);
		},
	},
};
</script>
<style lang="scss">
@import "/node_modules/vuetify/src/styles/settings";

/* stylelint-disable */
.v-data-table .v-data-table-header th {
	@include responsive("position", "initial", "initial", sticky);
	background-color: #fff;
	z-index: 1;
	top: 0;
	white-space: nowrap;
}
.v-data-table:not(.fullscreen-active) .v-data-table-header th {
	top: 64px;
}

.v-data-table td {
	// padding-bottom: 12px !important;
	height: 56px !important;
}
.theme--light {
	.v-data-table .v-data-table-header th,
	.v-data-table .v-data-footer {
		background-color: #fff;
	}
}
.theme--dark {
	.v-data-table .v-data-table-header th {
		background-color: map-get($material-dark-elevation-colors, "4");
	}
}

.v-data-table .v-data-table__wrapper {
	overflow: unset;
}
.fullscreen {
	// padding-top: 8px;

	.v-data-footer {
		position: relative;
	}
	.v-data-table__wrapper {
		max-height: calc(100vh - 128px);
		overflow: scroll;
	}
}
.sort-handle {
	cursor: move;
}
</style>
