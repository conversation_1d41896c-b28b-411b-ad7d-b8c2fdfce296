<template>
	<page title="Part Number" desc="">
		<template #actions>
			<v-btn color="success" @click="$refs.crud.new()"> <v-icon left>mdi-plus</v-icon>Add Part Number</v-btn>
		</template>
		<div>
			<data-table ref="dataTable"  :columns="columns" api="/v1/portal/part-number-components">
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="'Delete'" small icon @click="$refs.crud.delete(item.id)">
							<v-icon small>mdi-delete</v-icon>
						</v-btn>
						<v-btn v-tooltip="'Edit'" small icon @click="$refs.crud.edit(item.id)">
							<v-icon small>mdi-pencil</v-icon>
						</v-btn>
					</div>
				</template>

				<template #item.product="{ item }">
					{{ item.product.name }}
				</template>

				<template #item.specification="{ item }">
					{{ item.specification.name }}
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>
			</data-table>

			<crud
				ref="crud"
				v-slot="{ item }"
				width="400"
				:default="defaultItem"
				item-name="Country"
				api="/v1/portal/part-number-components"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
			<field-text-field
				v-model="item.name"
				name="name"
				label="Name"
				:rules="[$rules.required('name')]"
			></field-text-field>

			<vc-select
				v-model="item.product_id"
				name="product_id"
				api="/v1/lookups/products"
				label="Product"
				:rules="[$rules.required('product_id')]"
			></vc-select>

			<vc-select
				v-model="item.specification_id"
				name="specification_id"
				api="/v1/lookups/product-specifications"
				label="Specification"
				:rules="[$rules.required('specification_id')]"
			></vc-select>

			<field-text-field
				v-model="item.value"
				name="value"
				label="Value"
				:rules="[$rules.required('value')]"
			></field-text-field>

			
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "Product",
					sortable: true,
					value: "product",
				},
				{
					text: "Specification",
					sortable: true,
					value: "specification",
				},
				{
					text: "Name",
					sortable: true,
					value: "name",
				},
			    {
			    	text: "Created Date",
			    	sortable: true,
			    	value: "created_at",
			    },
				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],
			defaultItem: {
				id: null,
				name: null,
				specification: {},
				product: {},
			},
		};
	},
	computed: {},
	watch: {},
	
	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

