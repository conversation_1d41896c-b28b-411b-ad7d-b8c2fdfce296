<template>
	<page title="Insurance Companies" desc="">
		<template #actions>
			<v-btn v-if="checkPermission('insurance_companies.create')" color="success" @click="$refs.crud.new()">
				<v-icon left>mdi-plus</v-icon>Add New
			</v-btn>
		</template>
		<div>
			<data-table
				v-if="checkPermission('insurance_companies.view')"
				ref="dataTable"
				:columns="columns"
				api="/v1/portal/insurance-companies"
			>
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn
							v-if="checkPermission('insurance_companies.delete')"
							v-tooltip="'Delete'"
							small
							icon
							@click="$refs.crud.delete(item.id)"
						>
							<v-icon small>mdi-delete</v-icon>
						</v-btn>
						<v-btn
							v-if="checkPermission('insurance_companies.update')"
							v-tooltip="'Edit'"
							small
							icon
							@click="$refs.crud.edit(item.id)"
						>
							<v-icon small>mdi-pencil</v-icon>
						</v-btn>
					</div>
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>
			</data-table>
			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>

			<crud
				ref="crud"
				v-slot="{ item }"
				width="400"
				:default="defaultItem"
				item-name="Insurance Company"
				api="/v1/portal/insurance-companies"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<field-text-field
					v-model="item.name"
					name="name"
					label="Name"
					:rules="[$rules.required('Insurance name')]"
				></field-text-field>

				<field-text-field
					v-model="item.email"
					name="email"
					label="Email"
					:rules="[$rules.required('Insurance email')]"
				></field-text-field>

				<field-text-field
					v-model="item.phone"
					name="phone"
					label="Phone"
					:rules="[$rules.required('Insurance phone')]"
				></field-text-field>
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "Insurance Name",
					sortable: true,
					value: "name",
				},
				{
					text: "Insurance Email",
					sortable: true,
					value: "email",
				},
				{
					text: "Insurance Phone",
					sortable: true,
					value: "phone",
				},
				{
					text: "Created at",
					sortable: true,
					value: "created_at",
				},
				{
					text: "Updated at",
					sortable: true,
					value: "updated_at",
				},
				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],
			defaultItem: {
				id: null,
				name: null,
				email: null,
				phone: null,
			},
		};
	},
	computed: {},
	watch: {},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>


