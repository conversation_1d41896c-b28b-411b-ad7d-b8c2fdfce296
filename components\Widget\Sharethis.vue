<template>
	<section>
		<div class="sticky flex">
			<div class="sharethis-inline-share-buttons"></div>
		</div>
	</section>
</template>

<script>
export default {
	name: "Sharethi<PERSON>",
	head() {
		return {
			script: [
				{
					hid: "sharethis",
					src: "//platform-api.sharethis.com/js/sharethis.js#property=58bfebc5638205001181e9d8&product=inline-share-buttons",
					body: true,
				},
			],
		};
	},

	mounted() {
		if (window.__sharethis__) window.__sharethis__.initialize();
	},
};
</script>
