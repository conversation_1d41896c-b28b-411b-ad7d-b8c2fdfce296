<template>
	<page title="Price History" desc="">
		<div>
			<data-table
				v-if="checkPermission('reports.price_history')"
				ref="dataTable"
				:columns="columns"
				api="/v1/portal/reports/price-history"
			>
				<template #filter="{ models }">
					<v-text-field v-model="models.quote_id" name="quote_id" label="Quote ID" clearable></v-text-field>
					<field-date key="data" v-model="models.quote_date" label="Quote Date" clearable></field-date>
				</template>

				<template #item.quote_date="{ item }">
					{{ item.quote_date | dateTime }}
				</template>
			</data-table>
			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "Quote Id",
					sortable: true,
					value: "quote_id",
				},
				{
					text: "Reference Number",
					sortable: true,
					value: "quote_reference_number",
				},
				{
					text: "Client Name",
					sortable: true,
					value: "client_name",
				},
				{
					text: "Markup Price",
					sortable: true,
					value: "markup_price",
				},
				{
					text: "Markup Type",
					sortable: true,
					value: "markup_type",
				},
				{
					text: "Factory Price",
					sortable: true,
					value: "factory_price",
				},
				{
					text: "Assumed Price",
					sortable: true,
					value: "assumed_price",
				},
				{
					text: "Unit Price",
					sortable: true,
					value: "unit_price",
				},
				{
					text: "Total",
					sortable: true,
					value: "total",
				},
				{
					text: "Quantity",
					sortable: true,
					value: "quantity",
				},
				{
					text: "Quote Date",
					sortable: true,
					value: "quote_date",
				},
			],
		};
	},
	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

