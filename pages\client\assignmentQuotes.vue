<template>
	<page title="Assignments" desc="">
		<template #actions>
			<v-btn v-if="checkPermission('quotes.create')" color="success" :to="localePath({ name: 'new-quotes' })">
				<v-icon left>mdi-plus</v-icon>Add quote
			</v-btn>
		</template>
		<div>
			<data-table v-if="checkPermission('quotes.view')" ref="dataTable" :sortable="true" :expanded.sync="expanded"
				:columns="columns" :item-class="getRowClass" show-expand api="/v1/portal/quotes">
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-if="checkPermission('quotes.delete')" v-tooltip="'Delete'" small icon
							@click="deleteQuote(item.id)">
							<v-icon small>mdi-delete</v-icon>
						</v-btn>
						<v-btn v-if="checkPermission('quotes.update')" v-tooltip="'Edit Quote'" small icon
							:to="localePath({ name: 'new-quotes', params: { quote_id: item.id } })">
							<v-icon small>mdi-eye</v-icon>
						</v-btn>

						<v-btn v-tooltip="'Clone Quote'" small icon @click="cloneFunction(item.id)">
							<v-icon small>mdi-content-copy</v-icon>
						</v-btn>
					</div>
				</template>

				<template #expanded-item="{ item }">
					<template v-if="item && item.revisions && item.revisions.length > 0">
						<td style="background: #fbf3e6">
							<div class="my-3">
								<p v-for="rev in item.revisions" :key="rev.id" class="mb-5">{{ rev.id }}</p>
							</div>
						</td>
						<td style="background: #fbf3e6">
							<div class="my-3">
								<!-- <p v-for="rev in item.revisions" :key="rev.id" class="mb-5">{{ rev.reference_number }}</p> -->

								<v-btn v-for="rev in item.revisions" :key="rev.id" text class="mb-1"
									:to="localePath({ name: 'new-quotes', params: { quote_id: rev.id } })">{{
										rev.reference_number }}</v-btn>
							</div>
						</td>
						<td style="background: #fbf3e6">
							<div class="my-3">
								<p v-for="rev in item.revisions" :key="rev.id" class="mb-5">{{
									rev.product_category?.name }}</p>
							</div>
						</td>
						<td style="background: #fbf3e6">
							<div class="my-3">
								<p v-for="rev in item.revisions" :key="rev.id" class="mb-5">{{ rev.factory?.name }}</p>
							</div>
						</td>
						<td style="background: #fbf3e6">
							<div class="my-3">
								<p v-for="rev in item.revisions" :key="rev.id" class="mb-5">{{ rev.client?.name }}</p>
							</div>
						</td>
						<td style="background: #fbf3e6">
							<div class="my-3">
								<div v-for="rev in item.revisions" :key="rev.id" class="d-flex align-items-center mb-1">
									<img width="25" class="mt-n3"
										:src="'/flag-icons/flags/4x3/' + rev.country?.code_2.toLowerCase() + '.svg'" />
									<p class="ml-2">{{ rev.country?.name }}</p>
								</div>
							</div>
						</td>
						<td style="background: #fbf3e6">
							<div v-for="rev in item.revisions" :key="rev.id" class="my-3">
								<v-chip v-if="rev.status === 0" small> Draft </v-chip>
								<v-chip v-else-if="rev.status === 1" small outlined color="orange"
									style="margin-bottom: 5.5px">
									Under Review
								</v-chip>
								<v-chip v-else-if="rev.status === 2" small outlined color="orange"
									style="margin-bottom: 5.5px">
									Under Approval
								</v-chip>
								<v-chip v-else-if="rev.status === 3" small outlined color="green"
									style="margin-bottom: 5.5px">Approved
								</v-chip>
								<v-chip v-else-if="rev.status === 4" small outlined color="purple"
									style="margin-bottom: 5.5px">Tender
								</v-chip>
								<v-chip v-else-if="rev.status === 5" small outlined color="blue"
									style="margin-bottom: 5.5px">Achieved
								</v-chip>
								<v-chip v-else-if="rev.status === 6" small outlined color="red"
									style="margin-bottom: 5.5px">On Going
								</v-chip>
								<v-chip v-else-if="rev.status === 7" small outlined color="red"
									style="margin-bottom: 5.5px">Lost </v-chip>
								<v-chip v-else-if="rev.status === 8" small outlined color="red"
									style="margin-bottom: 5.5px">Dead </v-chip>
								<v-chip v-else-if="rev.status === 9" small outlined color="orange"
									style="margin-bottom: 5.5px">
									Pre Qualification
								</v-chip>
								<v-chip v-else-if="rev.status === 10" small outlined color="orange"
									style="margin-bottom: 5.5px">
									Material Submittal
								</v-chip>
								<v-chip v-else-if="rev.status === 11" small outlined color="orange"
									style="margin-bottom: 5.5px">
									Sample Request
								</v-chip>
								<v-chip v-else-if="rev.status === 12" small outlined color="green"
									style="margin-bottom: 5.5px">
									Expected Order
								</v-chip>
								<v-chip v-else-if="rev.status === 13" small outlined color="green"
									style="margin-bottom: 5.5px">
									Ordered
								</v-chip>
							</div>
						</td>
						<td style="background: #fbf3e6">
							<div v-for="rev in item.revisions" :key="rev.id + 'current'" class="my-3">
								<v-chip v-if="rev.current_step === 0" small style="margin-bottom: 5.5px"> Draft
								</v-chip>
								<v-chip v-else-if="rev.current_step === 1" small color="#b71f23" class="white--text"
									style="margin-bottom: 5.5px">
									Define Project</v-chip>
								<v-chip v-else-if="rev.current_step === 2" small color="orange" class="white--text"
									style="margin-bottom: 5.5px">
									Take Off
								</v-chip>
								<v-chip v-else-if="rev.current_step === 3" small color="orange" class="white--text"
									style="margin-bottom: 5.5px">Factory Costing</v-chip>
								<v-chip v-else-if="rev.current_step === 4" small color="purple" class="white--text"
									style="margin-bottom: 5.5px">Pricing
								</v-chip>
								<v-chip v-else-if="rev.current_step === 5" small color="#ebebeb" class="white--text"
									style="margin-bottom: 5.5px">Shipping
								</v-chip>
								<v-chip v-else-if="rev.current_step === 6" small color="green" class="white--text"
									style="margin-bottom: 5.5px">Payment
								</v-chip>
								<v-chip v-else-if="rev.current_step === 7" small color="green" class="white--text"
									style="margin-bottom: 5.5px">Formal Quotation</v-chip>
								<v-chip v-else-if="rev.current_step === 8" small color="green" class="white--text"
									style="margin-bottom: 5.5px">Ordered
								</v-chip>
								<v-chip v-else small style="margin-bottom: 5.5px">Undefined </v-chip>
							</div>
						</td>
						<td style="background: #fbf3e6">
							<div class="my-3">
								<p v-for="rev in item.revisions" :key="rev.id" class="mb-5">{{ rev.updated_at | date }}
								</p>
							</div>
						</td>
						<td style="background: #fbf3e6">
							<div class="my-3">
								<p v-for="rev in item.revisions" :key="rev.id" class="mb-5">{{ rev.created_at | date }}
								</p>
							</div>
						</td>
						<td style="background: #fbf3e6"></td>
						<td style="background: #fbf3e6">
							<div v-for="rev in item.revisions" :key="rev.id" class="d-flex my-3">
								<v-btn v-if="checkPermission('quotes.delete')" v-tooltip="'Delete'" small icon
									@click="deleteQuote(rev.id)">
									<v-icon small>mdi-delete</v-icon>
								</v-btn>

								<v-btn v-if="checkPermission('quotes.update')" v-tooltip="'Edit Quote'" small icon
									style="margin-bottom: 1px"
									:to="localePath({ name: 'new-quotes', params: { quote_id: rev.id } })">
									<v-icon small>mdi-eye</v-icon>
								</v-btn>

								<v-btn v-tooltip="'Clone Quote'" small icon @click="cloneFunction(item.id)">
									<v-icon small>mdi-content-copy</v-icon>
								</v-btn>
							</div>
						</td>

						<td style="background: #fbf3e6"></td>
					</template>
				</template>

				<template #filter="{ models }">
					<v-text-field key="reference_number" v-model="models.reference_number"
						label="Reference Number"></v-text-field>

					<vc-autocomplete key="product_category_id" v-model="models.product_category_id"
						api="/v1/lookups/product-categories" label="Product Category" name="product_category_id"
						item-text="text" item-value="value" clearable>
					</vc-autocomplete>

					<vc-autocomplete key="factory_id" v-model="models.factory_id" api="/v1/lookups/factories"
						label="Factory Name" name="factory_id" item-text="text" item-value="value" clearable>
					</vc-autocomplete>

					<vc-autocomplete key="client_id" v-model="models.client_id" api="/v1/lookups/clients"
						label="Client Name" name="client_id" item-text="text" item-value="value" clearable>
					</vc-autocomplete>

					<vc-autocomplete key="country_id" v-model="models.country_id" api="/v1/lookups/countries"
						label="Country" name="country_id" item-text="text" item-value="value" clearable>
					</vc-autocomplete>

					<vc-autocomplete key="quote_status" v-model="models.status" name="status" label="Quote Status"
						:items="statusItems" clearable></vc-autocomplete>

					<vc-autocomplete key="workflow_status" v-model="models.current_step" name="workflow_status"
						label="Workflow" :items="workflowItems" clearable></vc-autocomplete>

					<!-- <div class="d-flex"> -->
					<vc-autocomplete key="order_by" v-model="models.order_by" name="order_by" label="Sort By"
						:items="sortItems" item-text="name" item-value="value" clearable></vc-autocomplete>

					<!-- <vc-autocomplete
							key="order_by"
							v-model="models.order_by"
							name="order_by"
							label="Sort Type"
							:items="['ascending', 'descending']"
							clearable
						></vc-autocomplete> -->
					<!-- </div> -->

					<field-date key="data" v-model="models.created_at" label="Created at" clearable></field-date>
				</template>

				<template #item.reference_number="{ item }">
					<v-btn text :to="localePath({ name: 'new-quotes', params: { quote_id: item.id } })">{{
						item.reference_number }}</v-btn>
				</template>

				<template #item.status="{ item }">
					<div class="my-3">
						<v-chip v-if="item.status === 0" small> Draft </v-chip>
						<v-chip v-else-if="item.status === 1" small outlined color="orange"> Under Review </v-chip>
						<v-chip v-else-if="item.status === 2" small outlined color="orange"> Under Approval </v-chip>
						<v-chip v-else-if="item.status === 3" small outlined color="green">Approved </v-chip>
						<v-chip v-else-if="item.status === 4" small outlined color="purple">Tender </v-chip>
						<v-chip v-else-if="item.status === 5" small outlined color="blue">Achieved </v-chip>
						<v-chip v-else-if="item.status === 6" small outlined color="red">On Going </v-chip>
						<v-chip v-else-if="item.status === 7" small outlined color="red">Lost </v-chip>
						<v-chip v-else-if="item.status === 8" small outlined color="red">Dead </v-chip>
						<v-chip v-else-if="item.status === 9" small outlined color="orange"> Pre Qualification </v-chip>
						<v-chip v-else-if="item.status === 10" small outlined color="orange"> Material Submittal</v-chip>
						<v-chip v-else-if="item.status === 11" small outlined color="orange"> Sample Request </v-chip>
						<v-chip v-else-if="item.status === 12" small outlined color="green"> Expected Order </v-chip>
						<v-chip v-else-if="item.status === 13" small outlined color="green"> Ordered </v-chip>
					</div>
				</template>

				<template #item.current_step="{ item }">
					<div class="my-3">
						<v-chip v-if="item.current_step === 0" small> Draft </v-chip>
						<v-chip v-else-if="item.current_step === 1" small color="#b71f23" class="white--text"> Define
							Project </v-chip>
						<v-chip v-else-if="item.current_step === 2" small color="orange" class="white--text"> Take Off
						</v-chip>
						<v-chip v-else-if="item.current_step === 3" small color="orange" class="white--text">Factory
							Costing </v-chip>
						<v-chip v-else-if="item.current_step === 4" small color="purple" class="white--text">Pricing
						</v-chip>
						<v-chip v-else-if="item.current_step === 5" small color="#ebebeb" class="white--text">Shipping
						</v-chip>
						<v-chip v-else-if="item.current_step === 6" small color="green" class="white--text">Payment
						</v-chip>
						<v-chip v-else-if="item.current_step === 7" small color="green" class="white--text">Formal
							Quotation </v-chip>
						<v-chip v-else-if="item.current_step === 8" small color="green" class="white--text">Ordered
						</v-chip>
					</div>
				</template>

				<template #item.country="{ item }">
					<div class="d-flex" style="text-wrap: nowrap">
						<img width="25" class="me-2"
							:src="'/flag-icons/flags/4x3/' + item.country?.code_2.toLowerCase() + '.svg'" />
						{{ item.country.name }}
					</div>
				</template>

				<template #item.client="{ item }">
					<div class="d-flex" style="text-wrap: nowrap">
						{{ item.client.name }}
					</div>
				</template>

				<template #item.isRevision="{ item }">
					<div v-if="item.revisions && item.revisions.length">
						<v-icon size="33" color="success">mdi-check</v-icon>
					</div>
					<div v-else><v-icon size="18">mdi-minus</v-icon></div>
				</template>

				<template #item.updated_at="{ item }">
					<div style="min-width: 110px">{{ item.updated_at | date }}</div>
				</template>

				<template #item.created_at="{ item }">
					<div style="min-width: 110px">{{ item.created_at | date }}</div>
				</template>
			</data-table>
			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "Reference Number",
					sortable: true,
					value: "reference_number",
				},
				{
					text: "Product Category",
					sortable: true,
					value: "product_category.name",
				},
				{
					text: "Factory",
					sortable: true,
					value: "factory.name",
				},
				{
					text: "Client",
					sortable: true,
					value: "client",
				},
				{
					text: "Country",
					sortable: true,
					value: "country",
				},
				{
					text: "Status",
					sortable: true,
					value: "status",
				},
				{
					text: "Workflow",
					sortable: true,
					value: "current_step",
				},
				{
					text: "Last Updated At",
					sortable: true,
					value: "updated_at",
				},
				{
					text: "Created At",
					sortable: true,
					value: "created_at",
				},
				{ text: "Has Revision", value: "isRevision" },
				{
					text: "",
					sortable: true,
					value: "actions",
				},
				{ text: "", value: "data-table-expand" },
			],
			expanded: [],
			statusItems: [
				{
					text: "Draft",
					value: 0,
				},
				{
					text: "Under Review",
					value: 1,
				},
				{
					text: "Under Approval",
					value: 2,
				},
				{
					text: "Approved",
					value: 3,
				},
				{
					text: "Tender",
					value: 4,
				},
				{
					text: "Achieved",
					value: 5,
				},
				{
					text: "On Going",
					value: 6,
				},
				{
					text: "Lost",
					value: 7,
				},
				{
					text: "Dead",
					value: 8,
				},
				{
					text: "Pre Qualification",
					value: 9,
				},
				{
					text: "Material submittal",
					value: 10,
				},
				{
					text: "Sample request",
					value: 11,
				},
				{
					text: "Expected order",
					value: 12,
				},
				{
					text: "Ordered",
					value: 13,
				},
			],
			workflowItems: [
				{
					text: "Define Project",
					value: 1,
				},
				{
					text: "Take Off",
					value: 2,
				},
				{
					text: "Factory Costing",
					value: 3,
				},
				{
					text: "Pricing",
					value: 4,
				},
				{
					text: "Shipping",
					value: 5,
				},
				{
					text: "Payment",
					value: 6,
				},
				{
					text: "Formal Quotation",
					value: 7,
				},
				{
					text: "Ordered",
					value: 8,
				},
			],
			sortItems: [
				{
					name: "Reference Number",
					value: "reference_number",
				},
				{
					name: "Product Category",
					value: "product_category_id",
				},
				{
					name: "Factory",
					value: "factory_id",
				},
				{
					name: "Country",
					value: "country_id",
				},
				{
					name: "Client",
					value: "client_id",
				},
				{
					name: "Status",
					value: "status",
				},
				{
					name: "Workflow",
					value: "current_step",
				},
				{
					name: "Last Updated At",
					value: "updated_at",
				},
				{
					name: "Created At",
					value: "created_at",
				},
			],
		};
	},

	methods: {
		getRowClass(item) {
			return item.status === 3 ? 'approved-row' : '';
		},

		refresh() {
			this.$refs.dataTable.refresh();
		},

		deleteQuote(item) {
			this.$confirm(null, { title: "Are you sure to delete the item?" }).then((isAccepted) => {
				if (isAccepted) {
					this.$axios
						.$delete(`/v1/portal/quotes/${item}`)
						.then((resp) => {
							this.$toast.success("has been successfully");
							this.refresh();
						})
						.catch(this.genericErrorHandler);
				}
			});
		},

		cloneFunction(item) {
			this.$confirm(null, { title: "Are you sure to cloning the item?" }).then((isAccepted) => {
				if (isAccepted) {
					this.$axios
						.$post(`/v1/portal/quotes/${item}/clone`)
						.then((resp) => {
							this.$toast.success("has been successfully");
							this.refresh();
						})
						.catch(this.genericErrorHandler);
				}
			});
		},
	},
};
</script>

<style scoped>
::v-deep .approved-row {
	background-color: #e8f5e8 !important;
}

::v-deep .approved-row:hover {
	background-color: #d4edda !important;
}
</style>
