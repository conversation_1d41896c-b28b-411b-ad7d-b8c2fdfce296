<template>
	<page title="Delivery Methods" desc="">
		<template #actions>
			<v-btn v-if="checkPermission('delivery_methods.create')" color="success" @click="$refs.crud.new()">
				<v-icon left>mdi-plus</v-icon>Add Cities
			</v-btn>
		</template>
		<div>
			<data-table
				v-if="checkPermission('delivery_methods.view')"
				ref="dataTable"
				:columns="columns"
				api="/v1/portal/delivery-methods"
			>
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn
							v-if="checkPermission('delivery_methods.delete')"
							v-tooltip="'Delete'"
							small
							icon
							@click="$refs.crud.delete(item.id)"
						>
							<v-icon small>mdi-delete</v-icon>
						</v-btn>
						<v-btn
							v-if="checkPermission('delivery_methods.update')"
							v-tooltip="'Edit'"
							small
							icon
							@click="$refs.crud.edit(item.id)"
						>
							<v-icon small>mdi-pencil</v-icon>
						</v-btn>
					</div>
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>
			</data-table>
			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>

			<crud
				ref="crud"
				v-slot="{ item }"
				width="400"
				:default="defaultItem"
				item-name="Delivery Methods"
				api="/v1/portal/delivery-methods"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<v-text-field v-model="item.name" name="name" label="Name" :rules="[$rules.required('name')]"></v-text-field>

				<vc-autocomplete
					v-model="item.branch_id"
					name="branch_id"
					item-text="text"
					item-value="value"
					api="/v1/lookups/branches"
					label="Company Branch Name"
					:rules="[$rules.required('branch_id')]"
				></vc-autocomplete>
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "Name",
					sortable: true,
					value: "name",
				},
				{
					text: "Company Branch",
					sortable: true,
					value: "branch.name",
				},
				{
					text: "Created at",
					sortable: true,
					value: "created_at",
				},
				{
					text: "Updated at",
					sortable: true,
					value: "updated_at",
				},
				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],
			defaultItem: {
				id: null,
				name: null,
				branch_id: null,
			},
		};
	},
	computed: {},
	watch: {},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

