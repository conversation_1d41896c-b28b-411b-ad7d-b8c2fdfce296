<template>
	<page :tabs="tabs" title="Order">
		<template #actions>
			<div id="actions"></div>
		</template>
		<nuxt-child ref="child" />
	</page>
</template>

<script>
export default {
	data() {
		return {
			tabs: [
				{
					text: "Purchase Order",
					to: this.localePath({ name: "show-order-po", params: { order_id: this.$route.params.order_id } }),
				},
				{
					text: "Pick Ticket",
					to: this.localePath({ name: "show-order-pt", params: { order_id: this.$route.params.order_id } }),
				},
				{
					text: "Invoice",
					to: this.localePath({ name: "show-order-invoice", params: { order_id: this.$route.params.order_id } }),
				},
				{
					text: "Proforma Invoice",
					to: this.localePath({ name: "show-order-proforma-invoice", params: { order_id: this.$route.params.order_id } }),
				},
				// {
				// 	text: "Order Acknowledgement",
				// 	to: this.localePath({ name: "show-order-acknowledgement", params: { order_id: this.$route.params.order_id } }),
				// },
			],
		};
	},
	methods: {
		newItem() {
			this.$refs.child.refs.crud.new();
		},
	},
};
</script>

