const globalOptions = {
	right: true, // default
	bottom: true, // default
	color: "info", // default
	icon: "mdi-information", // default
	iconColor: "", // default
	classes: ["body-2"],
	timeout: 3000, // default
	dismissable: true, // default
	multiLine: false, // default
	vertical: false, // default
	queueable: false, // default
	showClose: false, // default
	closeText: "", // default
	closeIcon: "mdi-close", // default
	closeColor: "", // default
	slot: [], // default
	shorts: {
		success: {
			color: "success",
			icon: "mdi-check",
		},
		error: {
			color: "error",
			icon: "mdi-alert-circle",
			timeout: 0,
			showClose: true,
		},
		warning: {
			color: "warning",
			icon: "mdi-alert",
			timeout: 10000,
			showClose: true,
		},
		info: {
			color: "info",
			icon: "mdi-information",
			timeout: 4000,
			showClose: true,
		},
		notification: {
			color: "primary",
			icon: "mdi-bell",
			timeout: 0,
			showClose: true,
		},
		debug: {
			color: "grey",
			icon: "mdi-bug",
			timeout: 0,
			showClose: true,
		},
	},
	title: null,
};

let cmp = null;
const queue = [];

// Show toast function
function show(message, options = {}) {
	if (!cmp) {
		const toast = document.getElementById("toast");
		cmp = toast?.__vue__;
	}

	if (cmp) {
		options.message = message;
		cmp.add({ ...globalOptions, ...options });
	} else {
		console.error("Toast component is not initialized.");
	}
}

// Function to return short methods for different toast types
function shorts(options) {
	const colors = ["success", "info", "error", "warning"];
	const methods = {};

	colors.forEach((color) => {
		methods[color] = (message, options) => show(message, { color, ...options });
	});

	if (options.shorts) {
		for (const key in options.shorts) {
			const localOptions = options.shorts[key];
			methods[key] = (message, options) => show(message, { ...localOptions, ...options });
		}
	}

	return methods;
}

// Get the toast component (for accessing methods like clearing toasts)
function getCmp() {
	return cmp;
}

// Clear the toast queue
function clearQueue() {
	return queue.splice(0, queue.length);
}

// Clear all toasts, both active and queued
function clear() {
	const cmpInstance = getCmp(); // Ensure cmp is initialized properly.

	if (cmpInstance && typeof cmpInstance.clear === "function") {
		cmpInstance.clear(); // Call the clear method to dismiss all toasts.
	} else {
		// Log the message for debugging
		console.warn("Toast component or clear method not available. Clearing the queue.");

		// If the clear method isn't available, hide all active toast messages manually.
		const toastElements = document.querySelectorAll(".v-snack__wrapper"); // Adjust the class if needed
   
    toastElements.forEach((toast) => {
      toast.remove(); // Remove the toast element from the DOM
    });
    
		// toastElements.forEach((element) => {
		// 	element.style.bottom = "0"; // Add bottom: 0 to each toast element
		// });
		// toastElements.forEach((toast) => {
		// 	toast.style.display = "none"; // Hide the toast element
		// });

		clearQueue(); // Clear the queue.
	}
}

export default (_context, inject) => {
	const toast = { globalOptions, getCmp, clearQueue, clear }; // Include the `clear` method.
	const methods = shorts(globalOptions);
	for (const key in methods) {
		toast[key] = methods[key];
	}
	inject("toast", toast);
};
