<template>
	<v-row justify="center">
		<v-dialog v-model="localDialog" fullscreen hide-overlay transition="dialog-bottom-transition">
			<v-card>
				<v-toolbar rounded="0" dark color="dark">
					<v-btn icon dark @click="closeDialog">
						<v-icon>mdi-close</v-icon>
					</v-btn>
					<v-spacer></v-spacer>
					<v-toolbar-items>
						<v-menu rounded="rounded" offset-y>
							<template #activator="{ attrs, on }">
								<v-btn dark text class="white--text" v-bind="attrs" v-on="on">
									<v-icon left>mdi-download</v-icon> Download
								</v-btn>
							</template>
							<v-list>
								<v-list-item link @click="exportPdf(pdfName)">
									<v-list-item-title>As PDF</v-list-item-title>
								</v-list-item>
								<v-list-item link @click="captureScreenshot(pdfName)">
									<v-list-item-title>As Image</v-list-item-title>
								</v-list-item>
							</v-list>
						</v-menu>
					</v-toolbar-items>
				</v-toolbar>

				<template v-if="pdfName === 'pi'">
					<pdfs-pi :pdf-data="pdfData" />
				</template>
				<template v-if="pdfName === 'po'">
					<pdfs-po :pdf-data="pdfData" />
				</template>
				<template v-if="pdfName === 'ci'">
					<pdfs-ci :pdf-data="pdfData" :extra-data="extraData" />
				</template>
				<template v-if="pdfName === 'pt'">
					<pdfs-pt :pdf-data="pdfData" />
				</template>
				<template v-if="pdfName === 'oa'">
					<pdfs-oa :pdf-data="pdfData" :extra-data="extraData" />
				</template>
				<template v-if="pdfName === 'cin'">
					<pdfs-cin :pdf-data="pdfData" />
				</template>
			</v-card>
		</v-dialog>
	</v-row>
</template>

<script>
import html2canvas from "html2canvas";
import JsPDF from "jspdf";

export default {
	props: {
		pdfData: {
			type: Object,
			default: () => ({}),
		},
		extraData: {
			type: Object,
			default: () => ({}),
		},
		pdfName: {
			type: String,
			default: "",
		},
		generalDialog: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			localDialog: this.generalDialog,
		};
	},
	watch: {
		generalDialog(val) {
			this.localDialog = val;
		},
		localDialog(val) {
			this.$emit("update:generalDialog", val);
		},
	},
	methods: {
		closeDialog() {
			this.localDialog = false;
		},

		// New PDF Function
		exportPdf(element) {
			const divsToHide = document.getElementsByClassName("hidden-on-copy"); // divsToHide is an array
			const dataFooter = document.getElementsByClassName("v-data-footer"); // divsToHide is an array

			for (let i = 0; i < divsToHide.length; i++) {
				divsToHide[i].style.display = "none"; // depending on what you're doing
			}
			for (let i = 0; i < dataFooter.length; i++) {
				dataFooter[i].style.display = "none"; // depending on what you're doing
			}

			// just this export code
			// const doc = new JsPDF();
			const doc = new JsPDF("p", "mm", "a4");
			const section = document.getElementById(element);

			html2canvas(section, { scale: 3 }) // Increase the scale value for better quality
				.then((canvas) => {
					const imgData = canvas.toDataURL("image/png");

					doc.addImage(imgData, "PNG", 10, 10, 190, 0);

					// Save the PDF file
					doc.save(`${this.pdfData.reference_number}.pdf`);
				});
			// end code

			setTimeout(() => {
				for (let i = 0; i < divsToHide.length; i++) {
					divsToHide[i].style.display = "table-cell"; // depending on what you're doing
				}
				for (let i = 0; i < dataFooter.length; i++) {
					dataFooter[i].style.display = "flex"; // depending on what you're doing
				}
			}, 300);
		},

		captureScreenshot(element) {
			html2canvas(document.querySelector("#" + element)).then((canvas) => {
				const dataURL = canvas.toDataURL("image/png");
				const a = document.createElement("a");
				a.href = dataURL;
				a.download = `${this.pdfData.reference_number}.png`;
				a.click();
			});
		},
	},
};
</script>
