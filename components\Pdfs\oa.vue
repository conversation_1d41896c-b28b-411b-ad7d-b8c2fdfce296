<template>
	<div id="oa" style="background: #fff">
		<v-container class="pa-8" style="padding: 20px">
			<v-row class="mb-4">
				<v-col md="6">
					<v-img src="/logo.png" width="350px" style="width: 250px"></v-img>

					<div>
						JAFZA ONE, Tower B , 13th Floor, Suite BB1302 P.O Box 17046, JEBEL ALI, Dubai, UAE Tel: +971 (04) 268 4666
						<EMAIL>
					</div>
				</v-col>
				<v-col md="6">
					<div>
						<div class="text-center mb-4">
							<h1 class="font-weight-bold display-1">ORDER ACKNOWLEDGEMENT</h1>
						</div>
						<div class="custom-table table">
							<table width="100%">
								<tr style="background: #ecebeb">
									<th>Sales Order #</th>
									<th>Order Date</th>
									<th>Customer #</th>
								</tr>
								<tr class="text-center">
									<td>{{ extraData.client_po_number }}</td>
									<td>{{ extraData.client_po_date | date }}</td>
									<td>{{ extraData.quote?.client?.name }}</td>
								</tr>
								<tr style="background: #ecebeb; text-align: center">
									<td colspan="3">Purchase Order</td>
								</tr>
								<tr style="text-align: center">
									<td colspan="3">-</td>
								</tr>
							</table>
						</div>
					</div>
				</v-col>
			</v-row>

			<v-row class="relative">
				<v-col md="12" cols="12">
					<div>
						<div class="custom-table table">
							<table width="100%">
								<tr>
									<td colspan="12">
										<table style="width: 100%" class="text-center">
											<tr style="background: #ecebeb">
												<th style="width: 20%">Terms</th>
												<th style="width: 20%">Collect/Prepaid</th>
												<th style="width: 20%">Carrier</th>
											</tr>
											<tr>
												<td style="width: 20%">
													<div class="mb-0 pb-0" v-html="extraData.client_payment_details"></div>
												</td>
												<td style="width: 20%">-</td>
												<td style="width: 20%">-</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr style="background: #ecebeb">
									<th>Bill To</th>
									<th>ship To</th>
								</tr>
								<tr>
									<td>
										<p class="mb-0">{{ extraData.quote?.client?.name }}</p>
										<p class="mb-2">{{ extraData.quote?.client?.email }}</p>

										<div style="display: flex; align-items: center" class="mb-2">
											<p class="font-weight-bold me-2 mb-0">Attention of:</p>
											<p class="mb-0">{{ extraData.bill_to_data?.name }}</p>
										</div>

										<div style="display: flex; align-items: center" class="mb-2">
											<p class="font-weight-bold me-2 mb-0">Phone:</p>
											<p class="mb-0">{{ extraData.bill_to_data?.phone }}</p>
										</div>

										<div style="display: flex; align-items: center" class="mb-2">
											<p class="font-weight-bold me-2 mb-0">Mobile:</p>
											<p class="mb-0">{{ extraData.bill_to_data?.mobile }}</p>
										</div>

										<div style="display: flex; align-items: center" class="mb-2">
											<p class="font-weight-bold me-2 mb-0">Fax:</p>
											<p class="mb-0">{{ extraData.bill_to_data?.fax }}</p>
										</div>

										<div style="display: flex; align-items: center" class="mb-2">
											<p class="font-weight-bold me-2 mb-0">Email:</p>
											<p class="mb-0">{{ extraData.bill_to_data?.email }}</p>
										</div>
									</td>
									<td>
										<p class="mb-0">{{ extraData.quote?.client?.name }}</p>
										<p class="mb-2">{{ extraData.quote?.client?.email }}</p>

										<div style="display: flex; align-items: center" class="mb-2">
											<p class="font-weight-bold me-2 mb-0">Attention of:</p>
											<p class="mb-0">{{ extraData.ship_to_data?.name }}</p>
										</div>

										<div style="display: flex; align-items: center" class="mb-2">
											<p class="font-weight-bold me-2 mb-0">Phone:</p>
											<p class="mb-0">{{ extraData.ship_to_data?.phone }}</p>
										</div>

										<div style="display: flex; align-items: center" class="mb-2">
											<p class="font-weight-bold me-2 mb-0">Mobile:</p>
											<p class="mb-0">{{ extraData.ship_to_data?.mobile }}</p>
										</div>

										<div style="display: flex; align-items: center" class="mb-2">
											<p class="font-weight-bold me-2 mb-0">Fax:</p>
											<p class="mb-0">{{ extraData.ship_to_data?.fax }}</p>
										</div>

										<div style="display: flex; align-items: center" class="mb-2">
											<p class="font-weight-bold me-2 mb-0">Email:</p>
											<p class="mb-0">{{ extraData.ship_to_data?.email }}</p>
										</div>
									</td>
								</tr>
								<tr style="background: #ecebeb">
									<th>Shipping Instructions</th>
									<th>Delivery Notes</th>
								</tr>
								<tr>
									<td><div v-html="extraData.quote?.delivery_method_terms"></div></td>
                  <td>-</td>
								
								</tr>
							</table>
						</div>
					</div>
				</v-col>

				<!-- <pre>
          {{ pdfData }}
        </pre> -->

				<!-- <pre>
          {{ extraData }}
        </pre> -->

				<v-col cols="12">
					<v-card outlined>
						<v-card-text>
							<v-simple-table class="border">
								<thead style="background: #232323">
									<tr>
										<!-- <th class="text-left" style="color: #fff">All Quantity</th> -->
										<th class="text-left" style="color: #fff">Qty Ordered</th>
										<th class="text-left" style="color: #fff">Readiness Date</th>
										<th class="text-left" style="color: #fff">Item Description</th>
										<th class="text-left" style="color: #fff">Item Price</th>
										<th class="text-left" style="color: #fff">UOM</th>
										<th class="text-left" style="color: #fff">Extended Price</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="item in pdfData.order?.quote?.products" :key="item.id">
										<template v-if="!item.is_optional && item.acknowledge_quantity">
											<!-- <td>{{ item.quantity }}</td> -->
											<td>{{ item.acknowledge_quantity }}</td>
											<td>{{ pdfData.delivery_to_client_at | date }}</td>
											<td>{{ item.description }}</td>
											<td>
												{{
													extraData.quote?.client_currency?.symbol
														? extraData.quote?.client_currency?.symbol
														: "USD"
												}}
												{{ item.unit_price }}
											</td>
											<td>{{ item.uom }}</td>
											<td>
												{{
													extraData.quote?.client_currency?.symbol
														? extraData.quote?.client_currency?.symbol
														: "USD"
												}}
												{{ item.unit_price * item.acknowledge_quantity }}
											</td>
										</template>
									</tr>
								</tbody>
							</v-simple-table>
						</v-card-text>
					</v-card>
				</v-col>
			</v-row>

			<v-row class="fill-height mt-0">
				<v-col cols="8">
					<div>
						<p class="font-weight-bold">
							Dear Customer,This document acknowledges receipt of your order. Please review the information and advise of any
							errors or disagreements you have at your earliest convenience. For fastest service, write or call us at the
							address and phone number printed above. Please refer to our Order Number and your Purchase Order number in all
							correspondence.
						</p>
					</div>
				</v-col>

				<v-spacer />

				<v-col md="3" cols="12" class="mt-0 pt-0">
					<v-card outlined color="#ecebeb" rounded="0">
						<v-card-text>
							<v-list-item dense>
								<v-list-item-content class="mb-0 pb-0">
									<v-list-item-title>Subtotal</v-list-item-title>
								</v-list-item-content>
								<v-list-item-content class="mb-0 pb-0 text-end pe-4">
									<v-list-item-title class="font-weight-bold">
										{{ extraData.quote?.client_currency?.symbol ? extraData.quote?.client_currency?.symbol : "USD" }}
										{{ totalPrice }}
									</v-list-item-title>
								</v-list-item-content>
							</v-list-item>
							<v-divider />

							<v-divider v-if="pdfData.discount" />
							<v-list-item dense>
								<v-list-item-content class="mb-0 pb-0">
									<v-list-item-title>Total</v-list-item-title>
								</v-list-item-content>
								<v-list-item-content class="mb-0 pb-0 text-end pe-4">
									<v-list-item-title class="font-weight-bold">
										{{ extraData.quote?.client_currency?.symbol ? extraData.quote?.client_currency?.symbol : "USD" }}
										{{ totalPrice }}
									</v-list-item-title>
								</v-list-item-content>
							</v-list-item>
						</v-card-text>
					</v-card>
					<!-- <p class="mt-3 font-weight-bold">{{ extraData.quote?.total_spell }}</p> -->
				</v-col>
			</v-row>
		</v-container>
	</div>
</template>

<script>
export default {
	props: {
		pdfData: {
			type: Object,
			default: () => ({}),
		},
		extraData: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			//	totalPrice: 0,
		};
	},

	computed: {
		totalPrice() {
			let total = 0;
			if (this.pdfData.order?.quote?.products) {
				for (const item of this.pdfData.order.quote.products) {
					total += item.unit_price * item.acknowledge_quantity;
				}
			}
			return total;
		},
	},
};
</script>

<style lang="scss">
.custom-table table,
.custom-table th,
.custom-table td {
	border: 1px solid #ecebeb;
	border-collapse: collapse;
}
.custom-table th,
.custom-table td {
	padding: 8px;
}
.custom-table {
	border: 1px solid #ecebeb;
	border-collapse: collapse;
}
.custom-table th,
.custom-table td {
	border: 1px solid #ecebeb;
	border-collapse: collapse;
}
</style>
