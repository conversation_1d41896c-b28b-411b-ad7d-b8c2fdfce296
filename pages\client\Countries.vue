<template>
	<page title="Countries" desc="">
		<template #actions>
			<v-btn v-if="checkPermission('countries.create')" color="success" @click="$refs.crud.new()">
				<v-icon left>mdi-plus</v-icon>Add Countries
			</v-btn>
		</template>
		<div>
			<data-table v-if="checkPermission('countries.view')" ref="dataTable" :columns="columns" api="/v1/portal/countries">
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn
							v-if="checkPermission('countries.delete')"
							v-tooltip="'Delete'"
							small
							icon
							@click="$refs.crud.delete(item.id)"
						>
							<v-icon small>mdi-delete</v-icon>
						</v-btn>
						<v-btn v-if="checkPermission('countries.update')" v-tooltip="'Edit'" small icon @click="$refs.crud.edit(item.id)">
							<v-icon small>mdi-pencil</v-icon>
						</v-btn>
					</div>
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>
			</data-table>
			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>

			<crud
				ref="crud"
				v-slot="{ item }"
				width="400"
				:default="defaultItem"
				item-name="Country"
				api="/v1/portal/countries"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<field-text-field v-model="item.name" name="name" label="Name" :rules="[$rules.required('name')]"></field-text-field>

				<field-text-field
					v-model="item.code"
					name="code"
					label="Country Code"
					:rules="[$rules.required('code')]"
				></field-text-field>

				<field-text-field
					v-model="item.phone_code"
					name="phone_code"
					label="Phone Code"
					:rules="[$rules.required('phone_code')]"
				></field-text-field>

				<field-text-field
					v-model="item.region"
					name="region"
					label="Region"
					:rules="[$rules.required('region')]"
				></field-text-field>

				<field-text-field v-model="item.language" name="language" label="Language"></field-text-field>

				<h4 class="mb-3 mt-6">Delivery Ports:</h4>

				<v-card v-for="(field, i) in item.delivery_ports" :key="field.id" outlined class="mb-3">
					<v-card-text>
						<v-btn icon small class="float-end ms-1" color="primary" @click="item.delivery_ports.splice(i, 1)">
							<v-icon>mdi-trash-can</v-icon>
						</v-btn>
						<v-text-field :key="'name' + i" v-model="field.name" label="Port Name"> </v-text-field>
						<v-text-field :key="'code' + i" v-model="field.code" label="Port code"> </v-text-field>
						<vc-autocomplete
							:key="'delivery_method_id' + i"
							v-model="field.delivery_method_id"
							api="/v1/lookups/delivery-methods"
							label="Delivery Method"
							name="delivery_method_id"
							item-text="text"
							item-value="value"
						>
						</vc-autocomplete>
					</v-card-text>
				</v-card>
				<v-btn
					v-tooltip="'add new'"
					class="success mt-3"
					small
					dence
					@click="
						item.delivery_ports.push({
							name: null,
							code: null,
							delivery_method_id: null,
						})
					"
				>
					<v-icon left>mdi-plus</v-icon>
					New Port
				</v-btn>
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "Country Name",
					sortable: true,
					value: "name",
				},
				{
					text: "Country Code",
					sortable: true,
					value: "code",
				},
				{
					text: "Created at",
					sortable: true,
					value: "created_at",
				},
				{
					text: "Updated at",
					sortable: true,
					value: "updated_at",
				},
				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],
			defaultItem: {
				id: null,
				name: null,
				code: null,
				phone_code: null,
				region: null,
				language: null,
				delivery_ports: [],
			},
		};
	},
	computed: {},
	watch: {},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

