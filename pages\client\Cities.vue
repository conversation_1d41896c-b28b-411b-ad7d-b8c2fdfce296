<template>
	<page title="Cities" desc="">
		<template #actions>
			<v-btn v-if="checkPermission('cities.create')" color="success" @click="$refs.crud.new()">
				<v-icon left>mdi-plus</v-icon>Add Cities
			</v-btn>
		</template>
		<div>
			<data-table v-if="checkPermission('cities.view')" ref="dataTable" :columns="columns" api="/v1/portal/cities">
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-if="checkPermission('cities.delete')" v-tooltip="'Delete'" small icon @click="$refs.crud.delete(item.id)">
							<v-icon small>mdi-delete</v-icon>
						</v-btn>
						<v-btn v-if="checkPermission('cities.update')" v-tooltip="'Edit'" small icon @click="$refs.crud.edit(item.id)">
							<v-icon small>mdi-pencil</v-icon>
						</v-btn>
					</div>
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>
			</data-table>
			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>

			<crud
				ref="crud"
				v-slot="{ item }"
				width="400"
				:default="defaultItem"
				item-name="City"
				api="/v1/portal/cities"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<field-text-field
					v-model="item.name"
					name="name"
					label="City Name"
					:rules="[$rules.required('city name')]"
				></field-text-field>

				<vc-autocomplete
					v-model="item.country_id"
					name="country_id"
					item-text="text"
					item-value="value"
					api="/v1/lookups/countries"
					label="Country"
					:rules="[$rules.required('country_id')]"
				></vc-autocomplete>
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "City Name",
					sortable: true,
					value: "name",
				},
				{
					text: "Country",
					sortable: true,
					value: "country_id",
				},
				{
					text: "Created at",
					sortable: true,
					value: "created_at",
				},
				{
					text: "Updated at",
					sortable: true,
					value: "updated_at",
				},
				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],
			defaultItem: {
				id: null,
				name: null,
				country_id: null,
			},
		};
	},
	computed: {},
	watch: {},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

