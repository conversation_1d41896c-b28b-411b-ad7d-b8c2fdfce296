<template>
	<v-card :loading="$fetchState.pending" class="fill-height">
		<v-card-text>
			<widget-line-chart :series="series" @interval-updated="setChartInterval">
				<template #title>
					<div class="d-flex align-center">
						<div class="title me-4">Registration Chart</div>
					</div>
				</template>

			</widget-line-chart>
			<!-- <div class="pa-4">
				<v-img contain src="/images/capture.PNG" alt="" />
			</div> -->
		</v-card-text>
	</v-card>
</template>

<script>
import marketAnalysis from "~/assets/market-analysis.json";
export default {
	data() {
		return {
			marketAnalysis, // : { pivot_points: {} },
			series: [],
			currentInterval: "1m",
		};
	},
	async fetch() {
		return await new Promise((resolve, reject) => {
			/* eslint-disable-next-line */
			setTimeout(() => {
				// this.marketAnalysis = marketAnalysis;
				this.series = [
					{
						type: "line",
          
						data: this.marketAnalysis?.data?.map((item) => {
							return {
								x: item.date,
								y: item.open,
							};
						}),
					},
				];
				resolve();
			}, 1000);
		});
	},
	computed: {
		tabs() {
			return this.marketAnalysis?.instruments?.map((item) => ({
				text: item,
				price: (Math.random() * 100).toFixed(2),
				to: this.localePath({ name: "market-analysis-instrument", params: { instrument: item } }),
			}));
		},
	},
	methods: {
		setChartInterval(interval) {
			this.currentInterval = interval;
			this.$fetch();
		},
	},
};
</script>

