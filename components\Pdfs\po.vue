<template>
	<div id="po" style="background: #fff">
		<v-container class="pa-8" style="padding: 20px">
			<v-row>
				<v-col md="6">
					<v-img src="/logo.png" width="350px" style="width: 250px"></v-img>
				</v-col>
				<v-col md="6">
					<div class="text-end" style="text-align: right">
						<div style="font-weight: bold; font-size: 1.6rem">PURCHASE ORDER</div>
						<div style="font-weight: bold">-</div>
						<div style="font-weight: bold">
							Date:
							{{ pdfData.client_po_date | date }}
						</div>
					</div>
				</v-col>
			</v-row>

			<v-divider class="my-3" />

			<v-row class="relative">
				<v-col md="6" cols="12">
					<div>
						<div style="margin-bottom: 8px">
							<h4 class="font-weight-bold" style="background: #ebebeb; padding-left: 3px; margin-right: 5px">PROJECT</h4>
							<span class="d-inline-block coffee-3--text body-2">{{ pdfData.quote?.project_name }}</span>
						</div>
					</div>
				</v-col>
				<v-col md="6" cols="12">
					<div>
						<div style="margin-bottom: 25px">
							<div style="margin-bottom: 8px">
								<h4 class="font-weight-bold" style="background: #ebebeb; padding-left: 3px; margin-right: 5px">PRODUCT</h4>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.quote?.product_category?.name }}</span>
							</div>

							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block">Quote Ref. #:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.quote?.reference_number }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block">Quote Date:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.quote?.valid_until_date | date }}</span>
							</div>
						</div>
					</div>
				</v-col>
				<v-col md="6" cols="12">
					<div>
						<div style="margin-bottom: 25px; margin-top: 5px">
							<h4 class="font-weight-bold" style="background: #ebebeb; padding-left: 3px; margin-right: 5px">BILL TO</h4>
							<div class="d-inline-block coffee-3--text body-2">{{ pdfData.quote?.client?.name }}</div>
							<div class="d-inline-block coffee-3--text body-2">{{ pdfData.quote?.client?.email }}</div>

							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Attention of:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.bill_to_data?.name }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Phone:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.bill_to_data?.phone }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Mobile:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.bill_to_data?.mobile }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Fax:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.bill_to_data?.fax }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Email:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.bill_to_data?.email }}</span>
							</div>
						</div>
					</div>
				</v-col>

				<v-col md="6" cols="12">
					<div>
						<div style="margin-bottom: 25px; margin-top: 5px">
							<h4 class="font-weight-bold" style="background: #ebebeb; padding-left: 3px; margin-right: 5px">SHIP TO</h4>

							<div class="d-inline-block coffee-3--text body-2">{{ pdfData.quote?.client?.name }}</div>
							<div class="d-inline-block coffee-3--text body-2">{{ pdfData.quote?.client?.email }}</div>

							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Attention of:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.ship_to_data?.name }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Phone:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.ship_to_data?.phone }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Mobile:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.ship_to_data?.mobile }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Fax:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.ship_to_data?.fax }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Email:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.ship_to_data?.email }}</span>
							</div>
						</div>
					</div>
				</v-col>

				<v-col cols="12">
					<v-card outlined>
						<v-card-text>
							<v-simple-table class="border">
								<thead style="background: #232323">
									<tr>
										<th class="text-left" style="color: #fff">Quantity</th>
										<th class="text-left" style="color: #fff">UOM</th>
										<th class="text-left" style="color: #fff">Part # / Description</th>
										<th class="text-left" style="color: #fff">Unit Price</th>
										<th class="text-left" style="color: #fff">Total</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="item in pdfData.quote.products" :key="item.id">
										<template v-if="!item.is_optional">
											<td>{{ item.quantity }}</td>
											<td>{{ item.uom }}</td>

											<td>
												<div>
													{{ item.part_number }}
												</div>
												<div>
													{{ item.description }}
												</div>
											</td>
											<td>
												{{
													pdfData.quote?.client_currency?.symbol ? pdfData.quote?.client_currency?.symbol : "USD"
												}}
												{{ item.unit_price }}
											</td>
											<td>
												{{
													pdfData.quote?.client_currency?.symbol ? pdfData.quote?.client_currency?.symbol : "USD"
												}}
												{{ item.total }}
											</td>
										</template>
									</tr>
								</tbody>
							</v-simple-table>
						</v-card-text>
					</v-card>
				</v-col>
			</v-row>

			<v-row class="fill-height mt-0">
				<v-col cols="8">
					<div class="mt-6">
						<h3 class="font-weight-bold mb-3">Terms and Conditions:</h3>
						<div class="coffee-3--text body-2" v-html="pdfData.payment_method_terms"></div>
					</div>
					<div class="mt-6 d-flex">
						<h4 class="font-weight-bold mb-3 me-2">Payment:</h4>
						<span v-html="pdfData.client_payment_details"> </span>
					</div>
					<div class="mt-0 d-flex">
						<h4 class="font-weight-bold mb-3 me-2">Packing:</h4>
						<span v-html="pdfData.quote?.packaging_details"> </span>
					</div>
					<div class="mt-0 mb-3 d-flex">
						<h4 class="font-weight-bold me-10">Note:</h4>
						<div>
							Advance copy of documents shall be sent to AREEL via email for check - Original invoice and certificate of
							origin shall be manually rubber stamped only ,Any original documets with electronic stamps stamping will not be
							accepted by custom.
						</div>
					</div>
					<div class="mt-0 d-flex">
						<h4 class="font-weight-bold mb-3 me-2">Warranty:</h4>
						<div>To be provided with 5 years against manufacturing defects starting from date of delivery.</div>
					</div>
				</v-col>

				<v-spacer />

				<v-col md="3" cols="12" class="mt-0 pt-0">
					<v-card outlined color="#ecebeb" rounded="0">
						<v-card-text>
							<v-list-item dense>
								<v-list-item-content class="mb-0 pb-0">
									<v-list-item-title>Subtotal</v-list-item-title>
								</v-list-item-content>
								<v-list-item-content class="mb-0 pb-0 text-end pe-4">
									<v-list-item-title class="font-weight-bold">
										{{ pdfData.quote?.client_currency?.symbol ? pdfData.quote?.client_currency?.symbol : "USD" }}
										{{ pdfData.quote?.subtotal }}
									</v-list-item-title>
								</v-list-item-content>
							</v-list-item>
							<v-divider />

							<v-list-item v-if="pdfData.quote?.tax_label && pdfData.quote?.tax" dense>
								<v-list-item-content class="mb-0 pb-0">
									<v-list-item-title class="text-capitalize">{{ pdfData.quote?.tax_label }}</v-list-item-title>
								</v-list-item-content>
								<v-list-item-content class="mb-0 pb-0 text-end pe-4">
									<v-list-item-title class="font-weight-bold"> %{{ pdfData.quote?.tax }} </v-list-item-title>
								</v-list-item-content>
							</v-list-item>
							<v-divider v-if="pdfData.quote?.tax_label && pdfData.quote?.tax" />

							<v-list-item v-if="pdfData.quote?.discount" dense>
								<v-list-item-content class="mb-0 pb-0">
									<v-list-item-title>Special Discount</v-list-item-title>
								</v-list-item-content>
								<v-list-item-content class="mb-0 pb-0 text-end pe-4">
									<v-list-item-title class="font-weight-bold">%{{ pdfData.quote?.discount }}</v-list-item-title>
								</v-list-item-content>
							</v-list-item>
							<v-divider v-if="pdfData.quote?.discount" />
							<v-list-item dense>
								<v-list-item-content class="mb-0 pb-0">
									<v-list-item-title>Total</v-list-item-title>
								</v-list-item-content>
								<v-list-item-content class="mb-0 pb-0 text-end pe-4">
									<v-list-item-title class="font-weight-bold">
										{{ pdfData.quote?.client_currency?.symbol ? pdfData.quote?.client_currency?.symbol : "USD" }}
										{{ pdfData.quote?.total }}
									</v-list-item-title>
								</v-list-item-content>
							</v-list-item>
						</v-card-text>
					</v-card>
					<p class="mt-3 font-weight-bold">{{ pdfData.quote?.total_spell }}</p>
				</v-col>
			</v-row>

			<v-divider class="my-6" />

			<div class="text-center">
				<span class="body-2">
					United Arab Emirates, JAFZA ONE, Tower B, 13th Floor, Suite BB1302, JEBEL ALI, P.O Box 17046, Dubai JEBEL ALI, P.O Box
					17046, Dubai, UAE, <EMAIL>, Tel: +971 (04) 268 4666 Jordan, Amman, King Abdullah II Street, Complex
					No. 150, office 313 + 314 <EMAIL>, Tel.:+962 6 5371119, Fax :+962 6 5378889, Mob.:+962 79 6022771/2
				</span>
			</div>
		</v-container>
	</div>
</template>

<script>
export default {
	props: {
		pdfData: {
			type: Object,
			default: () => ({}),
		},
	},
};
</script>




