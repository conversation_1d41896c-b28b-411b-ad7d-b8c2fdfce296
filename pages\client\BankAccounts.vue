<template>
	<page title="Bank Accounts" desc="">
		<template #actions>
			<v-btn v-if="checkPermission('bank_accounts.create')" color="success" @click="$refs.crud.new()">
				<v-icon left>mdi-plus</v-icon>Add Bank Account
			</v-btn>
		</template>
		<div>
			<data-table v-if="checkPermission('bank_accounts.view')" ref="dataTable" :columns="columns" api="/v1/portal/bank-accounts">
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn
							v-if="checkPermission('bank_accounts.delete')"
							v-tooltip="'Delete'"
							small
							icon
							@click="$refs.crud.delete(item.id)"
						>
							<v-icon small>mdi-delete</v-icon>
						</v-btn>
						<v-btn
							v-if="checkPermission('bank_accounts.update')"
							v-tooltip="'Edit'"
							small
							icon
							@click="$refs.crud.edit(item.id)"
						>
							<v-icon small>mdi-pencil</v-icon>
						</v-btn>
					</div>
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>
			</data-table>
			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>

			<crud
				ref="crud"
				v-slot="{ item }"
				width="400"
				:default="defaultItem"
				item-name="Bank Account"
				api="/v1/portal/bank-accounts"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<field-text-field
					v-model="item.bank_name"
					name="bank_name"
					label="Bank Name"
					:rules="[$rules.required('bank name')]"
				></field-text-field>

				<field-text-field
					v-model="item.account_number"
					name="account_number"
					label="Account Number"
					:rules="[$rules.required('account number')]"
				></field-text-field>

				<field-text-field
					v-model="item.account_name"
					name="account_name"
					label="Account Name"
					:rules="[$rules.required('Account Name')]"
				></field-text-field>

				<field-text-field
					v-model="item.branch_name"
					name="branch_name"
					label="Bank Branch Name"
					:rules="[$rules.required('branch name')]"
				></field-text-field>

				<field-text-field v-model="item.iban" name="iban" label="Iban" :rules="[$rules.required('iban')]"></field-text-field>

				<field-text-field v-model="item.swift_code" name="swift_code" label="Swift Code"></field-text-field>

				<vc-autocomplete
					v-model="item.country_id"
					name="country_id"
					item-text="text"
					item-value="value"
					api="/v1/lookups/countries"
					label="Country"
					:rules="[$rules.required('country_id')]"
				></vc-autocomplete>

				<vc-autocomplete
					v-model="item.currency_id"
					name="currency_id"
					item-text="text"
					item-value="value"
					api="/v1/lookups/currencies"
					label="Currency"
					:rules="[$rules.required('currency_id')]"
				></vc-autocomplete>

				<vc-autocomplete
					v-model="item.branch_id"
					name="branch_id"
					item-text="text"
					item-value="value"
					api="/v1/lookups/branches"
					label="Company Branch Name"
					:rules="[$rules.required('branch_id')]"
				></vc-autocomplete>
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "Bank Name",
					sortable: true,
					value: "bank_name",
				},
				{
					text: "Bank Branch Name",
					sortable: true,
					value: "branch_name",
				},
				{
					text: "Account Name",
					sortable: true,
					value: "account_name",
				},
				{
					text: "Iban",
					sortable: true,
					value: "iban",
				},
				{
					text: "Country",
					sortable: true,
					value: "country.code",
				},
				{
					text: "Company Branch Name",
					sortable: true,
					value: "branch.name",
				},
				{
					text: "Created at",
					sortable: true,
					value: "created_at",
				},
				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],
			defaultItem: {
				id: null,
				name: null,
				symbol: null,
			},
		};
	},
	computed: {},
	watch: {},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

