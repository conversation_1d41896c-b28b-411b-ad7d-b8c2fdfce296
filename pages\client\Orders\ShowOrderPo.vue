<template>
	<div>
		<div id="element-to-convert" style="background: #fff">
			<v-container class="pa-8" style="padding: 20px">
				<v-row>
					<v-col md="6">
						<v-img src="/logo.png" width="350px" style="width: 250px"></v-img>
					</v-col>
					<v-col md="6">
						<div class="text-end" style="text-align: right">
							<div style="font-weight: bold; font-size: 1.6rem">PURCHASE ORDER</div>
							<div style="font-weight: bold">{{ orderData.reference_number }}</div>
							<div style="font-weight: bold">{{ orderData.created_at | date }}</div>
						</div>
					</v-col>
				</v-row>

				<v-divider class="my-3" />

				<v-row class="relative">
					<div v-if="pdfHiddenElements" class="absolute end">
						<v-btn icon small text class="text-end mt-1" @click="exportToPDF"><v-icon>mdi-download</v-icon></v-btn>
					</div>

					<v-col md="6" cols="12">
						<div>
							<div style="margin-bottom: 25px; margin-top: 5px">
								<h4 class="font-weight-bold" style="margin-bottom: 6px; padding-left: 3px; background: #ebebeb">BILL TO</h4>
								<span class="d-inline-block coffee-3--text body-2">AREEL SPECIALTIES INTERNATIONAL FZCO</span>
								<span class="d-inline-block coffee-3--text body-2">JAFZA ONE, Tower B , 13th Floor, Suite BB1302</span>
								<span class="d-inline-block coffee-3--text body-2" style="margin-bottom: 8px"
									>P.O Box 17046, JEBEL ALI, Duba</span
								>

								<div style="display: flex; align-items: center; margin-bottom: 2px">
									<h5 class="d-inline-block" style="margin-right: 5px">Attention of:</h5>
									<span class="d-inline-block coffee-3--text body-2">{{
										orderData.quote?.client_contact_person?.name
									}}</span>
								</div>
								<div style="display: flex; align-items: center; margin-bottom: 2px">
									<h5 class="d-inline-block" style="margin-right: 5px">Phone:</h5>
									<span class="d-inline-block coffee-3--text body-2">
										{{ orderData.quote?.client_contact_person?.phone }}
									</span>
								</div>
								<div style="display: flex; align-items: center; margin-bottom: 2px">
									<h5 class="d-inline-block" style="margin-right: 5px">Mobile:</h5>
									<span class="d-inline-block coffee-3--text body-2">{{
										orderData.quote?.client_contact_person?.mobile
									}}</span>
								</div>
								<div style="display: flex; align-items: center; margin-bottom: 2px">
									<h5 class="d-inline-block" style="margin-right: 5px">Email:</h5>
									<span class="d-inline-block coffee-3--text body-2">{{ orderData.account_manager?.email }}</span>
								</div>
							</div>
						</div>
					</v-col>

					<v-col md="6" cols="12">
						<div>
							<div style="margin-bottom: 25px; margin-top: 5px">
								<div style="margin-bottom: 8px">
									<h4 class="font-weight-bold" style="background: #ebebeb; padding-left: 3px; margin-right: 5px">
										PRODUCT
									</h4>
									<span class="d-inline-block coffee-3--text body-2">{{ orderData.quote?.product_category?.name }}</span>
								</div>

								<div style="display: flex; align-items: center; margin-bottom: 2px">
									<h5 class="d-inline-block" style="margin-right: 5px">Quote Ref. #:</h5>
									<span class="d-inline-block coffee-3--text body-2">{{ orderData.quote?.reference_number }}</span>
								</div>
								<div style="display: flex; align-items: center; margin-bottom: 2px">
									<h5 class="d-inline-block" style="margin-right: 5px">Quote Date:</h5>
									<span class="d-inline-block coffee-3--text body-2">{{ orderData.quote?.valid_until_date | date }}</span>
								</div>
							</div>
							<div style="margin-bottom: 25px; margin-top: 5px">
								<h4 class="font-weight-bold" style="margin-bottom: 6px; background: #ebebeb; padding-left: 3px">
									SHIPPING ADDRESS
								</h4>

								<div style="display: flex; align-items: center; margin-bottom: 2px">
									<h5 class="d-inline-block"></h5>
									<span class="d-inline-block coffee-3--text body-2" v-html="orderData.shipping_instructions"> </span>
								</div>
								<div style="display: flex; align-items: center; margin-bottom: 2px">
									<h5 class="d-inline-block">-</h5>
									<span class="d-inline-block coffee-3--text body-2"></span>
								</div>
							</div>
						</div>
					</v-col>

					<v-col cols="12">
						<v-card outlined>
							<v-card-text>
								<v-simple-table class="border">
									<thead style="background: #232323">
										<tr>
											<th class="text-left" style="color: #fff">Quantity</th>
											<th class="text-left" style="color: #fff">UOM</th>
											<th class="text-left" style="color: #fff">Part # / Description</th>
											<th class="text-left" style="color: #fff">Unit Price</th>
											<th class="text-left" style="color: #fff">Total</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="item in orderData.quote?.products" :key="item">
											<template v-if="!item.is_optional">
												<td>{{ item.quantity }}</td>
												<td>{{ item.uom }}</td>

												<td>
													<div>
														{{ item.part_number }}
													</div>
													<div>
														{{ item.description }}
													</div>
												</td>
												<td>{{ item.unit_price }}</td>
												<td>{{ item.total }}</td>
											</template>
										</tr>
									</tbody>
								</v-simple-table>
							</v-card-text>
						</v-card>
					</v-col>
				</v-row>

				<v-row class="fill-height mt-0">
					<v-col cols="8">
						<div class="mt-6">
							<h4 class="font-weight-bold mb-3" style="margin-right: 5px">Terms & Conditions :</h4>
							<span class="d-inline-block coffee-3--text body-2" v-html="orderData.quote?.delivery_method_terms"></span>
						</div>
						<div class="mt-4 d-flex">
							<h4 class="font-weight-bold mb-3" style="margin-right: 5px">Payment :</h4>
							<span class="d-inline-block coffee-3--text body-2">{{ orderData.quote?.payment_method }}</span>
						</div>
						<div class="mt-4 d-flex">
							<h4 class="font-weight-bold mb-3" style="margin-right: 5px">Packing :</h4>
							<div class="d-inline-block coffee-3--text body-2" v-html="orderData.delivery_notes"></div>
						</div>
						<div class="mt-4">
							<h4 class="font-weight-bold mb-3">Notes</h4>
							<!-- <p class="caption" v-html="orderData.notes"></p> -->
							<p class="coffee-3--text body-2">-</p>
						</div>
						<div class="mt-6">
							<h4 class="font-weight-bold mb-3">Warranty</h4>
							<p class="coffee-3--text body-2">
								To be provided with 5 years against manufacturing defects starting from date of delivery
							</p>
						</div>
					</v-col>

					<v-spacer />

					<v-col md="3" cols="12" class="mt-0 pt-0">
						<v-card outlined color="#ecebeb" rounded="0">
							<v-card-content>
								<v-list-item dense>
									<v-list-item-content class="mb-0 pb-0">
										<v-list-item-title>Subtotal</v-list-item-title>
									</v-list-item-content>
									<v-list-item-content class="mb-0 pb-0 text-end pe-4">
										<v-list-item-title class="font-weight-bold">${{ orderData.quote.subtotal }}</v-list-item-title>
									</v-list-item-content>
								</v-list-item>
								<v-divider />
								<v-list-item v-if="orderData.quote.vat" dense>
									<v-list-item-content class="mb-0 pb-0">
										<v-list-item-title>Vat</v-list-item-title>
									</v-list-item-content>
									<v-list-item-content class="mb-0 pb-0 text-end pe-4">
										<v-list-item-title class="font-weight-bold">%{{ orderData.quote.vat }}</v-list-item-title>
									</v-list-item-content>
								</v-list-item>
								<v-divider v-if="orderData.quote.vat" />
								<v-list-item v-if="orderData.quote.tax" dense>
									<v-list-item-content class="mb-0 pb-0">
										<v-list-item-title>Tax</v-list-item-title>
									</v-list-item-content>
									<v-list-item-content class="mb-0 pb-0 text-end pe-4">
										<v-list-item-title class="font-weight-bold">%{{ orderData.quote.tax }}</v-list-item-title>
									</v-list-item-content>
								</v-list-item>
								<v-divider v-if="orderData.quote.tax" />
								<v-list-item v-if="orderData.quote.discount" dense>
									<v-list-item-content class="mb-0 pb-0">
										<v-list-item-title>Special Discount</v-list-item-title>
									</v-list-item-content>
									<v-list-item-content class="mb-0 pb-0 text-end pe-4">
										<v-list-item-title class="font-weight-bold">%{{ orderData.quote.discount }}</v-list-item-title>
									</v-list-item-content>
								</v-list-item>
								<v-divider v-if="orderData.quote.discount" />
								<v-list-item dense>
									<v-list-item-content class="mb-0 pb-0">
										<v-list-item-title>Total</v-list-item-title>
									</v-list-item-content>
									<v-list-item-content class="mb-0 pb-0 text-end pe-4">
										<v-list-item-title class="font-weight-bold">${{ orderData.quote.total }}</v-list-item-title>
									</v-list-item-content>
								</v-list-item>
							</v-card-content>

							<!-- <v-card-actions v-if="pdfHiddenElements">
								<v-btn small @click="$refs.crudCalculations.edit($route.params.quote_id)">Edit</v-btn>
							</v-card-actions> -->
						</v-card>
					</v-col>
				</v-row>
				<v-divider style="margin-top: 50px" />

				<v-row>
					<v-col class="text-center" style="font-size: 14px; text-align: center">
						<div style="margin-top: 15px">
							United Arab Emirates, JAFZA ONE, Tower B, 13th Floor, Suite BB1302, JEBEL ALI, P.O Box 17046, Dubai
						</div>
						<div>JEBEL ALI, P.O Box 17046, Dubai, UAE, <EMAIL>, Tel: +971 (04) 268 4666</div>
						<div>Jordan, Amman, King Abdullah II Street, Complex No. 150, office 313 + 314</div>
						<div><EMAIL>, Tel.:+962 6 5371119, Fax :+962 6 5378889, Mob.:+962 79 6022771/2</div>
					</v-col>
				</v-row>
			</v-container>

			<!-- <crud
				ref="crudCalculations"
				v-slot="{ item }"
				width="400"
				:default="orderData"
				item-name="Discount"
				api="/v1/portal/quotes"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<vc-text-field
					v-model="item.discount"
					@input="discountConvertInt(item.discount)"
					name="discount"
					outlined
					label="Discount"
				></vc-text-field>

				<vc-text-field v-model="item.tax" @input="taxConvertInt(item.tax)" name="tax" outlined label="Tax"></vc-text-field>

				<vc-text-field v-model="item.vat" @input="vatConvertInt(item.vat)" name="vat" outlined label="Vat"></vc-text-field>
			</crud> -->
		</div>
	</div>
</template>

<script>
import html2pdf from "html2pdf.js";
export default {
	data() {
		return {
			pdfHiddenElements: true,
			calculationsBtn: true,

			orderData: {
				id: 4,
				reference_number: null,
				status: 0,
				factory_quote_number: null,
				factory_quote_date: null,
				shipping_instructions: null,
				delivery_notes: null,
				created_at: null,
				updated_at: null,
				terms: null,
				quote_id: 1,
				account_manager_id: null,
				quote: {
					id: null,
					reference_number: null,
					status: null,
					valid_until: null,
					valid_until_date: null,
					payment_method: 0,
					payment_method_terms:null,
					delivery_method: 1,
					delivery_method_terms: null,
					delivery_address: null,
					notes: null,
					created_at:null,
					updated_at:null,
					client_contact_person_id: null,
					product_category_id: null,
					parent_id: null,
					total: null,
					subtotal: null,
					discount: null,
					tax: null,
					vat: null,
					client_contact_person: {
						id: 2,
						name: null,
						email_one: null,
						email_two: null,
						phone: null,
						mobile: null,
						fax:null,
						position: null,
						address_one: null,
						address_two: null,
						created_at: null,
						updated_at: null,
						client: {
							id: 1,
							name: null,
							email:null,
							created_at: null,
							updated_at: null,
						},
					},
					product_category: {
						id: null,
						name: null,
						slug: null,
						code: null,
						photo: null,
						description: null,
						created_at: null,
						updated_at: null,
					},
				},
				account_manager: {
					id: 5,
					name: null,
					email: null,
					created_at: null,
					updated_at: null,
				},
				proforma_invoice: {
					id: 3,
					reference_number: null,
					trn_number: null,
					terms: null,
					created_at: null,
					updated_at: null,
					bank_accounts: [
						{
							id: 1,
							account_number: null,
							bank_name: null,
							branch_name: null,
							account_name:null,
							iban: null,
							swift_code: null,
							currency: null,
							created_at: null,
							updated_at: null,
						},
						{
							id: 2,
							account_number: null,
							bank_name: null,
							branch_name: null,
							account_name:null,
							iban: null,
							swift_code: null,
							currency: null,
							created_at: null,
							updated_at: null,
						},
					],
					revisions: [],
				},
				commercial_invoice: {
					id: null,
					reference_number:null,
					invoice_number: null,
					invoice_date: null,
					created_at: null,
					updated_at: null,
					terms: null,
					revisions: [],
				},
				pick_ticket: {
					id: null,
					reference_number: null,
					terms: null,
					created_at: null,
					updated_at: null,
					revisions: [],
				},
				acknowledgement_invoice: {
					id: 3,
					reference_number: null,
					terms: null,
					notes: null,
					created_at: null,
					updated_at: null,
					revisions: [],
				},
			},
		};
	},

	async fetch() {
		await this.$axios.$get(`/v1/portal/orders/${this.$route.params.order_id}`).then((resp) => {
			this.orderData = resp;
		});
	},

	methods: {
		exportToPDF() {
			this.pdfHiddenElements = false;
			html2pdf(document.getElementById("element-to-convert"), {
				margin: 1,
				filename: "qoute.pdf",
			});

			setTimeout(() => {
				this.pdfHiddenElements = true;
			}, 2000);
		},

		discountConvertInt(data) {
			this.orderData.discount = data;
		},
		taxConvertInt(data) {
			this.orderData.tax = data;
		},
		vatConvertInt(data) {
			this.orderData.vat = data;
		},

		refresh() {
			this.$axios.$get(`/v1/portal/quotes/${this.$route.params.quote_id}`).then((resp) => {
				this.orderData = resp;
			});
		},
	},
};
</script>
