<template>
	<div v-if="checkPermission('dashboard.stats')">
		<v-row>
			<v-col cols="12" md="3">
				<v-card class="mx-auto" max-width="600">
					<v-card-title>
						<v-icon color="green lighten-3" class="mr-12" size="64" @click="takePulse">mdi-note-check </v-icon>
						<v-row align="start">
							<div class="mt-2">
								<span class="text-h4 font-weight-black" v-text="statsData.tender_or_draft_quotes"></span>
								<div class="text-caption grey--text">Tender / Draft Quotes</div>
							</div>
						</v-row>
						<v-spacer></v-spacer>
					</v-card-title>
				</v-card>
			</v-col>

			<v-col cols="12" md="3">
				<v-card class="mx-auto" max-width="600">
					<v-card-title>
						<v-icon color="#ffad21ba" class="mr-12" size="64" @click="takePulse"> mdi-archive-clock </v-icon>
						<v-row align="start">
							<div class="mt-2">
								<span class="text-h4 font-weight-black" v-text="statsData.on_going_quotes"></span>
								<div class="text-caption grey--text">On Going Quotes</div>
							</div>
						</v-row>

						<v-spacer></v-spacer>
					</v-card-title>
				</v-card>
			</v-col>

			<v-col cols="12" md="3">
				<v-card class="mx-auto" max-width="600">
					<v-card-title>
						<v-icon color="blue lighten-3" class="mr-12" size="64" @click="takePulse"> mdi-package-down</v-icon>
						<v-row align="start">
							<div class="mt-2">
								<span class="text-h4 font-weight-black" v-text="statsData.achieved_quotes"></span>
								<div class="text-caption grey--text">Achieved Quotes</div>
							</div>
						</v-row>

						<v-spacer></v-spacer>
					</v-card-title>
				</v-card>
			</v-col>

			<v-col cols="12" md="3">
				<v-card class="mx-auto" max-width="600">
					<v-card-title>
						<v-icon color="red lighten-3" class="mr-12" size="64" @click="takePulse"> mdi-archive-off</v-icon>
						<v-row align="start">
							<div class="mt-2">
								<span class="text-h4 font-weight-black" v-text="statsData.lost_or_dead_quotes"></span>
								<div class="text-caption grey--text">Lost / Dead Quotes</div>
							</div>
						</v-row>

						<v-spacer></v-spacer>
					</v-card-title>
				</v-card>
			</v-col>

			<!-- <v-col cols="12" md="3">
				<v-card class="mx-auto" color="white lighten-4" max-width="600">
					<v-card-title class="pb-0">
						<div class="mt-2">
							<span class="text-h4 font-weight-black">140,550</span>
							<strong>USD</strong>
							<div class="text-subtitle-1 grey--text">Notional Value</div>
						</div>

						<v-spacer></v-spacer>

						<v-btn icon class="align-self-start" size="28">
							<v-icon>mdi-arrow-right-thick</v-icon>
						</v-btn>
					</v-card-title>
					<v-sheet color="transparent">
						<sparkline
							:key="String(avg)"
							:fill="true"
							:smooth="8"
							:padding="0"
							:line-width="2"
							:value="[1, 2, 4, 3, 6, 5, 6]"
							:gradient="['#226f4e']"
							:gradient-fill="['#226f4e', '#226f4e', '#226f4e']"
							auto-draw
							stroke-linecap="round"
						></sparkline>
					</v-sheet>
				</v-card>
			</v-col> -->
		</v-row>

		<v-row>
			<v-col cols="12" md="9">
				<widget-heat-map class="fill-height"> </widget-heat-map>
			</v-col>
			<v-col cols="12" md="3">
				<widget-donut-chart class="mb-6"> </widget-donut-chart>
			</v-col>
		</v-row>
	</div>
	<!-- quotes.assignments -->
	<div v-else-if="checkPermission('dashboard.assignments')">
		<page title="My Assigned Quotes" desc="">
			<data-table
				v-if="checkPermission('quotes.view')"
				ref="dataTable"
				:sortable="true"
				:expanded.sync="expanded"
				:columns="columns"
				show-expand
				api="/v1/portal/quotes"
			>
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-tooltip="'Edit Quote'" small icon :to="localePath({ name: 'new-quotes', params: { quote_id: item.id } })">
							<v-icon small>mdi-eye</v-icon>
						</v-btn>
					</div>
				</template>

				<template #expanded-item="{ item }">
					<template v-if="item && item.revisions && item.revisions.length > 0">
						<td style="background: #fbf3e6">
							<div class="my-3">
								<p v-for="rev in item.revisions" :key="rev.id" class="mb-5">{{ rev.id }}</p>
							</div>
						</td>
						<td style="background: #fbf3e6">
							<div class="my-3">
								<v-btn
									v-for="rev in item.revisions"
									:key="rev.id"
									text
									class="mb-1"
									:to="localePath({ name: 'new-quotes', params: { quote_id: rev.id } })"
									>{{ rev.reference_number }}</v-btn
								>
							</div>
						</td>
						<td style="background: #fbf3e6">
							<div class="my-3">
								<p v-for="rev in item.revisions" :key="rev.id" class="mb-5">{{ rev.product_category?.name }}</p>
							</div>
						</td>
						<td style="background: #fbf3e6">
							<div class="my-3">
								<p v-for="rev in item.revisions" :key="rev.id" class="mb-5">{{ rev.factory?.name }}</p>
							</div>
						</td>
						<td style="background: #fbf3e6">
							<div class="my-3">
								<p v-for="rev in item.revisions" :key="rev.id" class="mb-5">{{ rev.client?.name }}</p>
							</div>
						</td>
						<td style="background: #fbf3e6">
							<div class="my-3">
								<div v-for="rev in item.revisions" :key="rev.id" class="d-flex align-items-center mb-1">
									<img
										width="25"
										class="mt-n3"
										:src="'/flag-icons/flags/4x3/' + rev.country?.code_2.toLowerCase() + '.svg'"
									/>
									<p class="ml-2">{{ rev.country?.name }}</p>
								</div>
							</div>
						</td>
						<td style="background: #fbf3e6">
							<div v-for="rev in item.revisions" :key="rev.id" class="my-3">
								<v-chip v-if="rev.status === 0" small> Draft </v-chip>
								<v-chip v-else-if="rev.status === 1" small outlined color="orange" style="margin-bottom: 5.5px">
									Under Review
								</v-chip>
								<v-chip v-else-if="rev.status === 2" small outlined color="orange" style="margin-bottom: 5.5px">
									Under Approval
								</v-chip>
								<v-chip v-else-if="rev.status === 3" small outlined color="green" style="margin-bottom: 5.5px"
									>Approved
								</v-chip>
								<v-chip v-else-if="rev.status === 4" small outlined color="purple" style="margin-bottom: 5.5px"
									>Tender
								</v-chip>
								<v-chip v-else-if="rev.status === 5" small outlined color="blue" style="margin-bottom: 5.5px"
									>Achieved
								</v-chip>
								<v-chip v-else-if="rev.status === 6" small outlined color="red" style="margin-bottom: 5.5px"
									>On Going
								</v-chip>
								<v-chip v-else-if="rev.status === 7" small outlined color="red" style="margin-bottom: 5.5px">Lost </v-chip>
								<v-chip v-else-if="rev.status === 8" small outlined color="red" style="margin-bottom: 5.5px">Dead </v-chip>
								<v-chip v-else-if="rev.status === 9" small outlined color="orange" style="margin-bottom: 5.5px">
									Pre Qualification
								</v-chip>
								<v-chip v-else-if="rev.status === 10" small outlined color="orange" style="margin-bottom: 5.5px">
									Material Submittal
								</v-chip>
								<v-chip v-else-if="rev.status === 11" small outlined color="orange" style="margin-bottom: 5.5px">
									Sample Request
								</v-chip>
								<v-chip v-else-if="rev.status === 12" small outlined color="green" style="margin-bottom: 5.5px">
									Expected Order
								</v-chip>
								<v-chip v-else-if="rev.status === 13" small outlined color="green" style="margin-bottom: 5.5px">
									Ordered
								</v-chip>
							</div>
						</td>
						<td style="background: #fbf3e6">
							<div v-for="rev in item.revisions" :key="rev.id + 'current'" class="my-3">
								<v-chip v-if="rev.current_step === 0" small style="margin-bottom: 5.5px"> Draft </v-chip>
								<v-chip
									v-else-if="rev.current_step === 1"
									small
									color="#b71f23"
									class="white--text"
									style="margin-bottom: 5.5px"
								>
									Define Project</v-chip
								>
								<v-chip
									v-else-if="rev.current_step === 2"
									small
									color="orange"
									class="white--text"
									style="margin-bottom: 5.5px"
								>
									Take Off
								</v-chip>
								<v-chip
									v-else-if="rev.current_step === 3"
									small
									color="orange"
									class="white--text"
									style="margin-bottom: 5.5px"
									>Factory Costing</v-chip
								>
								<v-chip
									v-else-if="rev.current_step === 4"
									small
									color="purple"
									class="white--text"
									style="margin-bottom: 5.5px"
									>Pricing
								</v-chip>
								<v-chip
									v-else-if="rev.current_step === 5"
									small
									color="#ebebeb"
									class="white--text"
									style="margin-bottom: 5.5px"
									>Shipping
								</v-chip>
								<v-chip
									v-else-if="rev.current_step === 6"
									small
									color="green"
									class="white--text"
									style="margin-bottom: 5.5px"
									>Payment
								</v-chip>
								<v-chip
									v-else-if="rev.current_step === 7"
									small
									color="green"
									class="white--text"
									style="margin-bottom: 5.5px"
									>Formal Quotation</v-chip
								>
								<v-chip
									v-else-if="rev.current_step === 8"
									small
									color="green"
									class="white--text"
									style="margin-bottom: 5.5px"
									>Ordered
								</v-chip>
								<v-chip v-else small style="margin-bottom: 5.5px">Undefined </v-chip>
							</div>
						</td>
						<td style="background: #fbf3e6">
							<div class="my-3">
								<p v-for="rev in item.revisions" :key="rev.id" class="mb-5">{{ rev.updated_at | date }}</p>
							</div>
						</td>
						<td style="background: #fbf3e6">
							<div class="my-3">
								<p v-for="rev in item.revisions" :key="rev.id" class="mb-5">{{ rev.created_at | date }}</p>
							</div>
						</td>
						<td style="background: #fbf3e6"></td>
						<td style="background: #fbf3e6">
							<div v-for="rev in item.revisions" :key="rev.id" class="d-flex my-3">
								<v-btn
									v-if="checkPermission('quotes.update')"
									v-tooltip="'Edit Quote'"
									small
									icon
									style="margin-bottom: 1px"
									:to="localePath({ name: 'new-quotes', params: { quote_id: rev.id } })"
								>
									<v-icon small>mdi-eye</v-icon>
								</v-btn>
							</div>
						</td>
						<td style="background: #fbf3e6"></td>
					</template>
				</template>

				<template #filter="{ models }">
					<v-text-field key="reference_number" v-model="models.reference_number" label="Reference Number"></v-text-field>

					<vc-autocomplete
						key="product_category_id"
						v-model="models.product_category_id"
						api="/v1/lookups/product-categories"
						label="Product Category"
						name="product_category_id"
						item-text="text"
						item-value="value"
						clearable
					>
					</vc-autocomplete>

					<vc-autocomplete
						key="factory_id"
						v-model="models.factory_id"
						api="/v1/lookups/factories"
						label="Factory Name"
						name="factory_id"
						item-text="text"
						item-value="value"
						clearable
					>
					</vc-autocomplete>

					<vc-autocomplete
						key="client_id"
						v-model="models.client_id"
						api="/v1/lookups/clients"
						label="Client Name"
						name="client_id"
						item-text="text"
						item-value="value"
						clearable
					>
					</vc-autocomplete>

					<vc-autocomplete
						key="country_id"
						v-model="models.country_id"
						api="/v1/lookups/countries"
						label="Country"
						name="country_id"
						item-text="text"
						item-value="value"
						clearable
					>
					</vc-autocomplete>

					<vc-autocomplete
						key="quote_status"
						v-model="models.status"
						name="status"
						label="Quote Status"
						:items="statusItems"
						clearable
					></vc-autocomplete>

					<vc-autocomplete
						key="workflow_status"
						v-model="models.current_step"
						name="workflow_status"
						label="Workflow"
						:items="workflowItems"
						clearable
					></vc-autocomplete>

					<!-- <div class="d-flex"> -->
					<vc-autocomplete
						key="order_by"
						v-model="models.order_by"
						name="order_by"
						label="Sort By"
						:items="sortItems"
						item-text="name"
						item-value="value"
						clearable
					></vc-autocomplete>

					<!-- <vc-autocomplete
							key="order_by"
							v-model="models.order_by"
							name="order_by"
							label="Sort Type"
							:items="['ascending', 'descending']"
							clearable
						></vc-autocomplete> -->
					<!-- </div> -->

					<field-date key="data" v-model="models.created_at" label="Created at" clearable></field-date>
				</template>

				<template #item.reference_number="{ item }">
					<v-btn text :to="localePath({ name: 'new-quotes', params: { quote_id: item.id } })">{{ item.reference_number }}</v-btn>
				</template>

				<template #item.status="{ item }">
					<div class="my-3">
						<v-chip v-if="item.status === 0" small> Draft </v-chip>
						<v-chip v-else-if="item.status === 1" small outlined color="orange"> Under Review </v-chip>
						<v-chip v-else-if="item.status === 2" small outlined color="orange"> Under Approval </v-chip>
						<v-chip v-else-if="item.status === 3" small outlined color="green">Approved </v-chip>
						<v-chip v-else-if="item.status === 4" small outlined color="purple">Tender </v-chip>
						<v-chip v-else-if="item.status === 5" small outlined color="blue">Achieved </v-chip>
						<v-chip v-else-if="item.status === 6" small outlined color="red">On Going </v-chip>
						<v-chip v-else-if="item.status === 7" small outlined color="red">Lost </v-chip>
						<v-chip v-else-if="item.status === 8" small outlined color="red">Dead </v-chip>
						<v-chip v-else-if="item.status === 9" small outlined color="orange"> Pre Qualification </v-chip>
						<v-chip v-else-if="item.status === 10" small outlined color="orange"> Material Submittal </v-chip>
						<v-chip v-else-if="item.status === 11" small outlined color="orange"> Sample Request </v-chip>
						<v-chip v-else-if="item.status === 12" small outlined color="green"> Expected Order </v-chip>
						<v-chip v-else-if="item.status === 13" small outlined color="green"> Ordered </v-chip>
					</div>
				</template>

				<template #item.current_step="{ item }">
					<div class="my-3">
						<v-chip v-if="item.current_step === 0" small> Draft </v-chip>
						<v-chip v-else-if="item.current_step === 1" small color="#b71f23" class="white--text"> Define Project </v-chip>
						<v-chip v-else-if="item.current_step === 2" small color="orange" class="white--text"> Take Off </v-chip>
						<v-chip v-else-if="item.current_step === 3" small color="orange" class="white--text">Factory Costing </v-chip>
						<v-chip v-else-if="item.current_step === 4" small color="purple" class="white--text">Pricing </v-chip>
						<v-chip v-else-if="item.current_step === 5" small color="#ebebeb" class="white--text">Shipping </v-chip>
						<v-chip v-else-if="item.current_step === 6" small color="green" class="white--text">Payment </v-chip>
						<v-chip v-else-if="item.current_step === 7" small color="green" class="white--text">Formal Quotation </v-chip>
						<v-chip v-else-if="item.current_step === 8" small color="green" class="white--text">Ordered </v-chip>
					</div>
				</template>

				<template #item.country="{ item }">
					<div class="d-flex">
						<img width="25" class="me-2" :src="'/flag-icons/flags/4x3/' + item.country?.code_2.toLowerCase() + '.svg'" />
						{{ item.country.name }}
					</div>
				</template>

				<template #item.client="{ item }">
					<div class="d-flex">
						{{ item.client.name }}
					</div>
				</template>

				<template #item.isRevision="{ item }">
					<div v-if="item.revisions && item.revisions.length">
						<v-icon size="33" color="success">mdi-check</v-icon>
					</div>
					<div v-else><v-icon size="18">mdi-minus</v-icon></div>
				</template>

				<template #item.updated_at="{ item }">
					<div style="min-width: 110px">{{ item.updated_at | date }}</div>
				</template>

				<template #item.created_at="{ item }">
					<div style="min-width: 110px">{{ item.created_at | date }}</div>
				</template>
			</data-table>
		</page>
	</div>
	<div v-else class="pa-5">
		<v-alert text type="warning" icon="mdi-alert-circle" class="mx-2 my-8"> Sorry, you do not have permission! </v-alert>
	</div>
</template>

<script>
const exhale = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

const gradients = [
	["#222"],
	["#42b3f4"],
	["red", "orange", "yellow"],
	["purple", "violet"],
	["#00c6ff", "#F0F", "#FF0"],
	["#f72047", "#ffd200", "#1feaea"],
];

export default {
	data: () => ({
		labels: ["12am", "3am", "6am", "9am", "12pm", "3pm", "6pm", "9pm"],
		value: [200, 675, 410, 390, 310, 460, 250, 240],

		items: [
			// 10 items dummy data
			{ name: "Registered Applications", value: 242, label: "Client" },
			{ name: "Registered Accounts", value: 150, label: "Account" },
			{ name: "Number Of Deposits", value: 95, label: "Deposit" },
			{ name: "Number Of Withdrawals", value: 185, label: "Withdrawal" },
			{ name: "Total Deposits", value: 959, label: "" },
			{ name: "Total Withdrawals", value: 684, label: "" },
			{ name: "Net Deposit", value: 242, label: "" },
			{ name: "Total Withdrawals (MTD)", value: 242, label: "" },
			{ name: "Total Deposits (MTD)", value: 242, label: "" },
			{ name: "Net Deposit (MTD)", value: 242, label: "" },
		],
		checking: false,
		heartbeats: [],

		width: 2,
		radius: 10,
		padding: 8,
		lineCap: "round",
		gradient: gradients[5],
		value2: [0, 2, 5, 9, 5, 10, 3, 5, 0, 0, 1, 8, 2, 9, 0],
		gradientDirection: "top",
		gradients,
		fill: false,
		type: "trend",
		autoLineWidth: false,
		statsData: {},

		columns: [
			{
				text: "#",
				align: "start",
				sortable: false,
				value: "id",
			},
			{
				text: "Reference Number",
				sortable: true,
				value: "reference_number",
			},
			{
				text: "Product Category",
				sortable: true,
				value: "product_category.name",
			},
			{
				text: "Factory",
				sortable: true,
				value: "factory.name",
			},
			{
				text: "Client",
				sortable: true,
				value: "client",
			},
			{
				text: "Country",
				sortable: true,
				value: "country",
			},
			{
				text: "Status",
				sortable: true,
				value: "status",
			},
			{
				text: "Workflow",
				sortable: true,
				value: "current_step",
			},
			{
				text: "Last Updated At",
				sortable: true,
				value: "updated_at",
			},
			{
				text: "Created At",
				sortable: true,
				value: "created_at",
			},
			{ text: "Has Revision", value: "isRevision" },
			{
				text: "",
				sortable: true,
				value: "actions",
			},
			{ text: "", value: "data-table-expand" },
		],
		expanded: [],
		statusItems: [
			{
				text: "Draft",
				value: 0,
			},
			{
				text: "Under Review",
				value: 1,
			},
			{
				text: "Under Approval",
				value: 2,
			},
			{
				text: "Approved",
				value: 3,
			},
			{
				text: "Tender",
				value: 4,
			},
			{
				text: "Achieved",
				value: 5,
			},
			{
				text: "On Going",
				value: 6,
			},
			{
				text: "Lost",
				value: 7,
			},
			{
				text: "Dead",
				value: 8,
			},
			{
				text: "Pre Qualification",
				value: 9,
			},
			{
				text: "Material submittal",
				value: 10,
			},
			{
				text: "Sample request",
				value: 11,
			},
			{
				text: "Expected order",
				value: 12,
			},
			{
				text: "Ordered",
				value: 13,
			},
		],
		workflowItems: [
			{
				text: "Define Project",
				value: 1,
			},
			{
				text: "Take Off",
				value: 2,
			},
			{
				text: "Factory Costing",
				value: 3,
			},
			{
				text: "Pricing",
				value: 4,
			},
			{
				text: "Shipping",
				value: 5,
			},
			{
				text: "Payment",
				value: 6,
			},
			{
				text: "Formal Quotation",
				value: 7,
			},
			{
				text: "Ordered",
				value: 8,
			},
		],
		sortItems: [
			{
				name: "Reference Number",
				value: "reference_number",
			},
			{
				name: "Product Category",
				value: "product_category_id",
			},
			{
				name: "Factory",
				value: "factory_id",
			},
			{
				name: "Country",
				value: "country_id",
			},
			{
				name: "Client",
				value: "client_id",
			},
			{
				name: "Status",
				value: "status",
			},
			{
				name: "Workflow",
				value: "current_step",
			},
			{
				name: "Last Updated At",
				value: "updated_at",
			},
			{
				name: "Created At",
				value: "created_at",
			},
		],
	}),

	async fetch() {
		await this.$axios.$get("/v1/portal/dashboard/stats").then((resp) => {
			this.statsData = resp;
		});
	},

	computed: {
		avg() {
			const sum = this.heartbeats.reduce((acc, cur) => acc + cur, 0);
			const length = this.heartbeats.length;

			if (!sum && !length) return 0;

			return Math.ceil(sum / length);
		},
	},

	created() {
		this.takePulse(false);
	},

	methods: {
		heartbeat() {
			return Math.ceil(Math.random() * (120 - 80) + 80);
		},
		async takePulse(inhale = true) {
			this.checking = true;

			inhale && (await exhale(1000));

			this.heartbeats = Array.from({ length: 20 }, this.heartbeat);

			this.checking = false;
		},

		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

<style lang="scss">
.v-sheet--offset {
	top: -24px;
	position: relative;
}
</style>




