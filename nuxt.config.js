import colors from "vuetify/es5/util/colors";
export default  () => {

	const defaultLocale = "en";

	const isDev = process.env.NODE_ENV === "development";
	const config = {
		buildDir: ".nuxt",

		ssr: false,
		target: "server",
		loading: false,
		publicRuntimeConfig: {
			defaultLocale,
			isDev,
			defaultUser: process.env.NUXT_ENV_DEFAULT_USER,
			defaultPassword: process.env.NUXT_ENV_DEFAULT_PASSWORD,
		},

		privateRuntimeConfig: {},

		head: {
			title: "Areel System",
			htmlAttrs: {
				lang: "en",
			},
			meta: [
				{ charset: "utf-8" },
				{ name: "viewport", content: "width=device-width, initial-scale=1" },
				{ hid: "description", name: "description", content: "" },
				{ name: "format-detection", content: "telephone=no" },
			],
			link: [{ rel: "icon", type: "image/x-icon", href: "/favicon.ico" }],
		},

		layoutTransition: {
			name: "fade-transition",
			mode: "out-in",
		},

		// Global CSS: https://go.nuxtjs.dev/config-css
		css: ["~/assets/general.scss"],

		serverMiddleware: [{ path: "/_ipx", handler: "~/server-middleware/ipx.js" }, "~/server-middleware/translations.js"],

		plugins: [
			"~/plugins/CloneDeep",
			"~/plugins/TypesConverters.js",
			"~/plugins/i18n.js",
			"~/plugins/axios",
			"~/plugins/vue-gates",
			"~/plugins/VuetifyStackedToast.js",
			"~/plugins/tooltip.client.js",
			"~/plugins/Marquee.client.js",
			"~/plugins/Driver.js",
			"~/plugins/General.js",
			"~/plugins/Rules.js",
			"~/plugins/i18nRouting.js",
      "~/plugins/VuetifyConfirm.js",
      { src: '~/plugins/html2canvas.js', ssr: false }
		],

		// Auto import components: https://go.nuxtjs.dev/config-components
		components: true,

		// Modules for dev and build (recommended): https://go.nuxtjs.dev/config-modules
		buildModules: [
			// https://go.nuxtjs.dev/eslint
			"@nuxtjs/eslint-module",
			// https://go.nuxtjs.dev/stylelint
			"@nuxtjs/stylelint-module",
			// https://go.nuxtjs.dev/vuetify
			"@nuxtjs/vuetify",
			"@nuxtjs/router-extras",
			[
				"nuxt-purgecss",
				{
					enabled: false, // ({ isDev, isClient }) => !isDev && isClient,
					paths: [
						"node_modules/@nuxtjs/vuetify/**/*.ts",
						"node_modules/@nuxt/vue-app/template/**/*.html",
						"node_modules/@nuxt/vue-app/template/**/*.vue",
						"node_modules/vue-marquee-text-component/**/*.vue",
						"components/**/*.vue",
						"components/*.vue",
						"layouts/*.vue",
						"pages/**/*.vue",
						"plugins/**/*.js",
					],
					styleExtensions: [".css", ".scss", ".sass"],
					whitelist: ["body", "html", "nuxt-progress", "v-application", "v-application--wrap"],
					whitelistPatterns: () => [/^v-((?!application).)*$/, /^\.theme--light*/, /^\.theme--dark*/, /.*-transition/, /col-*/],
					whitelistPatternsChildren: [/^v-((?!application).)*$/, /^theme--light*/, /^theme--dark*/, /col-*/],
					extractors: [
						{
							extractor: (content) => content.match(/[A-z0-9-:\\/]+/g) || [],
							extensions: ["html", "vue", "js"],
						},
					],
				},
			],
			"@nuxtjs/router",
			"@nuxt/image",
		],

		// Modules: https://go.nuxtjs.dev/config-modules
		modules: [
			"@nuxtjs/axios",
			"@nuxtjs/auth-next",
			"nuxt-route-meta",
			"cookie-universal-nuxt",
			"@nuxtjs/dayjs",
			process.env.NUXT_ENV_LOCAL_VUETIFY_CUSTOMIZED_FIELDS || "vuetify-customized-fields",
		],

		// Axios module configuration: https://go.nuxtjs.dev/config-axios
		axios: {
			// baseURL: process.env.NUXT_ENV_API_BASE_URL,
			baseURL: process.env.NUXT_ENV_PROXY_TARGET_BASE_URL,
			proxy: false,
			debug: true,
			proxyHeaders: true,
			credentials: true,
			headers: {
				common: {
					Accept: "application/json",
				},
			},
			proxyHeadersIgnore: [
				"accept",
				"host",
				"x-forwarded-host",
				"x-forwarded-port",
				"x-forwarded-proto",
				"cf-ray",
				"cf-connecting-ip",
				"content-length",
				"content-md5",
				"content-type",
			],
		},

		auth: {
			plugins: ["~/plugins/auth.js"],
			cookie: {
				options: {
					path: "/",
					maxAge: 60 * 60 * 24 * 365, // 1 year
					secure: !isDev,
				},
			},
			strategies: {
				local: {
					scheme: "refresh",

					token: {
						property: "token.access_token",
						global: true,
						required: true,
						type: "Bearer",
						maxAge: 60 * 60 * 2,
					},
					refreshToken: {
						property: "token.refresh_token",
						data: "refresh_token",
						maxAge: 60 * 60 * 24 * 30, // 1 month
					},
					user: {
						property: false,
						autoFetch: true,
					},
					endpoints: {
						login: { url: "/v1/auth/login", method: "post" },
						logout: { url: "/v1/auth/logout", method: "post" },
						user: { url: "/v1/my", method: "get" },
						refresh: { url: "/v1/auth/refresh", method: "post" },
					},
					redirect: {
						// i18n path will be corrected with plugins/auth.js
						login: "/login",
						logout: "/logout",
						callback: "/login",
						home: "/",
					},
					fullPathRedirect: true,
					resetOnError: true,
				},
			},
		},
		dayjs: {
			// locales: dayjsLocales, to import only available and only lazily
			defaultLocale: "en",
			// defaultTimeZone: 'Asia/Tokyo',
			plugins: [
				"utc", // import 'dayjs/plugin/utc'
				"timezone", // import 'dayjs/plugin/timezone'
				"duration", // import 'dayjs/plugin/duration'
				"relativeTime", // import 'dayjs/plugin/relativeTime'
			], // Your Day.js plugin
		},
		i18n: {
			lazy: true,
			vueI18nLoader: false,
			langDir: "locales/",
			strategy: "prefix",
			defaultLocale,
			vueI18n: { fallbackLocale: "en", silentTranslationWarn: true },
			// locales,
			skipSettingLocaleOnNavigate: true,
		},
		image: {
			provider: isDev ? "ipx" : "bunnyCDN",
			providers: {
				customProvider: {
					name: "bunnyCDN", // optional value to overrider provider name
					provider: "~/modules/BunnyCDN.js", // Path to custom provider
					options: {
						// ... provider options
						baseURL: process.env.NUXT_ENV_CDN_URL || "/", // https://front-ingot.b-cdn.net
					},
				},
			},
			screens: {
				xs: 600,
				sm: 960,
				md: 1264,
				lg: 1904,
				xl: Infinity,
			},
		},
		proxy: {
			"/v1/": {
				target: process.env.NUXT_ENV_PROXY_TARGET_BASE_URL,
				changeOrigin: true,
				secure: false,

				on: {
					onProxyRes: (proxyRes, req, res) => {
						console.log("proxyRes", proxyRes);
						console.log("req", req);
						console.log("res", res);
						res.removeHeader("content-length");
					},
					onProxyReq(proxyReq, req, res, options) {
						console.log("proxyRes", proxyReq);
						console.log("req", req);
						console.log("res", res);
						if (req.body) {
							const bodyData = JSON.stringify(req.body);
							// incase if content-type is application/x-www-form-urlencoded -> we need to change to application/json
							proxyReq.setHeader("Content-Type", "application/json");
							proxyReq.setHeader("Content-Length", Buffer.byteLength(bodyData));
							// stream the content
							proxyReq.write(bodyData);
						}
					},
				},
			},
		},
		routerModule: {
			path: "routes",
			fileName: "index.js",
			// keepDefaultRouter: false,
		},
		// Vuetify module configuration: https://go.nuxtjs.dev/config-vuetify
		vuetify: {
			customVariables: ["~/assets/variables.scss"],
			treeShake: true,
			options: {
				customProperties: true,
				variations: false,
			},
			theme: {
				dark: false,
				default: "light",
				defaultAssets: {
					font: false,
				},
				options: { customProperties: true },
				themes: {
					dark: {
						// primary: "#ff3b41",
						primary: "#B71F23",
						accent: colors.grey.darken3,
						secondary: "#1a9f72",
						info: colors.blue.lighten1,
						warning: colors.amber.base,
						error: colors.deepOrange.accent4,
						success: colors.green.accent3,
						sidebar: "#363636",
						gold: "#f3c405",
						"inactive-tab": colors.shades.black,
						"active-tab": "#272727",
						background: "#121212",
						deposit: colors.teal.base,
					},
					light: {
						primary: "#B71F23",
						// primary: "#94171b",
						accent: colors.grey.darken3,
						info: colors.blue.lighten1,
						secondary: "#1d996b",
						sidebar: "#f7f7fb",
						// input: "#eaeaef",
						gold: "#f3c405",
						"inactive-tab": colors.grey.lighten3,
						"active-tab": colors.shades.white,
						background: "#fafafa",
						deposit: colors.teal.base,
					},
				},
			},
		},
		vuetifyCustomFields: {
			prefix: "vc",
			style: {
				// dense: true,
			},
		},
		webpackOptimisations: {
			features: {
				// enable risky optimisations in dev only
				// hardSourcePlugin: isDev,
				// parallelPlugin: isDev,
			},
		},

		// Build Configuration: https://go.nuxtjs.dev/config-build
		build: {
			publicPath: process.env.NUXT_ENV_CDN_URL || "/_nuxt",
			analyze: !isDev,
			extractCSS: !isDev,
			loaders: {
				vue: {
					prettify: false,
					compilerOptions: {
						whitespace: process.env.NODE_ENV === "production" ? "condense" : "",
					},
				},
				// sass: {
				// 	additionalData: '@import "/node_modules/vuetify/src/styles/settings";',
				// },
				scss: {
					additionalData: '@import "/node_modules/vuetify/src/styles/settings", "~/assets/mixins";',
				},
			},
		},
		vue: {
			config: {
				productionTip: false,
				devtools: true,
			},
		},
	};

	// if (isDev) {
	// 	// Development
	// 	config.plugins.push("~/plugins/Faker.js");
	// } else {
	// 	// Production
	// }

	config.plugins.push("~/plugins/Faker.js");

	return config;
};
