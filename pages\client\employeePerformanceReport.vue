<template>
	<page title="Employee Performance Report" desc="">
		<div>
			<data-table
				v-if="checkPermission('reports.employee_performance')"
				ref="dataTable"
				:columns="columns"
				api="/v1/portal/reports/employee-performance"
			>
				<!-- <template #filter="{ models }">
					<vc-autocomplete
						key="client_id"
						v-model="models.client_id"
						api="/v1/lookups/clients"
						label="Client"
						name="client_id"
						item-text="text"
						item-value="value"
					>
					</vc-autocomplete>

					<vc-autocomplete
						key="country_id"
						v-model="models.country_id"
						api="/v1/lookups/countries"
						label="Country"
						name="countries"
						item-text="text"
						item-value="value"
					>
					</vc-autocomplete>

					<vc-autocomplete
						key="product_category_id"
						v-model="models.product_category_id"
						api="/v1/lookups/product-categories"
						label="Product Category"
						name="product_category_id"
						item-text="text"
						item-value="value"
					>
					</vc-autocomplete>
				</template> -->
			</data-table>
			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "User id",
					align: "start",
					value: "user_id",
				},
				{
					text: "User Name",
					sortable: true,
					value: "user_name",
				},
				{
					text: "Role Name",
					sortable: true,
					value: "role_name",
				},
				{
					text: "Number Of Created Quotes",
					sortable: true,
					value: "number_of_created_quotes",
				},
				{
					text: "Number of current assigned quotes",
					sortable: true,
					value: "number_of_current_assigned_quotes",
				},
			],
		};
	},
	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

