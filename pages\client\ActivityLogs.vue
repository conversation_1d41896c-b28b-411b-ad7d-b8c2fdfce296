<template>
	<page title="Activity Logs" desc="">
		<div>
			<data-table v-if="checkPermission('activity_logs.view')" ref="dataTable" :columns="columns" api="/v1/portal/activity-logs">
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-if="checkPermission('activity_logs.update')" v-tooltip="'Show'" small icon @click="activityLogs(item.id)">
							<v-icon small>mdi-eye</v-icon>
						</v-btn>
					</div>
				</template>

				<template #filter="{ models }">
					<vc-autocomplete
						key="client_id"
						v-model="models.type"
						api="/v1/lookups/activity-log-types"
						label="Type"
						name="type"
						item-text="text"
						item-value="value"
					>
					</vc-autocomplete>

					<vc-autocomplete
						key="event"
						v-model="models.event"
						api="/v1/lookups/activity-log-events"
						label="Event"
						name="event"
						item-text="text"
						item-value="value"
					>
					</vc-autocomplete>
				</template>

				<template #item.user="{ item }">
					{{ item.user?.name }}
				</template>

				<template #item.subject="{ item }">
					<div class="d-flex">
						<span>{{ item.subject_type }}</span>
						<span v-if="item.subject_id" class="ms-2">#</span>
						<span class="font-weight-bold">{{ item.subject_id }}</span>

						<span v-if="!item.subject_id && !item.subject_type">-</span>
					</div>
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>
			</data-table>
			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>

			<v-dialog v-model="activityLogsModal" absolute width="900px">
				<v-card>
					<v-card-title>
						<div class="mb-3">User Activity</div>
						<v-spacer></v-spacer>
						<v-btn icon @click="activityLogsModal = false">
							<v-icon>mdi-close</v-icon>
						</v-btn>
					</v-card-title>
					<v-card-text>
						<v-card>
							<v-card-title class="d-flex">
								<div class="body-2">
									By:
									<span class="font-weight-bold ms-1">
										{{ activityLogsData.user?.name }}
									</span>
									<span>( {{ activityLogsData.description }} )</span>
								</div>
								<v-spacer></v-spacer>
								<h6 class="me-2">{{ activityLogsData.updated_at }}</h6>
							</v-card-title>

							<v-card-text>
								<div v-if="activityLogsData && activityLogsData.properties" class="d-flex">
									<div style="width: 50%" class="me-2">
										<h4 class="mb-2">Old Data:</h4>
										<div v-for="(old, j) in activityLogsData.properties.old" :key="j" class="mb-3">
											<template v-if="typeof old.value === 'object'">
												<div v-for="(value, key) in old.value" :key="key" class="d-flex mb-4">
													<field-text-field
														background-color="#ffe2e2"
														class="me-1"
														outlined
														hide-details
														:value="key"
														:label="key"
													></field-text-field>

													<field-text-field
														background-color="#ffe2e2"
														class="me-1"
														outlined
														hide-details
														:value="value ? value : 'Null'"
														:label="value"
													></field-text-field>
												</div>
											</template>

											<template v-else>
												<field-text-field
													background-color="#ffe2e2"
													class="me-1"
													outlined
													hide-details
													:value="old.value ? old.value : 'Null'"
													:label="old.key"
												></field-text-field>
											</template>
										</div>
									</div>
									<div style="width: 50%">
										<h4 class="mb-2">New Data:</h4>
										<div v-for="(newVal, n) in activityLogsData.properties.attributes" :key="n" class="mb-3">
											<template v-if="typeof newVal.value === 'object'">
												<div v-for="(value, key) in newVal.value" :key="key" class="d-flex mb-4">
													<field-text-field
														background-color="#4caf5036"
														class="me-1"
														outlined
														hide-details
														:value="key"
														:label="key"
													></field-text-field>

													<field-text-field
														background-color="#4caf5036"
														class="me-1"
														outlined
														hide-details
														:value="value ? value : 'Null'"
														:label="value"
													></field-text-field>
												</div>
											</template>

											<template v-else>
												<field-text-field
													background-color="#4caf5036"
													class="me-1"
													outlined
													hide-details
													:value="newVal.value ? newVal.value : 'Null'"
													:label="newVal.key"
												></field-text-field>
											</template>
										</div>
									</div>
								</div>
								<div v-else>No data available to show</div>
							</v-card-text>
						</v-card>
					</v-card-text>
					<v-card-actions>
						<v-spacer></v-spacer>
						<v-btn text @click="activityLogsModal = false">Cancel</v-btn>
					</v-card-actions>
				</v-card>
			</v-dialog>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},

				{
					text: "Type",
					sortable: true,
					value: "type",
				},
				{
					text: "Event",
					sortable: true,
					value: "event",
				},
				{
					text: "Subject",
					sortable: true,
					value: "subject",
				},
				{
					text: "User",
					sortable: true,
					value: "user",
				},
				{
					text: "Description",
					sortable: true,
					value: "description",
				},
				{
					text: "Created at",
					sortable: true,
					value: "created_at",
				},
				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],
			activityLogsModal: false,
			activityLogsData: {},
		};
	},
	computed: {},
	watch: {},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},

		activityLogs(id) {
			this.activityLogsData = {};
			this.$axios.$get(`/v1/portal/activity-logs/${id}`).then((resp) => {
				this.activityLogsData = resp;
				this.activityLogsModal = true;
			});
		},
	},
};
</script>

