<template>
	<v-checkbox v-if="!items" v-bind="$attrs" v-model="localValue" :true-value="true" :false-value="false"></v-checkbox>
	<div v-else class="d-flex">
		<v-checkbox
			v-for="item in items"
			v-bind="$attrs"
			:key="item.value"
			v-model="localValue"
			:value="item.value"
			:label="item.text"
			:true-value="true"
			:false-value="false"
		></v-checkbox>
	</div>
</template>

<script>
export default {
	inheritAttrs: false,
	props: {
		value: {
			type: [Boolean],
			default: false,
		},
		items: {
			type: [Array],
			default: null,
		},
	},
	computed: {
		localValue: {
			get() {
				return this.value;
			},
			set(value) {
				return this.$emit("input", value);
			},
		},
	},
};
</script>
