<template>
	<div>
		<div id="element-to-convert" style="background: #fff">
			<v-container class="pa-8" style="padding: 20px">
				<v-row>
					<v-col md="6">
						<v-img src="/logo.png" width="350px" style="width: 250px"></v-img>
						<div style="width: 250px; text-align: center">
							<div style="font-size: 12px">JAFZA ONE, Tower B , 13th Floor, Suite BB1302</div>
							<div style="font-size: 12px">P.O Box 17046, JEBEL ALI, Dubai, UAE</div>
							<div style="font-size: 12px">Tel: +971 (04) 268 4666</div>
						</div>
					</v-col>
					<v-col md="6">
						<v-simple-table style="border: 1px solid #ebebeb">
							<thead>
								<tr style="background: #696969">
									<th style="font-size: 2rem; color: #fff; text-align: center" colspan="4">PICK TICKET</th>
								</tr>
								<tr>
									<th class="text-left" style="background: #ebebeb">Sales Order #</th>
									<th class="text-left" style="background: #ebebeb">Order Date</th>
									<th class="text-left" style="background: #ebebeb">Customer #</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>{{ orderData.pick_ticket?.reference_number }}</td>
									<td>{{ orderData.pick_ticket?.created_at | date }}</td>
									<td>{{ orderData.quote?.client_contact_person?.client?.name }}</td>
								</tr>
								<tr style="background: #ebebeb">
									<th colspan="2">Account Manager</th>
									<th class="text-left" style="background: #ebebeb">Purchase Order</th>
								</tr>
								<tr>
									<td colspan="2">{{ orderData.account_manager?.name }}</td>
									<td>{{ orderData.quote?.client_contact_person?.client?.name }}</td>
								</tr>
							</tbody>
						</v-simple-table>
					</v-col>
				</v-row>

				<v-divider class="my-3" />

				<v-row class="relative">
					<div v-if="pdfHiddenElements" class="absolute end">
						<v-btn icon small text class="text-end mt-1" @click="exportToPDF"><v-icon>mdi-download</v-icon></v-btn>
					</div>

					<v-col md="6" cols="12">
						<div>
							<div style="margin-bottom: 25px; margin-top: 5px">
								<h3 class="font-weight-bold" style="margin-bottom: 6px">BILL TO</h3>
								<span class="d-inline-block coffee-3--text body-2">AREEL SPECIALTIES INTERNATIONAL FZCO</span>
								<span class="d-inline-block coffee-3--text body-2">JAFZA ONE, Tower B , 13th Floor, Suite BB1302</span>
								<span class="d-inline-block coffee-3--text body-2">P.O Box 17046, JEBEL ALI, Duba</span>
								<span class="d-inline-block coffee-3--text body-2" style="margin-bottom: 8px">United Arab of Emirates</span>

								<div style="display: flex; align-items: center; margin-bottom: 2px">
									<h5 class="d-inline-block">Attention of:</h5>
									<span class="d-inline-block coffee-3--text body-2">{{ orderData.account_manager?.name }}</span>
								</div>
								<div style="display: flex; align-items: center; margin-bottom: 2px">
									<h5 class="d-inline-block">Phone:</h5>
									<span class="d-inline-block coffee-3--text body-2">+971 (04) 268 4666</span>
								</div>
								<div style="display: flex; align-items: center; margin-bottom: 2px">
									<h5 class="d-inline-block">Mobile:</h5>
									<span class="d-inline-block coffee-3--text body-2">+971 521 721 569</span>
								</div>
								<div style="display: flex; align-items: center; margin-bottom: 2px">
									<h5 class="d-inline-block">Email:</h5>
									<span class="d-inline-block coffee-3--text body-2">{{ orderData.account_manager?.email }}</span>
								</div>
							</div>
						</div>
					</v-col>

					<v-col md="6" cols="12">
						<div>
							<div style="margin-bottom: 25px; margin-top: 5px">
								<div style="margin-bottom: 8px">
									<h3 class="font-weight-bold">SHIP TO</h3>
								</div>

								<div style="display: flex; align-items: center; margin-bottom: 2px">
									<h5 class="d-inline-block me-1">Client Name:</h5>
									<span class="d-inline-block coffee-3--text body-2">
										{{ orderData.quote?.client_contact_person?.client?.name }}
									</span>
								</div>
								<div style="display: flex; align-items: center; margin-bottom: 2px">
									<h5 class="d-inline-block me-1">Client Email:</h5>
									<span class="d-inline-block coffee-3--text body-2">
										{{ orderData.quote?.client_contact_person?.client?.email }}
									</span>
								</div>
								<div style="display: flex; align-items: center; margin-bottom: 2px">
									<h5 class="d-inline-block me-1">Client Address:</h5>
									<span class="d-inline-block coffee-3--text body-2">
										{{ orderData.quote?.client_contact_person?.address_one }}
									</span>
								</div>
								<div style="display: flex; align-items: center; margin-bottom: 2px">
									<h5 class="d-inline-block me-1">Address Tow:</h5>
									<span class="d-inline-block coffee-3--text body-2">
										{{ orderData.quote?.client_contact_person?.address_two }}
									</span>
								</div>
								<div style="display: flex; align-items: center; margin-bottom: 2px">
									<h5 class="d-inline-block me-1">Phone:</h5>
									<span class="d-inline-block coffee-3--text body-2">
										{{ orderData.quote?.client_contact_person?.phone }}
									</span>
								</div>
								<div style="display: flex; align-items: center; margin-bottom: 2px">
									<h5 class="d-inline-block me-1">Fax:</h5>
									<span class="d-inline-block coffee-3--text body-2">
										{{ orderData.quote?.client_contact_person?.fax }}
									</span>
								</div>
								<div style="display: flex; align-items: center; margin-bottom: 2px">
									<h5 class="d-inline-block me-1">Email:</h5>
									<span class="d-inline-block coffee-3--text body-2">
										{{ orderData.quote?.client_contact_person?.client?.email }}
									</span>
								</div>
							</div>
						</div>
					</v-col>

					<v-col cols="12">
						<v-card outlined>
							<v-card-text>
								<v-simple-table class="border">
									<thead style="background: #232323">
										<tr>
											<th class="text-left" style="color: #fff">Quantity</th>
											<th class="text-left" style="color: #fff">UOM</th>
											<th class="text-left" style="color: #fff">Part # / Description</th>
											<th class="text-left" style="color: #fff">Unit Price</th>
											<th class="text-left" style="color: #fff">Total</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="item in orderData.quote?.products" :key="item">
											<template v-if="!item.is_optional">
												<td>{{ item.quantity }}</td>
												<td>{{ item.uom }}</td>

												<td>
													<div>
														{{ item.part_number }}
													</div>
													<div>
														{{ item.description }}
													</div>
												</td>
												<td>{{ item.unit_price }}</td>
												<td>{{ item.total }}</td>
											</template>
										</tr>
									</tbody>
								</v-simple-table>
							</v-card-text>
						</v-card>
					</v-col>
				</v-row>

				<v-row class="fill-height mt-0">
					<v-col cols="8"> </v-col>

					<v-spacer />

					<v-col md="3" cols="12" class="mt-0 pt-0">
						<v-card outlined color="#ecebeb" rounded="0">
							<v-card-content>
								<v-list-item dense>
									<v-list-item-content class="mb-0 pb-0">
										<v-list-item-title>Subtotal</v-list-item-title>
									</v-list-item-content>
									<v-list-item-content class="mb-0 pb-0 text-end pe-4">
										<v-list-item-title class="font-weight-bold">${{ orderData.quote.subtotal }}</v-list-item-title>
									</v-list-item-content>
								</v-list-item>
								<v-divider />
								<v-list-item v-if="orderData.quote.vat" dense>
									<v-list-item-content class="mb-0 pb-0">
										<v-list-item-title>Vat</v-list-item-title>
									</v-list-item-content>
									<v-list-item-content class="mb-0 pb-0 text-end pe-4">
										<v-list-item-title class="font-weight-bold">%{{ orderData.quote.vat }}</v-list-item-title>
									</v-list-item-content>
								</v-list-item>
								<v-divider v-if="orderData.quote.vat" />
								<v-list-item v-if="orderData.quote.tax" dense>
									<v-list-item-content class="mb-0 pb-0">
										<v-list-item-title>Tax</v-list-item-title>
									</v-list-item-content>
									<v-list-item-content class="mb-0 pb-0 text-end pe-4">
										<v-list-item-title class="font-weight-bold">%{{ orderData.quote.tax }}</v-list-item-title>
									</v-list-item-content>
								</v-list-item>
								<v-divider v-if="orderData.quote.tax" />
								<v-list-item v-if="orderData.quote.discount" dense>
									<v-list-item-content class="mb-0 pb-0">
										<v-list-item-title>Special Discount</v-list-item-title>
									</v-list-item-content>
									<v-list-item-content class="mb-0 pb-0 text-end pe-4">
										<v-list-item-title class="font-weight-bold">%{{ orderData.quote.discount }}</v-list-item-title>
									</v-list-item-content>
								</v-list-item>
								<v-divider v-if="orderData.quote.discount" />
								<v-list-item dense>
									<v-list-item-content class="mb-0 pb-0">
										<v-list-item-title>Total</v-list-item-title>
									</v-list-item-content>
									<v-list-item-content class="mb-0 pb-0 text-end pe-4">
										<v-list-item-title class="font-weight-bold">${{ orderData.quote.total }}</v-list-item-title>
									</v-list-item-content>
								</v-list-item>
							</v-card-content>

							<!-- <v-card-actions v-if="pdfHiddenElements">
								<v-btn small @click="$refs.crudCalculations.edit($route.params.quote_id)">Edit</v-btn>
							</v-card-actions> -->
						</v-card>
					</v-col>
				</v-row>
			</v-container>

			<!-- <crud
				ref="crudCalculations"
				v-slot="{ item }"
				width="400"
				:default="orderData"
				item-name="Discount"
				api="/v1/portal/quotes"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<vc-text-field
					v-model="item.discount"
					@input="discountConvertInt(item.discount)"
					name="discount"
					outlined
					label="Discount"
				></vc-text-field>

				<vc-text-field v-model="item.tax" @input="taxConvertInt(item.tax)" name="tax" outlined label="Tax"></vc-text-field>

				<vc-text-field v-model="item.vat" @input="vatConvertInt(item.vat)" name="vat" outlined label="Vat"></vc-text-field>
			</crud> -->
		</div>
	</div>
</template>

<script>
import html2pdf from "html2pdf.js";
export default {
	data() {
		return {
			pdfHiddenElements: true,
			calculationsBtn: true,

			orderData: {
				id: 4,
				reference_number: "PO-4-A-MX-R0",
				status: 0,
				factory_quote_number: "532",
				factory_quote_date: "2023-09-09T00:00:00.000000Z",
				shipping_instructions: "<p>Perferendis magni mo.</p>",
				delivery_notes: "<p>Deserunt et aspernat.</p>",
				created_at: "2023-09-01T23:00:10.000000Z",
				updated_at: "2023-09-01T23:00:10.000000Z",
				terms: "<p>Iste sunt, quisquam .</p>",
				quote_id: 1,
				account_manager_id: 5,
				quote: {
					id: 1,
					reference_number: "Q-1-A-MX-R0",
					status: 5,
					valid_until: 4,
					valid_until_date: "2023-09-06T00:00:00.000000Z",
					payment_method: 0,
					payment_method_terms:
						"<p>100% wire transfer In Advance</p><p> Down Payment xx%, Remaining xx% @ xx Days, From ( Date of PI / Date of CI / Date of Shipment )</p>",
					delivery_method: 1,
					delivery_method_terms: "<p>EX-WORKS</p> <p>C&F xxx ( Airport / Port )</p> <p>CIF xxx ( Airport / Port )</p>",
					delivery_address: "Amman",
					notes: null,
					created_at: "2023-09-01T14:02:51.000000Z",
					updated_at: "2023-09-01T14:37:46.000000Z",
					client_contact_person_id: 2,
					product_category_id: 1,
					parent_id: null,
					total: 235.6,
					subtotal: 190,
					discount: 0,
					tax: 15,
					vat: 9,
					client_contact_person: {
						id: 2,
						name: "Emilie Huel",
						email_one: "<EMAIL>",
						email_two: "<EMAIL>",
						phone: "**************",
						mobile: "+18166072639",
						fax: "(*************",
						position: "Rail Transportation Worker",
						address_one: "383 O'Connell Highway\nWest Vincenzaland, DC 37173",
						address_two: "777 Deondre Vista Apt. 596\nThompsonhaven, AR 39733",
						created_at: "2023-09-01T13:23:25.000000Z",
						updated_at: "2023-09-01T13:23:25.000000Z",
						client: {
							id: 1,
							name: "Blaze Schmidt",
							email: "<EMAIL>",
							created_at: "2023-09-01T13:23:25.000000Z",
							updated_at: "2023-09-01T13:23:25.000000Z",
						},
					},
					product_category: {
						id: 1,
						name: "Floor Doors",
						slug: "floor-doors",
						code: "A",
						photo: null,
						description: null,
						created_at: "2023-09-01T13:23:11.000000Z",
						updated_at: "2023-09-01T13:23:11.000000Z",
					},
				},
				account_manager: {
					id: 5,
					name: "Omari Green",
					email: "<EMAIL>",
					created_at: "2023-09-01T13:23:23.000000Z",
					updated_at: "2023-09-01T13:23:23.000000Z",
				},
				proforma_invoice: {
					id: 3,
					reference_number: "PI-4-A-MX-R0",
					trn_number: "555",
					terms: "<p>Neque ipsum, sequi o.</p>",
					created_at: "2023-09-01T23:00:10.000000Z",
					updated_at: "2023-09-01T23:00:10.000000Z",
					bank_accounts: [
						{
							id: 1,
							account_number: "*************",
							bank_name: "EMIRATES NBD BANK PJSC",
							branch_name: "JEBEL ALI",
							account_name: "AREEL SPECIALTIES INTERNATIONAL FZCO",
							iban: "AE05026000*************",
							swift_code: "EBILAEAD",
							currency: "AED",
							created_at: "2023-09-01T13:23:11.000000Z",
							updated_at: "2023-09-01T13:23:11.000000Z",
						},
						{
							id: 2,
							account_number: "*************",
							bank_name: "EMIRATES NBD BANK PJSC",
							branch_name: "JEBEL ALI",
							account_name: "AREEL SPECIALTIES INTERNATIONAL FZCO",
							iban: "AE13026000*************",
							swift_code: "EBILAEAD",
							currency: "USD",
							created_at: "2023-09-01T13:23:11.000000Z",
							updated_at: "2023-09-01T13:23:11.000000Z",
						},
					],
					revisions: [],
				},
				commercial_invoice: {
					id: 6,
					reference_number: "CI-4-A-MX-R0",
					invoice_number: "810",
					invoice_date: null,
					created_at: "2023-09-01T23:00:10.000000Z",
					updated_at: "2023-09-01T23:00:10.000000Z",
					terms: "<p>Exercitationem eaque.</p>",
					revisions: [],
				},
				pick_ticket: {
					id: 3,
					reference_number: "PT-4-A-MX-R0",
					terms: "<p>Enim facere quo veri.</p>",
					created_at: "2023-09-01T23:00:10.000000Z",
					updated_at: "2023-09-01T23:00:10.000000Z",
					revisions: [],
				},
				acknowledgement_invoice: {
					id: 3,
					reference_number: "AKN-4-A-MX-R0",
					terms: "<p>Unde consequatur qui.</p>",
					notes: "<p>Sit et aliqua. Volup.</p>",
					created_at: "2023-09-01T23:00:10.000000Z",
					updated_at: "2023-09-01T23:00:10.000000Z",
					revisions: [],
				},
			},
		};
	},

	async fetch() {
		await this.$axios.$get(`/v1/portal/orders/${this.$route.params.order_id}`).then((resp) => {
			this.orderData = resp;
		});
	},

	methods: {
		exportToPDF() {
			this.pdfHiddenElements = false;
			html2pdf(document.getElementById("element-to-convert"), {
				margin: 1,
				filename: "qoute.pdf",
			});

			setTimeout(() => {
				this.pdfHiddenElements = true;
			}, 2000);
		},

		discountConvertInt(data) {
			this.orderData.discount = data;
		},
		taxConvertInt(data) {
			this.orderData.tax = data;
		},
		vatConvertInt(data) {
			this.orderData.vat = data;
		},

		refresh() {
			this.$axios.$get(`/v1/portal/quotes/${this.$route.params.quote_id}`).then((resp) => {
				this.orderData = resp;
			});
		},
	},
};
</script>
