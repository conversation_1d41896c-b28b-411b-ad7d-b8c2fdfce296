<template>
	<v-badge
		:content="unreadNotificationsCount"
		color="error pulse"
		:value="!!unreadNotificationsCount"
		overlap
		bordered
		offset-x="24px"
		offset-y="24px"
	>
		<v-menu offset-y v-model="menuModel" :close-on-content-click="false">
			<template #activator="slotData">
				<slot v-bind="{ ...slotData, loading: $fetchState.pending }">
					<v-btn icon v-on="slotData.on"><v-icon>mdi-bell</v-icon> </v-btn>
				</slot>
			</template>
			<v-card>
				<v-list dense>
					<div class="d-flex align-center justify-space-between px-2">
						<v-btn text small color="primary" @click="markAllAsRead" :loading="isMarkingAll">mark all read</v-btn>
						<v-chip small color="primary" dark>{{ unreadNotificationsCount }} unread</v-chip>
					</div>
				</v-list>
				<v-list dense two-line class="overflow-y-scroll" max-height="70vh">
					<v-divider class="my-2"></v-divider>
					<v-list-item
						exact
						:input-value="!item.read_at"
						color="primary"
						class="clickable"
						@click="clickHandler(item)"
						v-for="item in notifications"
						:key="item.id"
					>
						<v-list-item-avatar v-if="item.data.image">
							<v-img :src="item.data.image">mdi-account</v-img>
						</v-list-item-avatar>
						<v-list-item-icon v-else-if="item.data.icon">
							<v-icon>{{ item.data.icon }}</v-icon>
						</v-list-item-icon>
						<v-list-item-content>
							<v-list-item-title>{{ item.data.title }}</v-list-item-title>
							<v-list-item-subtitle>{{ item.data.body }}</v-list-item-subtitle>
						</v-list-item-content>
						<v-list-item-action>
							<v-list-item-action-text :key="timeKey + '-' + item.id">
								{{ item.created_at | dateFromNow }}
							</v-list-item-action-text>
						</v-list-item-action>
					</v-list-item>
				</v-list>

				<v-card flat>
					<v-divider class="my-2"></v-divider>
					<v-list-item link class="justify-center">
						<div>
							see all notifications
						</div>
					</v-list-item>
				</v-card>
			</v-card>
		</v-menu>
	</v-badge>
</template>

<script>
export default {
	name: "NotificationsMenu",
	data() {
		return {
			menuModel: false,
			timeKey: Date.now().toString(),
			isMarkingAll: false,
		};
	},
	async fetch() {
		return await this.$store.dispatch("notifications/getNotifications");
	},
	computed: {
		notifications() {
			return this.$store.getters["notifications/notifications"];
		},
		unreadNotificationsCount() {
			return this.$store.state.authExtend.stat.unread_notifications_count;
		},
	},
	methods: {
		refreshTimeEveryMinute() {
			setInterval(() => {
				this.timeKey = Date.now().toString();
			}, 60 * 1000);
		},
		clickHandler(item) {
			this.markAsRead(item.id);
			if (item.link) {
				this.$router.push(item.link);
			}
		},
		markAllAsRead() {
			this.isMarkingAll = true;
			this.$store
				.dispatch("notifications/markAllAsRead")
				.catch((error) => {
					this.genericErrorHandler(error);
				})
				.finally(() => {
					this.isMarkingAll = false;
				});
		},
		markAsRead(id) {
			this.$store.dispatch("notifications/markAsRead", id);
		},
	},
	mounted() {
		this.refreshTimeEveryMinute();
	},
};
</script>

