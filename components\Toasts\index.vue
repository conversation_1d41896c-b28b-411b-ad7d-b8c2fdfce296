<template>
	<toasts-snackbars ref="snackbar" v-slot="options" content-class="pt-0" :objects.sync="objects">
		<!-- eslint-disable-next-line -->

		<!-- :color="color"
		:bottom="y === 'bottom'"
		:top="y === 'top'"
		:left="x === 'left'"
		:right="x === 'right'"
		:centered="centered"
		:multi-line="multiLine"
		:vertical="vertical"
		class="v-application vts"
		:class="classes"
		role="alert"
		:timeout="0"
		transition="slide-x-reverse-transition"
		@click="dismiss" -->

		<v-progress-linear
			dark
			color="rgba(255,255,255,0.2)"
			reverse
			absolute
			background-color="rgba(255,255,255,0.5)"
			:active="!!options.timeout"
			:value="options.remaining"
		>
		</v-progress-linear>
		<slot name="title">
			<div class="font-weight-bold mb-2 d-flex align-center pt-3">
				<template v-if="options.title">
					<v-icon v-if="!!options.icon" dark :left="!isRTL" :right="isRTL" class="vts__icon" :color="options.iconColor">
						{{ options.icon }}
					</v-icon>
					<div v-if="options.title">{{ options.title }}</div>
					<template v-if="options.showClose">
						<v-spacer />
						<v-btn :icon="!options.closeText" :text="!!options.closeText" :color="options.closeColor" @click="options.close">
							<v-icon v-if="!options.closeText">{{ options.closeIcon }}</v-icon>
							<span v-if="!!options.closeText">{{ options.closeText }}</span>
						</v-btn>
					</template>
				</template>
			</div>
		</slot>
		<div>
			<v-icon
				v-if="!!options.icon & (!options.title && !options.showClose)"
				dark
				:left="!isRTL"
				:right="isRTL"
				:color="options.iconColor"
			>
				{{ options.icon }}
			</v-icon>
			<ul v-if="Array.isArray(options.message)">
				<template v-for="array in options.message"
					><li v-for="(line, li) in array" :key="li">{{ line }}</li></template
				>
			</ul>
			<span v-else v-html="options.message"></span>
			<slot></slot>
		</div>

		<!-- <template #action="{ attrs }">-->
	</toasts-snackbars>
</template>

<script>
export default {
	name: "Toasts",

	props: {
		// objects: {
		// 	type: Array,
		// 	default: () => [],
		// },
		// x: {
		// 	type: String,
		// 	default: "right",
		// },
		// y: {
		// 	type: String,
		// 	default: "bottom",
		// },
		// centered: {
		// 	type: Boolean,
		// 	default: false,
		// },
		// color: {
		// 	type: String,
		// 	default: "info",
		// },
		// icon: {
		// 	type: String,
		// 	default: "",
		// },
		// iconColor: {
		// 	type: String,
		// 	default: "",
		// },
		// classes: {
		// 	type: [String, Object, Array],
		// 	default: "",
		// },
		// message: {
		// 	type: String,
		// 	default: "",
		// },
		// timeout: {
		// 	type: Number,
		// 	default: 3000,
		// },
		// dismissable: {
		// 	type: Boolean,
		// 	default: true,
		// },
		// multiLine: {
		// 	type: Boolean,
		// 	default: false,
		// },
		// vertical: {
		// 	type: Boolean,
		// 	default: false,
		// },
		// showClose: {
		// 	type: Boolean,
		// 	default: false,
		// },
		// closeText: {
		// 	type: String,
		// 	default: "",
		// },
		// closeIcon: {
		// 	type: String,
		// 	default: "close",
		// },
		// closeColor: {
		// 	type: String,
		// 	default: "",
		// },
		// title: {
		// 	type: String,
		// 	default: "",
		// },
	},
	data: () => ({
		// active: false,
		// remainingTimeout: 0,
		objects: [],
	}),

	mounted() {
		// this.$nextTick(() => this.show());
	},
	methods: {
		// show() {
		// 	this.active = true;

		// 	if (this.timeout) {
		// 		const precision = 100;
		// 		this.remainingTimeout = this.timeout;
		// 		const int = setInterval(() => {
		// 			this.remainingTimeout -= precision;

		// 			if (this.remainingTimeout <= 0) {
		// 				clearInterval(int);
		// 				this.close();
		// 			}
		// 		}, precision);
		// 	}
		// },
		// close() {
		// 	this.active = false;
		// },
		// dismiss() {
		// 	if (this.dismissable) {
		// 		this.close();
		// 	}
		// },
		add(obj) {
			console.log("🚀 ~ file: Toast.vue ~ line 194 ~ add ~ obj", obj);

			this.objects.push(obj);
		},
	},
};
</script>

<style>
/*stylelint-disable*/
/* .vts {
	max-width: none !important;
	width: auto !important;
}
.vts .v-snack__content {
	justify-content: flex-start;
	padding-top: 0;
}
.vts__icon {
	margin-right: 12px;
}
.vts__message {
	margin-right: auto;
}
.vts__close {
	margin: 0 -8px 0 24px !important;
	justify-self: flex-end;
}
.vts.v-snack--vertical .vts__icon {
	margin: 0 0 12px !important;
}
.vts.v-snack--vertical .v-snack__content {
	padding-bottom: 16px !important;
}
.vts.v-snack--vertical .vts__message--padded {
	padding: 12px 0 0;
}
.vts.v-snack--vertical .vts__icon + .vts__message {
	padding-top: 0;
}
.vts.v-snack--vertical .vts__close {
	margin: 12px 0 -8px !important;
}
.vts.v-snack--vertical .vts__close--icon {
	margin: 0 !important;
	position: absolute;
	right: 4px;
	top: 4px;
	transform: scale(0.75);
	padding: 4px !important;
} */
</style>
