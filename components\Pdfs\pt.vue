<template>
	<div id="pt" style="background: #fff">
		<v-container class="pa-8" style="padding: 20px">
			<v-row class="mb-4">
				<v-col md="6">
					<v-img src="/logo.png" width="350px" style="width: 250px"></v-img>

					<div>
						JAFZA ONE, Tower B , 13th Floor, Suite BB1302 P.O Box 17046, JEBEL ALI, Dubai, UAE Tel: +971 (04) 268 4666
						<EMAIL>
					</div>
				</v-col>
				<v-col md="6">
					<div>
						<div class="text-center mb-4">
							<h1 class="font-weight-bold display-2">PICK TICKET</h1>
						</div>
						<div class="custom-table table">
							<table width="100%">
								<tr style="background: #ecebeb">
									<th>Sales Order #</th>
									<th>Order Date</th>
									<th>Customer</th>
								</tr>
								<tr>
									<td></td>
									<td>{{ pdfData.created_at | date }}</td>
									<td>{{ pdfData.quote?.client?.name }}</td>
								</tr>
								<tr style="background: #ecebeb; text-align: center">
									<td colspan="3">Purchase Order</td>
								</tr>
								<tr style="text-align: center">
									<td colspan="3">{{ pdfData.client_po_number }}</td>
								</tr>
							</table>
						</div>
					</div>
				</v-col>
			</v-row>

			<v-row class="relative">
				<v-col md="12" cols="12">
					<div>
						<div class="custom-table table">
							<table width="100%">
								<tr>
									<td colspan="12">
										<table style="width: 100%">
											<tr style="background: #ecebeb">
												<th style="width: 20%">Terms</th>
												<th style="width: 20%">Collect/Prepaid</th>
												<th style="width: 20%">Origin</th>
												<th style="width: 20%">Carrier</th>
											</tr>
											<tr>
												<td style="width: 20%">-</td>
												<td style="width: 20%">-</td>
												<td style="width: 20%">-</td>
												<td style="width: 20%">-</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr style="background: #ecebeb">
									<th>Bill To</th>
									<th>ship To</th>
								</tr>
								<tr>
									<td style="width: 50%">
										<div class="d-inline-block coffee-3--text body-2">{{ pdfData.quote?.client?.name }}</div>
										<div class="d-inline-block coffee-3--text body-2">{{ pdfData.quote?.client?.email }}</div>

										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-2">Attention of:</h5>
											<span class="d-inline-block coffee-3--text body-2">{{ pdfData.bill_to_data?.name }}</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-2">Phone:</h5>
											<span class="d-inline-block coffee-3--text body-2">{{ pdfData.bill_to_data?.phone }}</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-2">Mobile:</h5>
											<span class="d-inline-block coffee-3--text body-2">{{ pdfData.bill_to_data?.mobile }}</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-2">Fax:</h5>
											<span class="d-inline-block coffee-3--text body-2">{{ pdfData.bill_to_data?.fax }}</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-2">Email:</h5>
											<span class="d-inline-block coffee-3--text body-2">{{ pdfData.bill_to_data?.email }}</span>
										</div>
									</td>
									<td>
										<div class="d-inline-block coffee-3--text body-2">{{ pdfData.quote?.client?.name }}</div>
										<div class="d-inline-block coffee-3--text body-2">{{ pdfData.quote?.client?.email }}</div>

										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-2">Attention of:</h5>
											<span class="d-inline-block coffee-3--text body-2">{{ pdfData.ship_to_data?.name }}</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-2">Phone:</h5>
											<span class="d-inline-block coffee-3--text body-2">{{ pdfData.ship_to_data?.phone }}</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-2">Mobile:</h5>
											<span class="d-inline-block coffee-3--text body-2">{{ pdfData.ship_to_data?.mobile }}</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-2">Fax:</h5>
											<span class="d-inline-block coffee-3--text body-2">{{ pdfData.ship_to_data?.fax }}</span>
										</div>
										<div style="display: flex; align-items: center; margin-bottom: 2px">
											<h5 class="d-inline-block me-2">Email:</h5>
											<span class="d-inline-block coffee-3--text body-2">{{ pdfData.ship_to_data?.email }}</span>
										</div>
									</td>
								</tr>
								<tr style="background: #ecebeb">
									<th>Shipping Instructions</th>
									<th>Delivery Notes</th>
								</tr>
								<tr>
									<td>
										<div v-html="pdfData.quote?.packaging_details"></div>
									</td>
									<td><div v-html="pdfData.quote?.delivery_method_terms"></div></td>
								</tr>
							</table>
						</div>
					</div>
				</v-col>

				<v-col cols="12">
					<v-card outlined>
						<v-card-text>
							<v-simple-table class="border">
								<thead style="background: #232323">
									<tr>
										<th class="text-left" style="color: #fff">Qty Ordered</th>
										<th class="text-left" style="color: #fff">Description</th>
										<th class="text-left" style="color: #fff">UOM</th>
										<th class="text-left" style="color: #fff">Qty Picked</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="item in pdfData.quote.products" :key="item.id">
										<template v-if="!item.is_optional">
											<td>{{ item.quantity }}</td>
											<td>
												{{ item.description }}
											</td>

											<td>{{ item.uom }}</td>

											<td>-</td>
										</template>
									</tr>
								</tbody>
							</v-simple-table>
						</v-card-text>
					</v-card>
				</v-col>
			</v-row>
		</v-container>
	</div>
</template>

<script>
export default {
	props: {
		pdfData: {
			type: Object,
			default: () => ({}),
		},
	},
};
</script>

<style lang="scss">
.custom-table table,
.custom-table th,
.custom-table td {
	border: 1px solid #ecebeb;
	border-collapse: collapse;
}
.custom-table th,
.custom-table td {
	padding: 8px;
}
.custom-table {
	border: 1px solid #ecebeb;
	border-collapse: collapse;
}
.custom-table th,
.custom-table td {
	border: 1px solid #ecebeb;
	border-collapse: collapse;
}
</style>
