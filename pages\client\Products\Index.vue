<template>
	<page :tabs="tabs" title="Products">
		<template #actions>
			<div id="actions"></div>
		</template>
		<nuxt-child ref="child" />
	</page>
</template>

<script>
export default {
	data() {
		return {
			tabs: [
				{
					text: "Product categories",
					to: this.localePath({ name: "product-categories" }),
				},
				{
					text: "Product Sub Categories",
					to: this.localePath({ name: "product-sub-categories" }),
				},
				{
					text: "Products",
					to: this.localePath({ name: "products" }),
				},
				
			],
		};
	},
	methods: {
		newItem() {
			this.$refs.child.refs.crud.new();
		},
	},
};
</script>

