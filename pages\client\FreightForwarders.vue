<template>
	<page title="Freight Forwarders" desc="">
		<template #actions>
			<v-btn v-if="checkPermission('freight_forwarders.create')" color="success" @click="$refs.crud.new()">
				<v-icon left>mdi-plus</v-icon>Add New</v-btn
			>
		</template>
		<div>
			<data-table
				v-if="checkPermission('freight_forwarders.view')"
				ref="dataTable"
				:columns="columns"
				api="/v1/portal/freight-forwarders"
			>
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn
							v-if="checkPermission('freight_forwarders.delete')"
							v-tooltip="'Delete'"
							small
							icon
							@click="$refs.crud.delete(item.id)"
						>
							<v-icon small>mdi-delete</v-icon>
						</v-btn>
						<v-btn
							v-if="checkPermission('freight_forwarders.update')"
							v-tooltip="'Edit'"
							small
							icon
							@click="$refs.crud.edit(item.id)"
						>
							<v-icon small>mdi-pencil</v-icon>
						</v-btn>
					</div>
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>
			</data-table>
			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>

			<crud
				ref="crud"
				v-slot="{ item }"
				width="400"
				:default="defaultItem"
				item-name="Freight Forwarder"
				api="/v1/portal/freight-forwarders"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<field-text-field v-model="item.name" name="name" label="Name" :rules="[$rules.required('name')]"></field-text-field>

				<field-text-field v-model="item.email" name="email" label="Email" :rules="[$rules.required('email')]"></field-text-field>

				<field-text-field
					v-model="item.address"
					name="address"
					label="Address"
					:rules="[$rules.required('address')]"
				></field-text-field>

				<field-text-field v-model="item.phone" name="phone" label="Phone" :rules="[$rules.required('phone')]"></field-text-field>
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "Name",
					sortable: true,
					value: "name",
				},
				{
					text: "Email",
					sortable: true,
					value: "email",
				},
				{
					text: "Address",
					sortable: true,
					value: "address",
				},
				{
					text: "Phone",
					sortable: true,
					value: "phone",
				},

				{
					text: "Created at",
					sortable: true,
					value: "created_at",
				},

				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],
			defaultItem: {
				id: null,
				name: null,
			},
		};
	},
	computed: {},
	watch: {},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>

