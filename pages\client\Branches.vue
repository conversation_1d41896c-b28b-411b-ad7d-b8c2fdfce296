<template>
	<page title="Branches" desc="">
		<template #actions>
			<v-btn v-if="checkPermission('branches.create')" color="success" @click="$refs.crud.new()">
				<v-icon left>mdi-plus</v-icon>Add Branch
			</v-btn>
		</template>
		<div>
			<data-table v-if="checkPermission('branches.view')" ref="dataTable" :columns="columns" api="/v1/portal/branches">
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn
							v-if="checkPermission('branches.delete')"
							v-tooltip="'Delete'"
							small
							icon
							@click="$refs.crud.delete(item.id)"
						>
							<v-icon small>mdi-delete</v-icon>
						</v-btn>
						<v-btn v-if="checkPermission('branches.update')" v-tooltip="'Edit'" small icon @click="$refs.crud.edit(item.id)">
							<v-icon small>mdi-pencil</v-icon>
						</v-btn>
					</div>
				</template>

				<template #item.logo="{ item }">
					<thumbnail :src="item.logo" :preview-title="item.name" />
				</template>

				<template #item.contact_person="{ item }">
					{{ item.name }}
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>
			</data-table>
			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>

			<crud
				ref="crud"
				v-slot="{ item }"
				width="400"
				:default="defaultItem"
				item-name="Branch"
				api="/v1/portal/branches"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<v-text-field v-model="item.name" name="name" label="Branch Name" :rules="[$rules.required('branch name')]"></v-text-field>

				<vc-textarea
					v-model="item.address"
					name="address"
					label="Branch Address"
					:rules="[$rules.required('branch address')]"
				></vc-textarea>

				<v-text-field v-model="item.phone" name="phone" label="Phone" :rules="[$rules.required('phone')]"></v-text-field>

				<field-upload
					v-model="item.logo"
					:value="value"
					:types="['png', 'jpg', 'jpeg', 'webp', 'svg']"
					label="Logo"
					@click="handler"
					@input="update"
				></field-upload>

				<v-card outlined class="mt-6">
					<v-card-title>
						<dvi class="body-1 font-weight-bold"> Contact Person Details</dvi>
					</v-card-title>
					<v-card-text v-if="item.contact_person">
						<v-text-field
							v-model="item.contact_person.name"
							name="contact_person_name"
							label="Name"
							:rules="[$rules.required('Contact Person Name')]"
						></v-text-field>
						<v-text-field
							v-model="item.contact_person.email"
							name="contact_person_email"
							label="Email"
							:rules="[$rules.required('Contact Person Email')]"
						></v-text-field>
						<v-text-field
							v-model="item.contact_person.phone_one"
							name="contact_person_phone_one"
							label="Phone 1"
						></v-text-field>
						<v-text-field
							v-model="item.contact_person.phone_two"
							name="contact_person_phone_two"
							label="Phone 2"
						></v-text-field>
					</v-card-text>
				</v-card>
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "#",
					align: "start",
					sortable: false,
					value: "id",
				},
				{
					text: "Logo",
					sortable: true,
					value: "logo",
				},
				{
					text: "Branch Name",
					sortable: true,
					value: "name",
				},
				{
					text: "Address",
					sortable: true,
					value: "address",
				},
				{
					text: "Phone",
					sortable: true,
					value: "phone",
				},
				{
					text: "Contact Person",
					sortable: true,
					value: "contact_person",
				},
				{
					text: "Created at",
					sortable: true,
					value: "created_at",
				},
				{
					text: "Updated at",
					sortable: true,
					value: "updated_at",
				},
				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],
			defaultItem: {
				id: null,
				name: null,
				address: null,
				phone: null,
				logo: null,
				contact_person: {
					name: null,
					email: null,
					phone_one: null,
					phone_two: null,
				},
			},
		};
	},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>
