<template>
	<v-card>
		<v-card-title> Top Selling Products </v-card-title>
		<div id="chart">
			<apexchart :key="generateKey + 'chart'" type="donut" width="380" :options="chartOptions" :series="series"></apexchart>
		</div>
	</v-card>
</template>

<script>
import apexchart from "vue-apexcharts";

export default {
	components: {
		apexchart,
	},

	data() {
		return {
			generateKey: 0,
			topSellingProducts: {},
			series: [],
			chartOptions: {
				labels: [],
				colors: ["#2196F3", "#66DA26", "#546E7A", "#E91E63", "#FF9800"],
				legend: {
					position: "bottom", // Move labels under the chart
				},

				plotOptions: {
					pie: {
						expandOnClick: false,

						donut: {
							labels: {
								show: true,
								total: {
									show: true,
									label: "Total",
									formatter: () => this.totalSum, // Replace 145 with your dynamic total value
								},
							},
						},
					},
				},

				responsive: [
					{
						breakpoint: 480,
						options: {
							chart: {
								width: 300,
							},
							legend: {
								position: "bottom",
							},
						},
					},
				],
			},
		};
	},
	computed: {
		totalSum() {
			return this.series.reduce((acc, value) => acc + value, 0);
		},
	},
	created() {
		this.gitTopSellingChart();
	},

	methods: {
		gitTopSellingChart() {
			this.$axios.$get("/v1/portal/charts/top-selling-products").then((resp) => {
				this.topSellingProducts = resp;
				this.series = this.topSellingProducts.datasets[0].data;
				this.chartOptions.labels = this.topSellingProducts.labels;
				this.generateKey += 1;
			});
		},
	},
};
</script>



