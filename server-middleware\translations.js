// import dot from "dot-object";
// const fs = require("fs");
// const path = require("path");
const express = require("express");
const bodyParser = require("body-parser");

// const oldLocales = {
// 	en: JSON.parse(fs.readFileSync(path.join(__dirname, "../old-locale/en.json"))),
// 	ar: JSON.parse(fs.readFileSync(path.join(__dirname, "../old-locale/ar.json"))),
// 	fa: JSON.parse(fs.readFileSync(path.join(__dirname, "../old-locale/fa.json"))),
// 	vi: JSON.parse(fs.readFileSync(path.join(__dirname, "../old-locale/vi.json"))),
// 	th: JSON.parse(fs.readFileSync(path.join(__dirname, "../old-locale/th.json"))),
// };

// const oldLocales = require("../old-locale/index.js");

const app = express();
app.use(
	bodyParser.urlencoded({
		extended: true,
	})
);
app.use(express.json());

// app.get("/local/translations/old", (req, res) => {
// 	const result = {};
// 	for (const locale in oldLocales) {
// 		result[locale] = dot.dot(oldLocales[locale]);
// 	}
// 	return res.status(200).json(result);
// });

module.exports = app;
