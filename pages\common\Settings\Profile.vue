<template>
	<v-container>
		<v-skeleton-loader :loading="$fetchState.pending" type="card, paragraph">
			<v-row>
				<v-col cols="12" md="3" class="text-center">
					<avatar size="200" src="/images/default-avatar.jpg" />
				</v-col>
				<v-col cols="12" md="6">
					<h3 class="mb-4"><v-icon left>mdi-account-details</v-icon> Personal Details</h3>
					<v-text-field v-model="item.name" outline name="name" label="name" :rules="[$rules.required('name')]" />
					<v-text-field v-model="item.email" outline name="email" label="email" :rules="[$rules.required('email')]" />

					<h3 class="mb-4 mt-4">
						<v-icon left>mdi-store-cog-outline</v-icon> Permissions:
						<small class="text-uppercase success--text ms-1">{{ item.role.name }}</small>
					</h3>
				</v-col>
			</v-row>
		</v-skeleton-loader>
	</v-container>
</template>

<script>
// import VueJsonPretty from "vue-json-pretty";
import "vue-json-pretty/lib/styles.css";
export default {
	// components: {
	// 	VueJsonPretty,
	// },
	data() {
		return {
			item: {
				name: null,
				email: null,
				created_at: null,
				updated_at: null,
				role: {
					permissions_names: [],
					permissions: [],
				},
			},
		};
	},
	async fetch() {
		await this.$axios.$get("v1/my").then((resp) => {
			this.item = resp;
		});
	},
	methods: {
		editMyProfile() {
			this.$axios
				.$put(`/v1/portal/my/profile`, this.item)
				.then((resp) => {
					this.$fetch();
				})
				.catch(this.genericErrorHandler);
		},
	},
};
</script>

