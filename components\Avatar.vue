<template>
	<v-avatar v-bind="$attrs" left v-on="$listeners">
		<v-menu offset-y v-bind="menuProps">
			<template #activator="{ on, attrs }">
				<slot v-bind="{ on, attrs, item }">
					<v-img v-if="src" :src="imgURL" cover v-bind="attrs" :class="{ pointer: !!$scopedSlots.menu }" v-on="on"></v-img>

					<v-sheet
						v-else-if="nameInitials"
						class="text-upper purple d-flex align-center justify-center text-subtitle-1 font-weight-medium"
						width="100%"
						height="100%"
						dark
						>{{ nameInitials }}</v-sheet
					>

					<v-img
						v-else
						src="/images/default-avatar.jpg"
						v-bind="attrs"
						:class="{ pointer: !!$scopedSlots.menu }"
						v-on="on"
					></v-img>
				</slot>
			</template>
			<v-card><slot name="menu" v-bind="{ item }"></slot></v-card>
		</v-menu>
	</v-avatar>
</template>

<script>
export default {
	inheritAttrs: false,
	props: {
		src: {
			type: String,
			default: null,
		},
		item: {
			type: Object,
			default: () => {
				return {};
			},
		},
		menuProps: {
			type: Object,
			default: () => {
				return {};
			},
		},
	},
	computed: {
		imgURL() {
			return this.item.profile_pic || this.src;
		},
		nameInitials() {
			// take the first letter of each word in the name this.item?.first_name & this.item?.last_name
			return this.item?.first_name?.charAt(0) + this.item?.last_name?.charAt(0);
		},
	},
};
</script>
