<template>
	<div id="cin" style="background: #fff">
		<v-container class="pa-8" style="padding: 20px">
			<v-row>
				<v-col md="6">
					<v-img src="/logo.png" width="350px" style="width: 250px"></v-img>
					<div>
						<p>
							JAFZA ONE, Tower B , 13th Floor, Suite BB1302 P.O Box 17046, JEBEL ALI, Dubai, UAE Tel: +971 (04) 268 4666
							<EMAIL>
						</p>
					</div>
				</v-col>
				<v-col md="6">
					<div>
						<div class="text-center mb-4">
							<h1 class="font-weight-bold display-2">INVOICE</h1>
						</div>
						<div class="custom-table table">
							<table width="100%">
								<tr style="background: #ecebeb">
									<th>Invoice #</th>
									<th>Invoice Date</th>
									<th>Customer #</th>
								</tr>
								<tr>
									<td></td>
									<td></td>
									<td>{{ pdfData.quote?.client?.name }}</td>
								</tr>
								<tr style="background: #ecebeb; text-align: center">
									<td colspan="3">Purchase Order</td>
								</tr>
								<tr style="text-align: center">
									<td colspan="3">{{ pdfData.client_po_number }}</td>
								</tr>
							</table>
						</div>
					</div>
				</v-col>
			</v-row>

			<v-divider class="my-3" />

			<v-row class="relative">
				<v-col md="6" cols="12">
					<div>
						<div style="margin-bottom: 25px; margin-top: 5px">
							<h4 class="font-weight-bold" style="background: #ebebeb; padding-left: 3px; margin-right: 5px">BILL TO</h4>
							<div class="d-inline-block coffee-3--text body-2">{{ pdfData.quote?.client?.name }}</div>
							<div class="d-inline-block coffee-3--text body-2">{{ pdfData.quote?.client?.email }}</div>

							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Attention of:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.bill_to_data?.name }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Phone:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.bill_to_data?.phone }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Mobile:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.bill_to_data?.mobile }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Fax:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.bill_to_data?.fax }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Email:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.bill_to_data?.email }}</span>
							</div>
						</div>
					</div>
				</v-col>

				<v-col md="6" cols="12">
					<div>
						<div style="margin-bottom: 25px; margin-top: 5px">
							<h4 class="font-weight-bold" style="background: #ebebeb; padding-left: 3px; margin-right: 5px">SHIP TO</h4>

							<div class="d-inline-block coffee-3--text body-2">{{ pdfData.quote?.client?.name }}</div>
							<div class="d-inline-block coffee-3--text body-2">{{ pdfData.quote?.client?.email }}</div>

							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Attention of:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.ship_to_data?.name }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Phone:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.ship_to_data?.phone }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Mobile:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.ship_to_data?.mobile }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Fax:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.ship_to_data?.fax }}</span>
							</div>
							<div style="display: flex; align-items: center; margin-bottom: 2px">
								<h5 class="d-inline-block me-2">Email:</h5>
								<span class="d-inline-block coffee-3--text body-2">{{ pdfData.ship_to_data?.email }}</span>
							</div>
						</div>
					</div>
				</v-col>

				<v-col cols="12">
					<v-card outlined>
						<v-card-text>
							<v-simple-table class="border">
								<thead style="background: #232323">
									<tr>
										<th class="text-left" style="color: #fff">Part # / Description</th>
										<th class="text-left" style="color: #fff">Quantity</th>
										<th class="text-left" style="color: #fff">Shipped</th>
										<th class="text-left" style="color: #fff">UOM</th>
										<th class="text-left" style="color: #fff">Unit Price</th>
										<th class="text-left" style="color: #fff">Total</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="item in pdfData.quote.products" :key="item.id">
										<template v-if="!item.is_optional">
											<td>
												<div>
													{{ item.part_number }}
												</div>
												<div>
													{{ item.description }}
												</div>
											</td>

											<td>{{ item.quantity }}</td>
											<td>-</td>
											<td>{{ item.uom }}</td>

											<td>
												{{
													pdfData.quote?.client_currency?.symbol ? pdfData.quote?.client_currency?.symbol : "USD"
												}}
												{{ item.unit_price }}
											</td>
											<td>
												{{
													pdfData.quote?.client_currency?.symbol ? pdfData.quote?.client_currency?.symbol : "USD"
												}}
												{{ item.total }}
											</td>
										</template>
									</tr>
								</tbody>
							</v-simple-table>
						</v-card-text>
					</v-card>
				</v-col>
			</v-row>

			<v-row class="fill-height mt-0">
				<v-col cols="8"> </v-col>

				<v-spacer />

				<v-col md="3" cols="12" class="mt-0 pt-0">
					<v-card outlined color="#ecebeb" rounded="0">
						<v-card-text>
							<v-list-item dense>
								<v-list-item-content class="mb-0 pb-0">
									<v-list-item-title>Subtotal</v-list-item-title>
								</v-list-item-content>
								<v-list-item-content class="mb-0 pb-0 text-end pe-4">
									<v-list-item-title class="font-weight-bold">
										{{ pdfData.quote?.client_currency?.symbol ? pdfData.quote?.client_currency?.symbol : "USD" }}
										{{ pdfData.quote?.subtotal }}
									</v-list-item-title>
								</v-list-item-content>
							</v-list-item>
							<v-divider />

							<v-list-item v-if="pdfData.quote?.tax_label && pdfData.quote?.tax" dense>
								<v-list-item-content class="mb-0 pb-0">
									<v-list-item-title class="text-capitalize">{{ pdfData.quote?.tax_label }}</v-list-item-title>
								</v-list-item-content>
								<v-list-item-content class="mb-0 pb-0 text-end pe-4">
									<v-list-item-title class="font-weight-bold"> %{{ pdfData.quote?.tax }} </v-list-item-title>
								</v-list-item-content>
							</v-list-item>
							<v-divider v-if="pdfData.quote?.tax_label && pdfData.quote?.tax" />

							<v-list-item v-if="pdfData.quote?.discount" dense>
								<v-list-item-content class="mb-0 pb-0">
									<v-list-item-title>Special Discount</v-list-item-title>
								</v-list-item-content>
								<v-list-item-content class="mb-0 pb-0 text-end pe-4">
									<v-list-item-title class="font-weight-bold">%{{ pdfData.quote?.discount }}</v-list-item-title>
								</v-list-item-content>
							</v-list-item>
							<v-divider v-if="pdfData.quote?.discount" />
							<v-list-item dense>
								<v-list-item-content class="mb-0 pb-0">
									<v-list-item-title>Total</v-list-item-title>
								</v-list-item-content>
								<v-list-item-content class="mb-0 pb-0 text-end pe-4">
									<v-list-item-title class="font-weight-bold">
										{{ pdfData.quote?.client_currency?.symbol ? pdfData.quote?.client_currency?.symbol : "USD" }}
										{{ pdfData.quote?.total }}
									</v-list-item-title>
								</v-list-item-content>
							</v-list-item>
						</v-card-text>
					</v-card>
					<p class="mt-3 font-weight-bold">{{ pdfData.quote?.total_spell }}</p>
				</v-col>
			</v-row>

			<div class="d-flex">
				<h4 class="me-3">Signature</h4>
				<span>_____________________________</span>
			</div>
		</v-container>
	</div>
</template>

<script>
export default {
	props: {
		pdfData: {
			type: Object,
			default: () => ({}),
		},
	},
};
</script>




