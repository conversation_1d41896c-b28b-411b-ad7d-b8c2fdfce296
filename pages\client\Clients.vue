  <template>
	<page title="Clients" desc="">
		<template #actions>
			<v-btn v-if="checkPermission('clients.create')" color="success" @click="$refs.crud.new()">
				<v-icon left>mdi-plus</v-icon>Add Client
			</v-btn>
		</template>
		<div>
			<data-table v-if="checkPermission('clients.view')" ref="dataTable" :columns="columns" api="/v1/portal/clients">
				<template #item.actions="{ item }">
					<div class="d-flex">
						<v-btn v-if="checkPermission('clients.delete')" v-tooltip="'Delete'" small icon @click="$refs.crud.delete(item.id)">
							<v-icon small>mdi-delete</v-icon>
						</v-btn>
						<v-btn v-if="checkPermission('clients.update')" v-tooltip="'Edit'" small icon @click="$refs.crud.edit(item.id)">
							<v-icon small>mdi-pencil</v-icon>
						</v-btn>
						<v-btn v-tooltip="'Show'" icon small :to="localePath({ name: 'client-profile', params: { id: item.id } })">
							<v-icon small>mdi-eye</v-icon>
						</v-btn>
					</div>
				</template>

				<template #item.created_at="{ item }">
					{{ item.created_at | dateTime }}
				</template>

				<template #item.updated_at="{ item }">
					{{ item.updated_at | dateTime }}
				</template>

				<template #item.contact_people="{ item }">
					<template v-if="item.contact_people && item.contact_people.length">
						<div v-for="contact in item.contact_people" :key="contact.id">
							<span>{{ contact.name }}</span>
						</div>
					</template>
				</template>

				<template #item.phone="{ item }">
					<a :href="'tel:' + item.phone">{{ item.phone }}</a>
				</template>
			</data-table>
			<div v-else class="pa-5">
				<v-icon left>mdi-alert-circle</v-icon>
				<span class="pt-2">Sorry, you do not have permission!</span>
			</div>

			<crud
				ref="crud"
				v-slot="{ item, isEdit }"
				width="450"
				:default="defaultItem"
				item-name="Client"
				api="/v1/portal/clients?ids=country"
				@updated="refresh"
				@created="refresh"
				@deleted="refresh"
			>
				<div v-if="isEdit">
					<v-text-field v-model="item.reference" name="reference" label="Reference Number" disabled></v-text-field>
				</div>

				<v-text-field v-model="item.name" name="name" label="Name" :rules="[$rules.required('name')]"></v-text-field>

				<v-text-field
					v-model="item.email"
					name="email"
					label="Email"
					:rules="[$rules.required('email'), $rules.email('email')]"
				></v-text-field>

				<vc-autocomplete
					v-model="item.country_id"
					name="country_id"
					api="/v1/lookups/countries"
					item-text="text"
					item-value="value"
					label="Country"
					class="mb-6"
					:rules="[$rules.required('country')]"
				></vc-autocomplete>

				<h4 class="mb-2">Contact People</h4>

				<v-expansion-panels class="mb-2">
					<v-expansion-panel v-for="(field, i) in item.contact_people" :key="i + 'field'">
						<v-expansion-panel-header>
							<span> {{ field.name }} </span>
							<div class="text-end">
								<v-btn class="me-2" small icon color="primary" @click="item.contact_people.splice(i, 1)">
									<v-icon>mdi-trash-can</v-icon>
								</v-btn>
							</div>
						</v-expansion-panel-header>
						<v-expansion-panel-content>
							<v-text-field :key="i + 'name'" v-model="field.name" dense label="Contact Name"> </v-text-field>
							<v-text-field
								:key="i + 'number'"
								v-model="field.number"
								dense
								name="number"
								label="Contact Number"
							></v-text-field>
							<v-text-field :key="i + 'email_one'" v-model="field.email_one" dense label="Email One"> </v-text-field>
							<v-text-field :key="i + 'email_two'" v-model="field.email_two" dense label="Email Two"> </v-text-field>
							<v-text-field :key="i + 'phone'" v-model="field.phone" dense label="Phone"> </v-text-field>
							<v-text-field :key="i + 'mobile'" v-model="field.mobile" dense label="Mobile"> </v-text-field>
							<v-text-field :key="i + 'fax'" v-model="field.fax" dense label="Fax"> </v-text-field>
							<v-text-field :key="i + 'position'" v-model="field.position" dense label="position"> </v-text-field>
							<v-text-field :key="i + 'address_one'" v-model="field.address_one" dense label="Address One"> </v-text-field>
							<v-text-field :key="i + 'address_two'" v-model="field.address_two" dense label="Address Two"> </v-text-field>
						</v-expansion-panel-content>
					</v-expansion-panel>
				</v-expansion-panels>

				<v-btn
					v-tooltip="'Add New Contact People'"
					class="float-end mt-4"
					small
					color="success"
					@click="
						item.contact_people.push({
							name: null,
							email_one: null,
							email_two: null,
							phone: null,
							mobile: null,
							fax: null,
							position: null,
							address_one: null,
							address_two: null,
						})
					"
				>
					Add Contact People
				</v-btn>
			</crud>
		</div>
	</page>
</template>

<script>
export default {
	data() {
		return {
			columns: [
				{
					text: "Reference Number",
					align: "start",
					sortable: false,
					value: "reference",
				},
				{
					text: "Client Name",
					sortable: true,
					value: "name",
				},
				{
					text: "Contact People",
					sortable: true,
					value: "contact_people",
				},
				{
					text: "Client Email",
					sortable: true,
					value: "email",
				},

				{
					text: "Created at",
					sortable: true,
					value: "created_at",
				},
				{
					text: "Updated at",
					sortable: true,
					value: "updated_at",
				},
				{
					text: "",
					sortable: true,
					value: "actions",
				},
			],
			defaultItem: {
				id: null,
				name: null,
				email: null,
				country: {},
				country_id: null,

				contact_people: [
					// {
					// 	name: null,
					// 	email_one: null,
					// 	email_two: null,
					// 	phone: null,
					// 	mobile: null,
					// 	fax: null,
					// 	position: null,
					// 	address_one: null,
					// 	address_two: null,
					// },
				],
			},
		};
	},
	computed: {},
	watch: {},

	methods: {
		refresh() {
			this.$refs.dataTable.refresh();
		},
	},
};
</script>
