<template>
	<!-- <auth-window-controller ref="windowController"> -->
	<v-form ref="login" @submit.prevent="submit">
		<!-- <v-card-title class="text-h5 relative justify-center">
				<div>Login to Portal</div>
			</v-card-title> -->
		<v-card-text>
			<v-window v-model="loginTypeModel" class="overflow-visible">
				<v-window-item v-click-outside="{ handler: historyItemOutsideClickHandler }" value="history">
					<v-list-item
						v-for="(login, l) in loginHistory"
						:key="'login-' + l"
						v-ripple="userSelected !== login.email"
						:three-line="userSelected === login.email"
						link
						class="all-transition"
						color="primary"
						:input-value="userSelected === login.email"
						@click.stop="userSelected = login.email"
					>
						<v-list-item-avatar>
							<v-img v-if="login.avatar" :src="login.avatar"></v-img>
							<v-img v-else src="/images/default-avatar.jpg"></v-img>
						</v-list-item-avatar>
						<v-list-item-content>
							<v-list-item-title>{{ login.firstName }}</v-list-item-title>
							<v-list-item-subtitle>{{ login.email }}</v-list-item-subtitle>
							<div v-if="userSelected === login.email" class="mt-2">
								<v-text-field
									:append-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
									:type="showPassword ? 'text' : 'password'"
									outlined
									validate-on-blur
									label="Password"
									dense
									:rules="[$rules.required('password'), $rules.minLength('password', 8)]"
									@click:append="showPassword = !showPassword"
								>
								</v-text-field>
							</div>
						</v-list-item-content>
						<v-list-item-action class="align-center">
							<v-menu offset-y>
								<template #activator="{ on }">
									<v-btn icon v-on="on" @click.stop=""> <v-icon>mdi-dots-vertical</v-icon></v-btn>
								</template>
								<v-card>
									<v-list-item link @click="deleteHistoryItem(l)">
										<v-list-item-icon><v-icon>mdi-delete</v-icon></v-list-item-icon>
										<v-list-item-title>Forget This Account</v-list-item-title>
									</v-list-item>
								</v-card>
							</v-menu>
							<v-btn v-if="userSelected === login.email" class="mt-2" icon type="submit" :loading="isLoggingIn"
								><v-icon>mdi-chevron-right</v-icon></v-btn
							>
						</v-list-item-action>
					</v-list-item>
				</v-window-item>
				<v-window-item value="new">
					<v-text-field
						v-model="form.email"
						outlined
						name="email"
						label="Email"
						prepend-inner-icon="mdi-email"
						:rules="[$rules.required('email'), $rules.email('email')]"
					></v-text-field>
					<v-text-field
						v-model="form.password"
						name="password"
						:append-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
						:type="showPassword ? 'text' : 'password'"
						outlined
						validate-on-blur
						:rules="[$rules.required('password'), $rules.minLength('password', 8)]"
						label="Password"
						prepend-inner-icon="mdi-lock"
						persistent-hint
						counter
						@click:append="showPassword = !showPassword"
					>
						<!-- <template #counter>
								<v-btn class="mt-2" plain small @click="loginTypeModel = 'forgot-password'">Forgot your password?</v-btn>
							</template> -->
					</v-text-field>
				</v-window-item>
				<v-window-item value="forgot-password">
					<v-text-field
						v-model="form.email"
						outlined
						label="Email"
						prepend-inner-icon="mdi-email"
						:rules="[$rules.required('email'), $rules.email('email')]"
					></v-text-field>

					<v-btn plain small @click="loginTypeModel = 'new'">Remembered your password?</v-btn>
				</v-window-item>
			</v-window>
		</v-card-text>

		<v-card-actions class="flex-column px-4 pb-4">
			<v-btn v-show="loginTypeModel === 'new'" :loading="isLoggingIn" type="submit" large block color="success">Continue</v-btn>
			<v-btn v-show="loginTypeModel === 'forgot-password'" block large color="primary" @click="loginTypeModel = 'new'">
				Reset My Password</v-btn
			>
			<v-btn v-show="loginTypeModel === 'history'" text large color="primary" @click="loginTypeModel = 'new'">
				<v-icon left>mdi-plus</v-icon> Login With Different Account</v-btn
			>
		</v-card-actions>
	</v-form>
	<!-- </auth-window-controller> -->
</template>

<script>
import authMixin from "~/mixins/auth";
export default {
	mixins: [authMixin],
	layout: "auth",
	middleware: "notLoggedIn",
	data() {
		return {
			showPassword: false,
			isLoggingIn: false,
			loginTypeModel: "new",
			userSelected: false,
			form: {
				email: this.$config.defaultUser,
				password: this.$config.defaultPassword,
			},
			socialMediaAuth: [
				{ name: "google", src: "/images/social-login/google.png", isLoding: false },
				{ name: "facebook", src: "/images/social-login/facebook.png", isLoading: false },
				{ name: "microsoft", src: "/images/social-login/microsoft.png", isLoading: false },
				{ name: "linkedin", src: "/images/social-login/linkedin.png", isLoading: false },
			],
		};
	},
	computed: {
		loginHistory() {
			return this.storage.loginHistory;
		},
	},
	watch: {
		"storage.loginHistory.length": {
			handler(val) {
				if (this.loginTypeModel === "history" && !val) {
					this.loginTypeModel = "new";
				}
			},
		},
	},
	created() {
		if (this.storage.loginHistory.length) {
			this.loginTypeModel = "history";
		}
	},
	methods: {
		loginWithMedia(media) {
			media.isLoading = true;
			this.submit().finally(() => {
				media.isLoading = false;
			});
		},
		submit() {
			if (!this.$refs.login.validate()) return false;
			this.isLoggingIn = true;
			try {
				this.$store
					.dispatch("authExtend/login", this.form)
					.then(() => {
						console.log("***** redirect ******");
						this.$router.push(this.localePath({ name: "index" }));
						// this.$router.push("/");

						// location.reload()
					})
					.catch((e) => {
						this.genericErrorHandler(e, this.$refs.login);
					})
					.finally(() => {
						this.isLoggingIn = false;
					});
			} catch (error) {
				console.log(error);
			}
		},
		historyItemOutsideClickHandler(e) {
			this.userSelected = false;
			return true;
		},
		deleteHistoryItem(item) {
			this.storage.loginHistory.splice(this.storage.loginHistory.indexOf(item), 1);
		},
	},
};
</script>

