<template>
	<v-chip :input-value="true" outlined :color="color" small> <v-icon :color="color" small left v-text="icon"></v-icon>{{ text }} </v-chip>
</template>

<script>
export default {
	props: {
		items: {
			type: Array,
			default: () => [],
		},
		left: {
			type: Boolean,
			default: false,
		},
		right: {
			type: Boolean,
			default: false,
		},
		value: {
			type: [Number, String, Boolean],
			default: null,
		},
	},

	computed: {
		item() {
			return this.items.find((item) => {
				return String(this.value) === String(item.value);
			});
		},
		text() {
			return this.item?.text || this.$t(this.item?.key);
		},
		color() {
			return this.item?.color;
		},
		icon() {
			return this.item?.icon;
		},
	},
};
</script>
